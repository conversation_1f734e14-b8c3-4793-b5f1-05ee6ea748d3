"""
Enhanced Shitpost Generator Service
Generează videoclipuri shitpost cu AI image generation și cultură românească avansată
"""

import os
import random
import logging
import tempfile
import asyncio
import numpy as np
from typing import Dict, List, Optional, Tuple
from moviepy.editor import (
    VideoFileClip, TextClip, CompositeVideoClip, ColorClip,
    concatenate_videoclips, AudioFileClip, ImageClip
)
import colorsys
from io import BytesIO
from PIL import Image

# Import AI-powered modules
try:
    from .ai_image_generator import AIImageGenerator, GenerationConfig
    from .romanian_meme_culture import RomanianMemeGenerator
    from .meme_templates import MemeTemplateEngine
    AI_FEATURES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"AI features not available: {e}")
    AI_FEATURES_AVAILABLE = False

logger = logging.getLogger(__name__)

class ShitpostGenerator:
    """Enhanced Generator de videoclipuri shitpost cu AI și cultură românească"""

    def __init__(self):
        # Original components
        self.text_templates = self._load_text_templates()
        self.word_banks = self._load_word_banks()
        self.chaos_effects = self._load_chaos_effects()

        # AI-powered components
        if AI_FEATURES_AVAILABLE:
            self.ai_image_generator = AIImageGenerator()
            self.romanian_meme_generator = RomanianMemeGenerator()
            self.meme_template_engine = MemeTemplateEngine()
            logger.info("AI-powered shitpost features initialized")
        else:
            self.ai_image_generator = None
            self.romanian_meme_generator = None
            self.meme_template_engine = None
            logger.info("Using basic shitpost features only")

        # Enhanced themes with AI support
        self.enhanced_themes = {
            'romanian': {
                'ai_prompts': [
                    "romanian guy confused pointing", "balkan meme energy",
                    "eastern european absurdist humor", "romanian cultural reference"
                ],
                'use_ai': True,
                'template_preference': ['romanian_confused', 'classic_meme']
            },
            'chaos': {
                'ai_prompts': [
                    "chaotic energy fever dream", "surreal absurd nightmare",
                    "deep fried meme aesthetic", "incomprehensible chaos"
                ],
                'use_ai': True,
                'template_preference': ['text_only', 'classic_meme']
            },
            'gaming': {
                'ai_prompts': [
                    "gaming meme culture", "gamer rage moment",
                    "video game reference humor", "esports meme"
                ],
                'use_ai': True,
                'template_preference': ['expanding_brain', 'classic_meme']
            },
            'philosophical': {
                'ai_prompts': [
                    "big brain galaxy brain meme", "intellectual absurdist",
                    "philosophical deep thought", "existential crisis meme"
                ],
                'use_ai': True,
                'template_preference': ['expanding_brain', 'text_only']
            }
        }
        
    def _load_text_templates(self) -> Dict[str, List[str]]:
        """Template-uri pentru generarea textului absurd"""
        return {
            'romanian': [
                "Când {subiect} dar {actiune} și {rezultat}",
                "POV: {situatie} și {reactie}",
                "Nimeni:\nAbsolut nimeni:\n{personaj}: {actiune_absurda}",
                "Eu: {gand_normal}\nCreierul meu la 3 dimineața: {gand_absurd}",
                "Bunica: {sfat_bunica}\nEu: {reactie_nepot}",
                "Profesorul: {intrebare_scoala}\nEu: {raspuns_absurd}"
            ],
            'philosophical': [
                "Dacă {obiect1} este {proprietate}, atunci {obiect2} este {proprietate_absurda}",
                "Gândește-te: {intrebare_profunda} dar {raspuns_absurd}",
                "Societatea când {actiune_normala} vs când {actiune_absurda}",
                "Știința: {fapt_stiintific}\nEu: {interpretare_gresita}"
            ],
            'gaming': [
                "Când {joc} dar {bug} și {ragequit}",
                "Speedrunners be like: {actiune_rapida}",
                "Tutorial: {pas_simplu}\nRealitatea: {pas_complicat}",
                "Noob: {greseala_noob}\nPro: {reactie_pro}"
            ],
            'random_chaos': [
                "{obiect_random} + {actiune_random} = {rezultat_haotic}",
                "Breaking news: {stire_absurda}",
                "Matematica: {ecuatie_absurda}",
                "Când {conditie_imposibila} atunci {consecinta_haotică}"
            ]
        }
    
    def _load_word_banks(self) -> Dict[str, List[str]]:
        """Bănci de cuvinte pentru generarea textului"""
        return {
            'subiect': [
                'covrigul', 'pisica', 'bunica', 'profesorul', 'vecinul', 
                'mâncarea', 'telefonul', 'internetul', 'vremea', 'liftul'
            ],
            'actiune': [
                'dansează', 'plânge', 'zboara', 'cântă opera', 'face matematică',
                'vorbește cu peștii', 'inventează roata', 'citește gândurile'
            ],
            'rezultat': [
                'devine filosof', 'inventează timpul', 'găsește sensul vieții',
                'se transformă în robot', 'descoperă America', 'învață să zboare'
            ],
            'situatie': [
                'îți cade telefonul', 'uiți parola', 'te trezești la 6',
                'rămâi fără internet', 'se strică liftul', 'plouă în casă'
            ],
            'reactie': [
                'devii Shrek', 'inventezi roata', 'pleci în spațiu',
                'te faci pirat', 'începi să vorbești cu plantele', 'devii ninja'
            ],
            'personaj': [
                'Bunica', 'Profesorul de mate', 'Vecinul de la 4',
                'Pisica', 'Alexa', 'GPS-ul', 'Frigiderul'
            ],
            'actiune_absurda': [
                'Să mănânci supa cu furculița',
                'Să dormi cu ochii deschiși',
                'Să mergi înapoi în timp pentru lapte',
                'Să vorbești cu peștii aurii'
            ],
            'gand_normal': [
                'O să mă culc devreme',
                'O să învăț pentru examen',
                'O să fac sport',
                'O să mănânc sănătos'
            ],
            'gand_absurd': [
                'Dacă merg înapoi în timp pot să-mi fac temele',
                'Poate pisicile sunt de fapt extratereștri',
                'Ce se întâmplă dacă deschid frigiderul în spațiu?',
                'De ce nu pot să zbor dacă cred tare în mine?'
            ],
            'obiect_random': [
                'Covrigul cosmic', 'Pisica quantică', 'Telefonul filozofic',
                'Frigiderul temporal', 'Pantofii magici', 'Cafeaua interdimensională'
            ],
            'actiune_random': [
                'dansează breakdance', 'citește Matrix-ul', 'vorbește cu timpul',
                'face yoga în 4D', 'cântă opera în spațiu', 'inventează gravitația'
            ],
            'rezultat_haotic': [
                'Universul se resetează', 'Timpul merge înapoi',
                'Pisicile preiau controlul', 'Matematica devine ilegală',
                'Toată lumea devine ninja', 'Gravitația se oprește'
            ]
        }
    
    def _load_chaos_effects(self) -> List[str]:
        """Lista efectelor haotice disponibile"""
        return [
            'random_zoom_chaos',
            'color_distortion_extreme',
            'rotation_madness',
            'speed_chaos_random',
            'shake_earthquake',
            'color_invert_flash',
            'size_chaos_random'
        ]

    async def generate_ai_powered_shitpost(self, theme: str = "romanian", duration: int = 10,
                                         chaos_level: int = 5, custom_text: str = None,
                                         use_ai_images: bool = True, template_name: str = None,
                                         ai_only_mode: bool = False) -> Dict:
        """
        Generează shitpost cu AI image generation și cultură românească avansată

        Args:
            theme: Tema shitpost-ului
            duration: Durata în secunde
            chaos_level: Nivelul de chaos (1-10)
            custom_text: Text personalizat (opțional)
            use_ai_images: Folosește AI pentru generarea imaginilor
            template_name: Template specific de meme (opțional)
            ai_only_mode: Folosește doar AI pentru întregul video (imagini + text)

        Returns:
            Dict cu informații despre shitpost-ul generat
        """
        logger.info(f"🤖 Generez AI-powered shitpost {theme} cu chaos level {chaos_level}")

        # Check if AI-only mode is requested
        if ai_only_mode:
            logger.info("🎬 Using AI-only mode for complete video generation")
            return await self._generate_ai_only_video(theme, duration, chaos_level, custom_text)

        try:
            # Generează textul cu AI românesc
            if self.romanian_meme_generator and not custom_text:
                text = self.romanian_meme_generator.generate_caption(
                    context="", chaos_level=chaos_level, theme=theme
                )
            elif custom_text:
                text = custom_text
            else:
                text = self._generate_absurd_text(theme)

            # Generează imaginea de fundal cu AI
            background_image = None
            if use_ai_images and self.ai_image_generator:
                theme_config = self.enhanced_themes.get(theme, {})
                ai_prompts = theme_config.get('ai_prompts', [f"{theme} meme"])
                prompt = random.choice(ai_prompts)

                config = GenerationConfig(
                    width=720, height=720,  # Square for better meme format
                    style=theme
                )

                background_image = await self.ai_image_generator.generate_meme_image(
                    prompt, style=theme, chaos_level=chaos_level, config=config
                )

                if background_image:
                    # Apply meme effects to AI image
                    background_image = self.ai_image_generator.apply_meme_effects(
                        background_image, chaos_level
                    )

            # Creează videoclipul folosind AI image sau template
            if background_image and self.meme_template_engine:
                # Use meme template with AI background
                template_name = template_name or self._select_best_template(theme)

                # Split text for template positions
                text_parts = self._split_text_for_template(text, template_name)

                # Apply template
                meme_image = self.meme_template_engine.apply_template(
                    template_name, text_parts, background_image, (720, 720)
                )

                # Convert to video
                base_video = self._create_video_from_image(meme_image, duration)
            elif background_image:
                # Use AI image directly
                base_video = self._create_video_from_image(background_image, duration)
            else:
                # Fallback to original method
                base_video = self._create_base_video(duration)

            # Aplică efectele haotice
            chaotic_video = self._apply_chaos_effects(base_video, chaos_level)

            # Asigură-te că FPS-ul este setat
            if not hasattr(chaotic_video, 'fps') or chaotic_video.fps is None:
                chaotic_video = chaotic_video.set_fps(30)

            # Adaugă text overlay dacă nu s-a folosit template
            if not (background_image and self.meme_template_engine):
                final_video = self._add_chaotic_text(chaotic_video, text, theme)
            else:
                final_video = chaotic_video

            # Asigură-te că FPS-ul final este setat
            if not hasattr(final_video, 'fps') or final_video.fps is None:
                final_video = final_video.set_fps(30)

            # Generează calea de output
            output_path = self._generate_output_path(theme, "ai_powered")

            # Asigură-te că directorul există
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Salvează videoclipul
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                fps=30,
                verbose=False,
                logger=None
            )

            # Cleanup
            base_video.close()
            chaotic_video.close()
            final_video.close()

            result = {
                'success': True,
                'output_path': output_path,
                'text_generated': text,
                'theme': theme,
                'chaos_level': chaos_level,
                'duration': duration,
                'ai_powered': True,
                'used_ai_image': background_image is not None,
                'template_used': template_name if background_image and self.meme_template_engine else None,
                'file_size': os.path.getsize(output_path) if os.path.exists(output_path) else 0
            }

            logger.info(f"✅ AI-powered shitpost generat cu succes: {output_path}")
            return result

        except Exception as e:
            logger.error(f"❌ Eroare în generarea AI shitpost: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'output_path': None,
                'ai_powered': True
            }

    async def _generate_ai_only_video(self, theme: str, duration: int, chaos_level: int, custom_text: str = None) -> Dict:
        """Generate complete video using only AI-generated content"""
        try:
            # Import AI services
            from .ai_sequence_generator import ai_sequence_generator, AISequenceConfig
            from .ai_video_compositor import AIVideoCompositor

            # Create compositor instance
            compositor = AIVideoCompositor()
            import time

            logger.info(f"🎬 Starting AI-only video generation: {theme}, {duration}s, chaos {chaos_level}")

            # Step 1: Generate AI text content
            if custom_text:
                text = custom_text
            elif hasattr(self, 'gpt4free_service') and self.gpt4free_service.is_available():
                logger.info("📝 Generating text with GPT4Free...")
                text = await self.gpt4free_service.generate_shitpost_content(
                    theme=theme, chaos_level=chaos_level, style="absurd"
                )
                if not text:
                    text = self._generate_absurd_text(theme)
            else:
                text = self._generate_absurd_text(theme)

            logger.info(f"📝 Generated text: {text[:50]}...")

            # Step 2: Configure AI sequence generation
            config = AISequenceConfig(
                duration=float(duration),
                images_per_second=0.4,  # 1 image every 2.5 seconds
                width=720,
                height=1280,  # Vertical format for social media
                chaos_level=chaos_level,
                theme=theme,
                style="meme"
            )

            logger.info(f"🎨 Generating {config.total_images} AI images for sequence...")

            # Step 3: Generate AI image sequence
            segments = await ai_sequence_generator.generate_sequence(config, text)

            if not segments:
                raise Exception("Failed to generate AI image sequence")

            # Step 4: Create text overlays
            text_overlays = self._create_text_overlays_for_ai_video(text, duration, chaos_level)

            # Step 5: Compose final video
            logger.info("🎬 Composing final AI video...")
            result = await compositor.create_video_from_sequence(
                segments=segments,
                config=config,
                text_overlays=text_overlays
            )

            if not result.get('success'):
                raise Exception(f"Video composition failed: {result.get('error')}")

            # Step 6: Create metadata
            sequence_metadata = ai_sequence_generator.create_sequence_metadata(segments, config)

            # Cleanup temporary files
            compositor.cleanup_temp_files()

            logger.info(f"✅ AI-only video generated successfully: {result['output_path']}")

            return {
                'success': True,
                'output_path': result['output_path'],
                'text_generated': text,
                'theme': theme,
                'chaos_level': chaos_level,
                'duration': duration,
                'ai_powered': True,
                'ai_only_mode': True,
                'used_ai_image': True,
                'file_size': result.get('file_size', 0),
                'ai_sequence_metadata': sequence_metadata,
                'segments_generated': len(segments),
                'segments_successful': len([s for s in segments if s.image_data])
            }

        except Exception as e:
            logger.error(f"❌ Error in AI-only video generation: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'output_path': None,
                'ai_powered': True,
                'ai_only_mode': True
            }

    def _create_text_overlays_for_ai_video(self, text: str, duration: int, chaos_level: int) -> List[Dict]:
        """Create text overlays optimized for AI-generated video"""
        overlays = []

        # Split text into parts for better visual impact
        text_parts = self._split_text_for_overlays(text)

        if len(text_parts) == 1:
            # Single text overlay for the entire duration
            overlays.append({
                "text": text_parts[0],
                "start_time": 0,
                "duration": duration,
                "position": "center",
                "fontsize": 60 if chaos_level >= 7 else 50,
                "color": "white",
                "stroke_color": "black",
                "stroke_width": 3 if chaos_level >= 6 else 2
            })
        else:
            # Multiple text overlays with timing
            overlay_duration = duration / len(text_parts)

            for i, part in enumerate(text_parts):
                start_time = i * overlay_duration

                # Vary position based on chaos level
                positions = ["top", "center", "bottom"]
                if chaos_level >= 8:
                    positions.extend(["top_left", "top_right", "bottom_left", "bottom_right"])

                position = positions[i % len(positions)]

                overlays.append({
                    "text": part,
                    "start_time": start_time,
                    "duration": overlay_duration + 0.5,  # Slight overlap
                    "position": position,
                    "fontsize": 70 if chaos_level >= 8 else 55,
                    "color": "white",
                    "stroke_color": "black",
                    "stroke_width": 4 if chaos_level >= 7 else 3
                })

        return overlays

    def _split_text_for_overlays(self, text: str) -> List[str]:
        """Split text into parts suitable for video overlays"""
        # Split by common delimiters
        if "\\n" in text:
            parts = text.split("\\n")
        elif ":" in text and len(text.split(":")) <= 3:
            parts = text.split(":")
        elif len(text) > 100:
            # Split long text into chunks
            words = text.split()
            mid = len(words) // 2
            parts = [" ".join(words[:mid]), " ".join(words[mid:])]
        else:
            parts = [text]

        # Clean and filter parts
        parts = [part.strip() for part in parts if part.strip()]

        # Limit to maximum 4 parts for video clarity
        return parts[:4]

    def generate_random_shitpost(self, theme: str = "random", duration: int = 15,
                               chaos_level: int = 7, custom_text: str = None) -> Dict:
        """
        Generează un shitpost complet random
        
        Args:
            theme: Tema shitpost-ului
            duration: Durata în secunde
            chaos_level: Nivelul de chaos (1-10)
            custom_text: Text personalizat (opțional)
            
        Returns:
            Dict cu informații despre shitpost-ul generat
        """
        logger.info(f"😂 Generez shitpost {theme} cu chaos level {chaos_level}")
        
        try:
            # Generează textul
            if custom_text:
                text = custom_text
            else:
                text = self._generate_absurd_text(theme)
            
            # Creează videoclipul de bază
            base_video = self._create_base_video(duration)
            
            # Aplică efectele haotice
            chaotic_video = self._apply_chaos_effects(base_video, chaos_level)

            # Asigură-te că FPS-ul este setat după efecte
            if not hasattr(chaotic_video, 'fps') or chaotic_video.fps is None:
                chaotic_video = chaotic_video.set_fps(30)

            # Adaugă textul
            final_video = self._add_chaotic_text(chaotic_video, text, theme)

            # Asigură-te că FPS-ul final este setat
            if not hasattr(final_video, 'fps') or final_video.fps is None:
                final_video = final_video.set_fps(30)
            
            # Generează calea de output
            output_path = self._generate_output_path(theme, "shitpost")
            
            # Asigură-te că directorul există
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Salvează videoclipul
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                fps=30,
                verbose=False,
                logger=None
            )
            
            # Cleanup
            base_video.close()
            chaotic_video.close()
            final_video.close()
            
            result = {
                'success': True,
                'output_path': output_path,
                'text_generated': text,
                'theme': theme,
                'chaos_level': chaos_level,
                'duration': duration,
                'file_size': os.path.getsize(output_path) if os.path.exists(output_path) else 0
            }
            
            logger.info(f"✅ Shitpost generat cu succes: {output_path}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Eroare în generarea shitpost: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'output_path': None
            }
    
    def _generate_absurd_text(self, theme: str) -> str:
        """Generează text absurd bazat pe temă"""
        templates = self.text_templates.get(theme, self.text_templates['random_chaos'])
        template = random.choice(templates)
        
        # Înlocuiește placeholder-urile cu cuvinte random
        for key, values in self.word_banks.items():
            placeholder = f'{{{key}}}'
            if placeholder in template:
                template = template.replace(placeholder, random.choice(values))
        
        return template

    def _select_best_template(self, theme: str) -> str:
        """Selectează cel mai bun template pentru temă"""
        if not self.meme_template_engine:
            return "classic_meme"

        theme_config = self.enhanced_themes.get(theme, {})
        preferred_templates = theme_config.get('template_preference', ['classic_meme'])

        # Check which templates are available
        available_templates = self.meme_template_engine.list_templates(min_popularity=5)

        # Find first preferred template that's available
        for template in preferred_templates:
            if template in available_templates:
                return template

        # Fallback to most popular available template
        return available_templates[0] if available_templates else "classic_meme"

    def _split_text_for_template(self, text: str, template_name: str) -> List[str]:
        """Împarte textul pentru pozițiile template-ului"""
        if not self.meme_template_engine:
            return [text]

        template = self.meme_template_engine.get_template(template_name)
        if not template:
            return [text]

        num_positions = len(template.text_positions)

        if num_positions == 1:
            return [text]
        elif num_positions == 2:
            # Split into top and bottom text
            parts = text.split('\n', 1)
            if len(parts) == 2:
                return parts
            else:
                # Split by sentence or use same text twice
                sentences = text.split('. ')
                if len(sentences) >= 2:
                    mid = len(sentences) // 2
                    return ['. '.join(sentences[:mid]), '. '.join(sentences[mid:])]
                else:
                    return [text, ""]
        else:
            # For 3+ positions, split by sentences or words
            sentences = text.split('. ')
            if len(sentences) >= num_positions:
                return sentences[:num_positions]
            else:
                words = text.split()
                chunk_size = max(1, len(words) // num_positions)
                chunks = []
                for i in range(0, len(words), chunk_size):
                    chunk = ' '.join(words[i:i + chunk_size])
                    chunks.append(chunk)

                # Pad with empty strings if needed
                while len(chunks) < num_positions:
                    chunks.append("")

                return chunks[:num_positions]

    def _create_video_from_image(self, image_data: bytes, duration: int) -> VideoFileClip:
        """Creează videoclip din imagine AI"""
        try:
            # Convert bytes to PIL Image
            image = Image.open(BytesIO(image_data))

            # Ensure RGB mode
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Resize to standard resolution
            image = image.resize((720, 720), Image.Resampling.LANCZOS)

            # Convert to numpy array
            image_array = np.array(image)

            # Create video clip
            clip = ImageClip(image_array, duration=duration)
            clip = clip.set_fps(30)

            return clip

        except Exception as e:
            logger.error(f"Error creating video from AI image: {e}")
            # Fallback to colored background
            return self._create_base_video(duration)

    def _generate_output_path(self, theme: str, prefix: str = "shitpost") -> str:
        """Generează calea de output cu prefix"""
        random_id = random.randint(1000, 9999)
        filename = f"{prefix}_{theme}_{random_id}.mp4"
        return os.path.join("storage", "videos", filename)

    def _create_base_video(self, duration: int) -> VideoFileClip:
        """Creează videoclipul de bază cu rezoluție optimizată"""
        # Creează un clip colorat random cu rezoluție mai mică pentru a economisi memoria
        colors = [
            (255, 0, 0),    # Roșu
            (0, 255, 0),    # Verde
            (0, 0, 255),    # Albastru
            (255, 255, 0),  # Galben
            (255, 0, 255),  # Magenta
            (0, 255, 255),  # Cyan
        ]

        color = random.choice(colors)
        # Rezoluție mai mică pentru a economisi memoria (720x1280 în loc de 1080x1920)
        base_clip = ColorClip(size=(720, 1280), color=color, duration=duration)
        base_clip = base_clip.set_fps(30)  # Setează FPS

        return base_clip
    
    def _apply_chaos_effects(self, clip: VideoFileClip, chaos_level: int) -> VideoFileClip:
        """Aplică efectele haotice"""
        processed = clip

        # Asigură-te că clip-ul are FPS setat
        if not hasattr(processed, 'fps') or processed.fps is None:
            processed = processed.set_fps(30)

        # Numărul de efecte bazat pe chaos level
        num_effects = min(chaos_level // 2, len(self.chaos_effects))
        selected_effects = random.sample(self.chaos_effects, max(1, num_effects))

        for effect_name in selected_effects:
            try:
                if hasattr(self, effect_name):
                    effect_function = getattr(self, effect_name)
                    processed = effect_function(processed, chaos_level)
                    # Asigură-te că FPS-ul este păstrat după fiecare efect
                    if not hasattr(processed, 'fps') or processed.fps is None:
                        processed = processed.set_fps(30)
            except Exception as e:
                logger.warning(f"Nu s-a putut aplica efectul {effect_name}: {str(e)}")

        return processed
    
    def _add_chaotic_text(self, clip: VideoFileClip, text: str, theme: str) -> VideoFileClip:
        """Adaugă text cu efecte haotice (versiune simplificată fără ImageMagick)"""
        try:
            # Pentru moment returnăm clip-ul fără text pentru a evita dependința de ImageMagick
            # În viitor se poate adăuga suport pentru text cu alte metode
            logger.info(f"Text pentru shitpost: {text}")

            # Asigură-te că clip-ul returnat are FPS setat
            if not hasattr(clip, 'fps') or clip.fps is None:
                clip = clip.set_fps(30)

            return clip

        except Exception as e:
            logger.warning(f"Nu s-a putut adăuga textul haotic: {str(e)}")
            # Asigură-te că clip-ul returnat are FPS setat chiar și în caz de eroare
            if not hasattr(clip, 'fps') or clip.fps is None:
                clip = clip.set_fps(30)
            return clip
    

    
    # === EFECTE HAOTICE ===
    
    def random_zoom_chaos(self, clip: VideoFileClip, intensity: int) -> VideoFileClip:
        """Zoom complet haotic"""
        try:
            # Zoom static pentru a evita problemele cu resize dinamic
            zoom_factor = 1 + random.uniform(0.1, 0.5) * (intensity / 10)
            zoom_factor = max(0.5, min(2.0, zoom_factor))  # Limitează zoom-ul

            result = clip.resize(zoom_factor)
            # Păstrează FPS-ul
            if hasattr(clip, 'fps') and clip.fps:
                result = result.set_fps(clip.fps)
            elif not hasattr(result, 'fps') or result.fps is None:
                result = result.set_fps(30)
            return result
        except Exception as e:
            logger.warning(f"Eroare zoom chaos: {str(e)}")
            # Asigură-te că clip-ul returnat are FPS
            if not hasattr(clip, 'fps') or clip.fps is None:
                clip = clip.set_fps(30)
            return clip
    
    def color_distortion_extreme(self, clip: VideoFileClip, intensity: int) -> VideoFileClip:
        """Distorsiune de culoare extremă"""
        try:
            def color_chaos(get_frame, t):
                frame = get_frame(t)
                frame = frame.astype(np.float32)

                # Rotește canalele de culoare random
                if int(t * 4) % 3 == 0:
                    frame = frame[:,:,[1,2,0]]  # RGB -> GBR
                elif int(t * 4) % 3 == 1:
                    frame = frame[:,:,[2,0,1]]  # RGB -> BRG

                # Saturație extremă
                frame = np.clip(frame * random.uniform(0.7, 1.8), 0, 255)

                return frame.astype(np.uint8)

            result = clip.fl(color_chaos)
            # Păstrează FPS-ul
            if hasattr(clip, 'fps') and clip.fps:
                result = result.set_fps(clip.fps)
            return result
        except Exception as e:
            logger.warning(f"Eroare color distortion: {str(e)}")
            return clip
    
    def rotation_madness(self, clip: VideoFileClip, intensity: int) -> VideoFileClip:
        """Rotație haotică"""
        try:
            # Rotație statică pentru a evita problemele cu funcții dinamice
            rotation_angle = random.uniform(-45, 45) * (intensity / 5)

            result = clip.rotate(rotation_angle)
            # Păstrează FPS-ul
            if hasattr(clip, 'fps') and clip.fps:
                result = result.set_fps(clip.fps)
            elif not hasattr(result, 'fps') or result.fps is None:
                result = result.set_fps(30)
            return result
        except Exception as e:
            logger.warning(f"Eroare rotation: {str(e)}")
            # Asigură-te că clip-ul returnat are FPS
            if not hasattr(clip, 'fps') or clip.fps is None:
                clip = clip.set_fps(30)
            return clip
    
    def speed_chaos_random(self, clip: VideoFileClip, intensity: int) -> VideoFileClip:
        """Viteză haotică"""
        try:
            # Import corect pentru speedx
            from moviepy.video.fx.speedx import speedx
            speed_factor = random.uniform(0.8, 1.5) * (intensity / 8)
            result = speedx(clip, speed_factor)
            # Păstrează FPS-ul
            if hasattr(clip, 'fps') and clip.fps:
                result = result.set_fps(clip.fps)
            elif not hasattr(result, 'fps') or result.fps is None:
                result = result.set_fps(30)
            return result
        except Exception as e:
            logger.warning(f"Eroare speed chaos: {str(e)}")
            # Asigură-te că clip-ul returnat are FPS
            if not hasattr(clip, 'fps') or clip.fps is None:
                clip = clip.set_fps(30)
            return clip

    def shake_earthquake(self, clip: VideoFileClip, intensity: int) -> VideoFileClip:
        """Shake ca un cutremur"""
        try:
            def shake_function(t):
                shake_x = random.randint(-intensity, intensity)
                shake_y = random.randint(-intensity//2, intensity//2)
                return (shake_x, shake_y)

            result = clip.set_position(shake_function)
            # Păstrează FPS-ul
            if hasattr(clip, 'fps') and clip.fps:
                result = result.set_fps(clip.fps)
            return result
        except Exception as e:
            logger.warning(f"Eroare shake: {str(e)}")
            return clip
    
    def color_invert_flash(self, clip: VideoFileClip, intensity: int) -> VideoFileClip:
        """Flash cu inversare de culori"""
        try:
            def invert_flash(get_frame, t):
                frame = get_frame(t)

                # Inversează culorile la intervale random
                if random.random() < 0.05 * (intensity / 10):
                    frame = 255 - frame

                return frame

            result = clip.fl(invert_flash)
            # Păstrează FPS-ul
            if hasattr(clip, 'fps') and clip.fps:
                result = result.set_fps(clip.fps)
            return result
        except Exception as e:
            logger.warning(f"Eroare color invert: {str(e)}")
            return clip

    def size_chaos_random(self, clip: VideoFileClip, intensity: int) -> VideoFileClip:
        """Schimbarea mărimii haotic"""
        try:
            # Size static pentru a evita problemele cu resize dinamic
            scale_factor = 1 + random.uniform(-0.3, 0.5) * (intensity / 10)
            scale_factor = max(0.5, min(1.8, scale_factor))  # Limitează scale-ul

            result = clip.resize(scale_factor)
            # Păstrează FPS-ul
            if hasattr(clip, 'fps') and clip.fps:
                result = result.set_fps(clip.fps)
            elif not hasattr(result, 'fps') or result.fps is None:
                result = result.set_fps(30)
            return result
        except Exception as e:
            logger.warning(f"Eroare size chaos: {str(e)}")
            # Asigură-te că clip-ul returnat are FPS
            if not hasattr(clip, 'fps') or clip.fps is None:
                clip = clip.set_fps(30)
            return clip
