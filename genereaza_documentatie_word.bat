@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: ========================================
:: 🏆 Generator Documentație InfoEducatie 2025
:: Convertește documentația Markdown în format Word
:: ========================================

title Generator Documentație InfoEducatie 2025

:: Culori pentru output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "RESET=[0m"

echo.
echo %CYAN%========================================%RESET%
echo %CYAN%🏆 Generator Documentație InfoEducatie%RESET%
echo %CYAN%========================================%RESET%
echo %GREEN%Convertește documentația în format Word%RESET%
echo.

:: Verifică dacă suntem în directorul corect
if not exist "DOCUMENTATIE_INFOEDUCATIE.md" (
    echo %RED%❌ EROARE: Fișierul DOCUMENTATIE_INFOEDUCATIE.md nu a fost găsit!%RESET%
    echo %YELLOW%Vă rugăm să rulați acest script din directorul MoneyPrinterTurbo%RESET%
    pause
    exit /b 1
)

:: Verifică dacă Python este instalat
echo %BLUE%🔍 Verificare Python...%RESET%
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Python nu este instalat sau nu este în PATH!%RESET%
    echo %YELLOW%Vă rugăm să instalați Python 3.10+ de la: https://python.org%RESET%
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo %GREEN%✅ Python %PYTHON_VERSION% detectat%RESET%

:: Activează mediul virtual dacă există
if exist "venv\Scripts\python.exe" (
    echo %BLUE%🔄 Activare mediu virtual...%RESET%
    call venv\Scripts\activate.bat
    echo %GREEN%✅ Mediu virtual activat%RESET%
) else (
    echo %YELLOW%⚠️ Mediul virtual nu există, folosesc Python global%RESET%
)

:: Rulează scriptul de conversie
echo.
echo %BLUE%📄 Începe conversia documentației...%RESET%
echo %YELLOW%Aceasta poate dura câteva minute pentru instalarea dependințelor...%RESET%
echo.

python convert_to_word.py

if errorlevel 1 (
    echo.
    echo %RED%❌ Conversia a eșuat!%RESET%
    echo %YELLOW%Verificați că aveți conexiune la internet pentru instalarea pachetelor%RESET%
    pause
    exit /b 1
)

:: Verifică dacă fișierul Word a fost creat
if exist "DOCUMENTATIE_INFOEDUCATIE_2025.docx" (
    echo.
    echo %GREEN%🎉 SUCCES! Documentația Word a fost creată!%RESET%
    echo.
    echo %CYAN%📄 Fișiere generate:%RESET%
    echo %GREEN%   • DOCUMENTATIE_INFOEDUCATIE_2025.docx%RESET%
    echo %GREEN%   • INSTRUCTIUNI_FINALIZARE.md%RESET%
    echo.
    
    :: Calculează dimensiunea fișierului
    for %%A in ("DOCUMENTATIE_INFOEDUCATIE_2025.docx") do (
        set "file_size=%%~zA"
        set /a "file_size_mb=!file_size! / 1024 / 1024"
    )
    
    echo %BLUE%📊 Informații fișier:%RESET%
    echo %YELLOW%   Dimensiune: !file_size_mb! MB%RESET%
    echo %YELLOW%   Format: Microsoft Word (.docx)%RESET%
    echo %YELLOW%   Pagini estimate: 15-20%RESET%
    echo.
    
    echo %CYAN%📋 Următorii pași:%RESET%
    echo %YELLOW%   1. Deschideți DOCUMENTATIE_INFOEDUCATIE_2025.docx%RESET%
    echo %YELLOW%   2. Completați informațiile personale (nume, școală, etc.)%RESET%
    echo %YELLOW%   3. Adăugați screenshot-uri ale aplicației%RESET%
    echo %YELLOW%   4. Verificați formatarea și corectați dacă este necesar%RESET%
    echo %YELLOW%   5. Generați cuprinsul automat în Word%RESET%
    echo %YELLOW%   6. Salvați versiunea finală pentru InfoEducatie%RESET%
    echo.
    
    echo %GREEN%💡 Sfaturi pentru InfoEducatie:%RESET%
    echo %YELLOW%   • Adăugați capturi de ecran ale interfeței în română%RESET%
    echo %YELLOW%   • Includeți exemple de videoclipuri generate%RESET%
    echo %YELLOW%   • Evidențiați aspectele inovatoare ale proiectului%RESET%
    echo %YELLOW%   • Verificați că respectați cerințele de formatare%RESET%
    echo.
    
    set /p "open_file=Deschid documentul Word acum? (Y/N): "
    if /i "!open_file!"=="Y" (
        echo %BLUE%📂 Deschid documentul...%RESET%
        start "" "DOCUMENTATIE_INFOEDUCATIE_2025.docx"
    )
    
    set /p "open_instructions=Deschid instrucțiunile de finalizare? (Y/N): "
    if /i "!open_instructions!"=="Y" (
        echo %BLUE%📋 Deschid instrucțiunile...%RESET%
        start "" "INSTRUCTIUNI_FINALIZARE.md"
    )
    
) else (
    echo.
    echo %RED%❌ Fișierul Word nu a fost creat!%RESET%
    echo %YELLOW%Verificați erorile de mai sus și încercați din nou%RESET%
)

echo.
echo %CYAN%🏆 Succes la competiția InfoEducatie 2025!%RESET%
echo %GREEN%Proiectul "Aparat de Scos Masele la Fraieri" este gata pentru submisie!%RESET%
echo.
pause
