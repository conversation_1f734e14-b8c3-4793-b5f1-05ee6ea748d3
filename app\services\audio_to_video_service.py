"""
Audio to Video Service
Serviciu pentru convertirea fișierelor audio în videoclipuri cu imagini statice
"""

import os
import tempfile
from typing import Optional, List, Dict, Any
from pathlib import Path
import logging
from dataclasses import dataclass
from PIL import Image, ImageDraw, ImageFont
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class AudioVideoConfig:
    """Configurație pentru convertirea audio în video"""
    output_resolution: str = "1080x1920"  # Format vertical
    frame_rate: int = 30
    background_color: str = "#1a1a1a"     # Culoare fundal dacă nu există imagine
    text_color: str = "#ffffff"           # Culoare text
    accent_color: str = "#00ff88"         # Culoare accent
    font_size: int = 48
    show_waveform: bool = True            # Afișează forma de undă
    show_progress: bool = True            # Afișează bara de progres
    show_title: bool = True               # Afișează titlul podcast-ului

class AudioToVideoService:
    """Serviciu pentru convertirea audio în video cu vizualizări"""
    
    def __init__(self):
        self.temp_dir = None
        self._check_dependencies()
    
    def _check_dependencies(self):
        """Verifică dependințele necesare"""
        try:
            import moviepy.editor as mpy
            logger.info("✅ MoviePy este disponibil")
        except ImportError:
            logger.warning("⚠️ MoviePy nu este disponibil")
        
        try:
            from PIL import Image
            logger.info("✅ Pillow este disponibil")
        except ImportError:
            logger.warning("⚠️ Pillow nu este disponibil")
    
    def create_video_from_audio(
        self,
        audio_path: str,
        background_image_path: Optional[str] = None,
        output_path: Optional[str] = None,
        config: Optional[AudioVideoConfig] = None,
        title: str = "Podcast",
        progress_callback=None
    ) -> Optional[str]:
        """
        Creează un videoclip din audio cu imagine statică
        
        Args:
            audio_path: Calea către fișierul audio
            background_image_path: Calea către imaginea de fundal (opțional)
            output_path: Calea pentru videoclipul generat
            config: Configurația pentru generare
            title: Titlul podcast-ului
            progress_callback: Funcție pentru raportarea progresului
            
        Returns:
            Calea către videoclipul generat sau None dacă a eșuat
        """
        
        try:
            if not config:
                config = AudioVideoConfig()
            
            if progress_callback:
                progress_callback("🎵 Încărcare fișier audio...", 0.1)
            
            # Importă MoviePy
            import moviepy.editor as mpy
            
            # Încarcă audio-ul
            audio_clip = mpy.AudioFileClip(audio_path)
            duration = audio_clip.duration
            
            if progress_callback:
                progress_callback("🖼️ Pregătire imagine de fundal...", 0.2)
            
            # Pregătește imaginea de fundal
            background_clip = self._create_background_clip(
                background_image_path, 
                duration, 
                config,
                title
            )
            
            if progress_callback:
                progress_callback("🎨 Generare vizualizări audio...", 0.4)
            
            # Adaugă vizualizări audio dacă sunt activate
            visual_clips = []
            
            if config.show_waveform:
                waveform_clip = self._create_waveform_visualization(
                    audio_clip, 
                    config
                )
                if waveform_clip:
                    visual_clips.append(waveform_clip)
            
            if config.show_progress:
                progress_clip = self._create_progress_bar(
                    duration,
                    config
                )
                if progress_clip:
                    visual_clips.append(progress_clip)
            
            if progress_callback:
                progress_callback("🎬 Combinare elemente video...", 0.6)
            
            # Combină toate elementele
            final_clips = [background_clip] + visual_clips
            
            # Creează videoclipul final
            final_video = mpy.CompositeVideoClip(final_clips)
            final_video = final_video.set_audio(audio_clip)
            final_video = final_video.set_duration(duration)
            
            # Generează calea de ieșire dacă nu este specificată
            if not output_path:
                audio_path_obj = Path(audio_path)
                output_path = str(audio_path_obj.parent / f"{audio_path_obj.stem}_video.mp4")
            
            if progress_callback:
                progress_callback("💾 Export videoclip final...", 0.8)
            
            # Exportă videoclipul
            final_video.write_videofile(
                output_path,
                fps=config.frame_rate,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # Cleanup
            audio_clip.close()
            final_video.close()
            
            if progress_callback:
                progress_callback("✅ Video generat cu succes!", 1.0)
            
            logger.info(f"Video generat: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Eroare în generarea video din audio: {e}")
            return None
    
    def _create_background_clip(
        self,
        background_image_path: Optional[str],
        duration: float,
        config: AudioVideoConfig,
        title: str
    ):
        """Creează clip-ul de fundal"""
        
        import moviepy.editor as mpy
        
        # Parsează rezoluția
        width, height = map(int, config.output_resolution.split('x'))
        
        if background_image_path and os.path.exists(background_image_path):
            # Folosește imaginea furnizată
            try:
                # Încarcă și redimensionează imaginea
                img = Image.open(background_image_path)
                img = img.convert('RGB')
                
                # Redimensionează pentru a se potrivi cu rezoluția
                img = img.resize((width, height), Image.Resampling.LANCZOS)
                
                # Adaugă overlay semi-transparent pentru text
                overlay = Image.new('RGBA', (width, height), (0, 0, 0, 128))
                img = img.convert('RGBA')
                img = Image.alpha_composite(img, overlay)
                img = img.convert('RGB')
                
                # Salvează imaginea temporar
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp:
                    img.save(tmp.name, 'JPEG')
                    background_clip = mpy.ImageClip(tmp.name, duration=duration)
                
            except Exception as e:
                logger.warning(f"Nu s-a putut încărca imaginea de fundal: {e}")
                background_clip = self._create_default_background(width, height, duration, config, title)
        else:
            # Creează fundal implicit
            background_clip = self._create_default_background(width, height, duration, config, title)
        
        return background_clip
    
    def _create_default_background(
        self,
        width: int,
        height: int,
        duration: float,
        config: AudioVideoConfig,
        title: str
    ):
        """Creează un fundal implicit cu gradient și text"""
        
        import moviepy.editor as mpy
        
        # Creează imagine cu gradient
        img = Image.new('RGB', (width, height), config.background_color)
        draw = ImageDraw.Draw(img)
        
        # Adaugă gradient simplu
        for y in range(height):
            alpha = y / height
            color_value = int(26 + alpha * 20)  # Gradient subtil
            color = (color_value, color_value, color_value)
            draw.line([(0, y), (width, y)], fill=color)
        
        # Adaugă titlul dacă este activat
        if config.show_title and title:
            try:
                # Încearcă să folosească un font mai frumos
                try:
                    font = ImageFont.truetype("arial.ttf", config.font_size)
                except:
                    font = ImageFont.load_default()
                
                # Calculează poziția textului
                bbox = draw.textbbox((0, 0), title, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                x = (width - text_width) // 2
                y = height // 4
                
                # Adaugă umbră pentru text
                shadow_offset = 2
                draw.text((x + shadow_offset, y + shadow_offset), title, 
                         fill=(0, 0, 0), font=font)
                
                # Adaugă textul principal
                draw.text((x, y), title, fill=config.text_color, font=font)
                
            except Exception as e:
                logger.warning(f"Nu s-a putut adăuga titlul: {e}")
        
        # Salvează imaginea temporar
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp:
            img.save(tmp.name, 'JPEG')
            background_clip = mpy.ImageClip(tmp.name, duration=duration)
        
        return background_clip
    
    def _create_waveform_visualization(
        self,
        audio_clip,
        config: AudioVideoConfig
    ):
        """Creează vizualizarea formei de undă (simplificată)"""
        
        try:
            import moviepy.editor as mpy
            
            # Pentru moment, returnăm None - implementarea completă necesită librosa
            # În versiunea completă, am genera o vizualizare reală a formei de undă
            logger.info("Vizualizarea formei de undă va fi implementată în versiunea completă")
            return None
            
        except Exception as e:
            logger.warning(f"Nu s-a putut crea vizualizarea formei de undă: {e}")
            return None
    
    def _create_progress_bar(
        self,
        duration: float,
        config: AudioVideoConfig
    ):
        """Creează bara de progres animată"""
        
        try:
            import moviepy.editor as mpy
            
            # Parsează rezoluția
            width, height = map(int, config.output_resolution.split('x'))
            
            # Dimensiunile barei de progres
            bar_width = int(width * 0.8)
            bar_height = 8
            bar_x = (width - bar_width) // 2
            bar_y = int(height * 0.85)
            
            def make_frame(t):
                """Generează frame-ul barei de progres la timpul t"""
                img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
                draw = ImageDraw.Draw(img)
                
                # Bara de fundal
                draw.rectangle(
                    [bar_x, bar_y, bar_x + bar_width, bar_y + bar_height],
                    fill=(255, 255, 255, 50)
                )
                
                # Bara de progres
                progress = t / duration
                progress_width = int(bar_width * progress)
                
                if progress_width > 0:
                    # Convertește culoarea accent din hex
                    accent_rgb = tuple(int(config.accent_color[i:i+2], 16) for i in (1, 3, 5))
                    
                    draw.rectangle(
                        [bar_x, bar_y, bar_x + progress_width, bar_y + bar_height],
                        fill=accent_rgb + (255,)
                    )
                
                return np.array(img)
            
            progress_clip = mpy.VideoClip(make_frame, duration=duration)
            return progress_clip
            
        except Exception as e:
            logger.warning(f"Nu s-a putut crea bara de progres: {e}")
            return None
    
    def extract_audio_segments(
        self,
        audio_path: str,
        segments: List[Dict[str, Any]],
        output_dir: str
    ) -> List[str]:
        """
        Extrage segmente audio pentru clipuri individuale
        
        Args:
            audio_path: Calea către fișierul audio original
            segments: Lista de segmente cu start_time și end_time
            output_dir: Directorul pentru segmentele extrase
            
        Returns:
            Lista cu căile către segmentele audio extrase
        """
        
        try:
            import moviepy.editor as mpy
            
            # Încarcă audio-ul original
            audio_clip = mpy.AudioFileClip(audio_path)
            
            extracted_segments = []
            
            for i, segment in enumerate(segments):
                start_time = segment.get('start_time', 0)
                end_time = segment.get('end_time', audio_clip.duration)
                
                # Extrage segmentul
                segment_clip = audio_clip.subclip(start_time, end_time)
                
                # Generează numele fișierului
                segment_filename = f"segment_{i:03d}_{start_time:.1f}s-{end_time:.1f}s.mp3"
                segment_path = os.path.join(output_dir, segment_filename)
                
                # Salvează segmentul
                segment_clip.write_audiofile(
                    segment_path,
                    verbose=False,
                    logger=None
                )
                
                extracted_segments.append(segment_path)
                segment_clip.close()
            
            audio_clip.close()
            
            logger.info(f"Extrase {len(extracted_segments)} segmente audio")
            return extracted_segments
            
        except Exception as e:
            logger.error(f"Eroare în extragerea segmentelor audio: {e}")
            return []
