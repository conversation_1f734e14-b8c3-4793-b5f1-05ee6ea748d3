"""
Manager pentru temele personalizabile ale aplicației.
Inspirat din designul Bandcamp cu suport pentru culori și imagini de fundal.
"""

import os
import json
import base64
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
from loguru import logger


@dataclass
class ThemeColors:
    """Culorile unei teme."""
    primary: str = "#1976d2"           # Culoarea principală
    secondary: str = "#dc004e"         # Culoarea secundară  
    background: str = "#ffffff"        # Fundalul principal
    surface: str = "#f5f5f5"          # Suprafețele (carduri, panouri)
    text_primary: str = "#212121"      # Textul principal
    text_secondary: str = "#757575"    # Textul secundar
    accent: str = "#ff5722"           # Culoarea de accent
    success: str = "#4caf50"          # Verde pentru succes
    warning: str = "#ff9800"          # Portocaliu pentru avertismente
    error: str = "#f44336"            # Ro<PERSON>u pentru erori


@dataclass
class BackgroundSettings:
    """Setările pentru imaginea de fundal."""
    image_path: Optional[str] = None   # Calea către imagine
    image_data: Optional[str] = None   # Datele imaginii în base64
    position: str = "cover"            # cover, contain, repeat, center
    opacity: float = 1.0               # Opacitatea imaginii (0.0 - 1.0)
    blur: int = 0                      # Blur în pixeli (0-20)


@dataclass
class Theme:
    """O temă completă pentru aplicație."""
    name: str
    description: str
    colors: ThemeColors
    background: BackgroundSettings
    created_by: str = "User"
    version: str = "1.0"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertește tema în dicționar."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Theme':
        """Creează o temă din dicționar."""
        colors_data = data.get('colors', {})
        background_data = data.get('background', {})
        
        return cls(
            name=data.get('name', 'Unnamed Theme'),
            description=data.get('description', ''),
            colors=ThemeColors(**colors_data),
            background=BackgroundSettings(**background_data),
            created_by=data.get('created_by', 'User'),
            version=data.get('version', '1.0')
        )


class ThemeManager:
    """Manager pentru temele aplicației."""
    
    def __init__(self):
        self.themes_dir = Path("storage/themes")
        self.themes_dir.mkdir(parents=True, exist_ok=True)
        
        self.current_theme: Optional[Theme] = None
        self.available_themes: Dict[str, Theme] = {}
        
        # Încarcă temele disponibile
        self._load_available_themes()
        
        # Încarcă tema implicită
        self._load_default_theme()
    
    def _load_available_themes(self) -> None:
        """Încarcă toate temele disponibile din directorul de teme."""
        try:
            # Încarcă temele predefinite
            self._create_predefined_themes()
            
            # Încarcă temele personalizate
            for theme_file in self.themes_dir.glob("*.json"):
                try:
                    with open(theme_file, 'r', encoding='utf-8') as f:
                        theme_data = json.load(f)
                        theme = Theme.from_dict(theme_data)
                        self.available_themes[theme.name] = theme
                        logger.info(f"✅ Temă încărcată: {theme.name}")
                except Exception as e:
                    logger.warning(f"⚠️ Nu s-a putut încărca tema {theme_file}: {e}")
            
            logger.info(f"📚 Încărcate {len(self.available_themes)} teme")
            
        except Exception as e:
            logger.error(f"❌ Eroare la încărcarea temelor: {e}")
    
    def _create_predefined_themes(self) -> None:
        """Creează temele predefinite inspirate din Bandcamp."""
        
        # Tema 1: Bandcamp Classic (albastru și roz)
        bandcamp_classic = Theme(
            name="Bandcamp Classic",
            description="Tema clasică Bandcamp cu albastru și roz",
            colors=ThemeColors(
                primary="#1da0c3",      # Albastru Bandcamp
                secondary="#ff6bcb",    # Roz Bandcamp
                background="#ffffff",
                surface="#f7f7f7",
                text_primary="#333333",
                text_secondary="#666666",
                accent="#ff6bcb",
                success="#4caf50",
                warning="#ff9800",
                error="#f44336"
            ),
            background=BackgroundSettings(),
            created_by="MoneyPrinterTurbo",
            version="1.0"
        )
        
        # Tema 2: Dark Bandcamp
        dark_bandcamp = Theme(
            name="Dark Bandcamp",
            description="Versiunea întunecată a temei Bandcamp cu contrast îmbunătățit",
            colors=ThemeColors(
                primary="#1da0c3",      # Albastru Bandcamp
                secondary="#ff6bcb",    # Roz Bandcamp
                background="#121212",   # Fundal foarte întunecat pentru contrast
                surface="#1e1e1e",      # Suprafețe mai deschise
                text_primary="#ffffff", # Text alb pentru contrast maxim
                text_secondary="#b0b0b0", # Text secundar mai deschis
                accent="#ff6bcb",       # Roz accent
                success="#4caf50",      # Verde standard pentru succes
                warning="#ff9800",      # Portocaliu pentru avertismente
                error="#f44336"         # Roșu pentru erori
            ),
            background=BackgroundSettings(),
            created_by="MoneyPrinterTurbo",
            version="1.0"
        )
        
        # Tema 3: Sunset Vibes
        sunset_vibes = Theme(
            name="Sunset Vibes",
            description="Culori calde de apus pentru o atmosferă relaxantă",
            colors=ThemeColors(
                primary="#ff6b35",      # Portocaliu apus
                secondary="#f7931e",    # Galben apus
                background="#fff8f0",   # Crem deschis
                surface="#ffe8d1",      # Portocaliu foarte pal
                text_primary="#2d3436", # Text întunecat pentru contrast
                text_secondary="#636e72", # Text secundar
                accent="#e17055",       # Portocaliu accent
                success="#00b894",      # Verde turcoaz
                warning="#fdcb6e",      # Galben avertisment
                error="#e84393"         # Roz eroare
            ),
            background=BackgroundSettings(),
            created_by="MoneyPrinterTurbo",
            version="1.0"
        )
        
        # Tema 4: Ocean Breeze
        ocean_breeze = Theme(
            name="Ocean Breeze",
            description="Nuanțe de albastru și verde inspirate de ocean",
            colors=ThemeColors(
                primary="#0984e3",      # Albastru ocean
                secondary="#00cec9",    # Turcoaz
                background="#f8f9fa",
                surface="#e3f2fd",      # Albastru foarte pal
                text_primary="#2c3e50",
                text_secondary="#7f8c8d",
                accent="#00cec9",
                success="#27ae60",
                warning="#f39c12",
                error="#e74c3c"
            ),
            background=BackgroundSettings(),
            created_by="MoneyPrinterTurbo",
            version="1.0"
        )
        
        # Tema 5: Purple Dreams
        purple_dreams = Theme(
            name="Purple Dreams",
            description="Nuanțe de violet și lavandă pentru o atmosferă creativă",
            colors=ThemeColors(
                primary="#6c5ce7",      # Violet
                secondary="#a29bfe",    # Lavandă
                background="#ffffff",   # Alb pentru lizibilitate
                surface="#f8f7ff",      # Violet foarte pal
                text_primary="#2d3436",
                text_secondary="#636e72",
                accent="#fd79a8",       # Roz pal
                success="#00b894",
                warning="#fdcb6e",
                error="#e84393"
            ),
            background=BackgroundSettings(),
            created_by="MoneyPrinterTurbo",
            version="1.0"
        )
        
        # Adaugă temele în lista disponibilă
        predefined_themes = [
            bandcamp_classic, dark_bandcamp, sunset_vibes, 
            ocean_breeze, purple_dreams
        ]
        
        for theme in predefined_themes:
            self.available_themes[theme.name] = theme
    
    def _load_default_theme(self) -> None:
        """Încarcă tema implicită."""
        # Încearcă să încărce tema salvată
        try:
            config_file = self.themes_dir / "current_theme.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    theme_name = json.load(f).get('current_theme')
                    if theme_name in self.available_themes:
                        self.current_theme = self.available_themes[theme_name]
                        logger.info(f"✅ Temă curentă încărcată: {theme_name}")
                        return
        except Exception as e:
            logger.warning(f"⚠️ Nu s-a putut încărca tema salvată: {e}")
        
        # Folosește tema implicită
        if "Bandcamp Classic" in self.available_themes:
            self.current_theme = self.available_themes["Bandcamp Classic"]
            logger.info("✅ Folosește tema implicită: Bandcamp Classic")
    
    def get_available_themes(self) -> List[str]:
        """Returnează lista numelor temelor disponibile."""
        return list(self.available_themes.keys())
    
    def get_theme(self, name: str) -> Optional[Theme]:
        """Returnează o temă după nume."""
        return self.available_themes.get(name)
    
    def get_current_theme(self) -> Optional[Theme]:
        """Returnează tema curentă."""
        return self.current_theme
    
    def set_current_theme(self, theme_name: str) -> bool:
        """Setează tema curentă."""
        if theme_name in self.available_themes:
            self.current_theme = self.available_themes[theme_name]
            self._save_current_theme_config()
            logger.info(f"✅ Temă setată: {theme_name}")
            return True
        else:
            logger.warning(f"⚠️ Tema nu există: {theme_name}")
            return False
    
    def save_theme(self, theme: Theme) -> bool:
        """Salvează o temă personalizată."""
        try:
            theme_file = self.themes_dir / f"{theme.name.replace(' ', '_').lower()}.json"
            with open(theme_file, 'w', encoding='utf-8') as f:
                json.dump(theme.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.available_themes[theme.name] = theme
            logger.info(f"✅ Temă salvată: {theme.name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Eroare la salvarea temei: {e}")
            return False
    
    def delete_theme(self, theme_name: str) -> bool:
        """Șterge o temă personalizată."""
        if theme_name not in self.available_themes:
            return False
        
        theme = self.available_themes[theme_name]
        if theme.created_by == "MoneyPrinterTurbo":
            logger.warning(f"⚠️ Nu se poate șterge tema predefinită: {theme_name}")
            return False
        
        try:
            theme_file = self.themes_dir / f"{theme_name.replace(' ', '_').lower()}.json"
            if theme_file.exists():
                theme_file.unlink()
            
            del self.available_themes[theme_name]
            
            # Dacă tema ștearsă era curentă, setează tema implicită
            if self.current_theme and self.current_theme.name == theme_name:
                self.set_current_theme("Bandcamp Classic")
            
            logger.info(f"✅ Temă ștearsă: {theme_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Eroare la ștergerea temei: {e}")
            return False
    
    def _save_current_theme_config(self) -> None:
        """Salvează configurația temei curente."""
        try:
            config_file = self.themes_dir / "current_theme.json"
            config = {
                'current_theme': self.current_theme.name if self.current_theme else None
            }
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ Nu s-a putut salva configurația temei: {e}")
    
    def generate_css(self, theme: Optional[Theme] = None) -> str:
        """Generează CSS-ul pentru o temă."""
        if theme is None:
            theme = self.current_theme
        
        if theme is None:
            return ""
        
        css = f"""
        <style>
        :root {{
            --primary-color: {theme.colors.primary};
            --secondary-color: {theme.colors.secondary};
            --background-color: {theme.colors.background};
            --surface-color: {theme.colors.surface};
            --text-primary: {theme.colors.text_primary};
            --text-secondary: {theme.colors.text_secondary};
            --accent-color: {theme.colors.accent};
            --success-color: {theme.colors.success};
            --warning-color: {theme.colors.warning};
            --error-color: {theme.colors.error};
        }}

        /* Protejează bara de navigare Streamlit de modificări */
        header[data-testid="stHeader"] {{
            background-color: inherit !important;
            color: inherit !important;
        }}

        /* Protejează meniul hamburger și butonul Deploy */
        button[data-testid="stAppViewBlockContainer"] {{
            background-color: inherit !important;
            color: inherit !important;
        }}

        /* Protejează toolbar-ul Streamlit */
        .stToolbar {{
            background-color: inherit !important;
            color: inherit !important;
        }}

        /* Aplicarea temei doar la conținutul principal */
        .main .block-container {{
            background-color: var(--background-color);
            color: var(--text-primary);
        }}

        /* Aplicarea temei la întreaga aplicație, dar nu la header */
        .stApp > div:not(header) {{
            background-color: var(--background-color);
            color: var(--text-primary);
        }}

        /* Sidebar styling */
        .stSidebar > div {{
            background-color: var(--surface-color);
            color: var(--text-primary);
        }}

        /* Button styling */
        .stButton > button {{
            background-color: var(--primary-color) !important;
            color: white !important;
            border: none !important;
            border-radius: 6px !important;
            transition: background-color 0.3s ease;
        }}

        .stButton > button:hover {{
            background-color: var(--secondary-color) !important;
            border: none !important;
        }}

        /* Input fields styling */
        .stSelectbox > div > div > div {{
            background-color: var(--surface-color) !important;
            color: var(--text-primary) !important;
            border: 1px solid var(--primary-color) !important;
        }}

        .stTextInput > div > div > input {{
            background-color: var(--surface-color) !important;
            color: var(--text-primary) !important;
            border: 1px solid var(--primary-color) !important;
        }}

        .stTextArea > div > div > textarea {{
            background-color: var(--surface-color) !important;
            color: var(--text-primary) !important;
            border: 1px solid var(--primary-color) !important;
        }}

        /* Expander styling */
        .stExpander > div {{
            background-color: var(--surface-color) !important;
            border: 1px solid var(--primary-color) !important;
            border-radius: 6px !important;
        }}

        .stExpander > div > div > div > div {{
            color: var(--text-primary) !important;
        }}

        /* Tabs styling */
        .stTabs > div > div > div > div {{
            background-color: var(--surface-color) !important;
            color: var(--text-primary) !important;
        }}

        .stTabs > div > div > div > div[data-baseweb="tab"] {{
            background-color: var(--surface-color) !important;
            color: var(--text-secondary) !important;
            border-bottom: 2px solid transparent !important;
        }}

        .stTabs > div > div > div > div[data-baseweb="tab"][aria-selected="true"] {{
            background-color: var(--primary-color) !important;
            color: white !important;
            border-bottom: 2px solid var(--accent-color) !important;
        }}

        /* Metrics styling */
        .metric-container {{
            background-color: var(--surface-color) !important;
            border: 1px solid var(--primary-color) !important;
            border-radius: 6px !important;
            padding: 10px !important;
        }}

        /* Success/Warning/Error messages */
        .stSuccess {{
            background-color: var(--success-color) !important;
            color: white !important;
        }}

        .stWarning {{
            background-color: var(--warning-color) !important;
            color: white !important;
        }}

        .stError {{
            background-color: var(--error-color) !important;
            color: white !important;
        }}

        /* Checkbox and radio styling */
        .stCheckbox > label > div {{
            background-color: var(--surface-color) !important;
            border: 1px solid var(--primary-color) !important;
        }}

        /* Slider styling */
        .stSlider > div > div > div > div {{
            background-color: var(--primary-color) !important;
        }}

        /* File uploader styling */
        .stFileUploader > div > div {{
            background-color: var(--surface-color) !important;
            border: 2px dashed var(--primary-color) !important;
            color: var(--text-primary) !important;
        }}

        /* Dataframe styling */
        .stDataFrame {{
            background-color: var(--surface-color) !important;
            color: var(--text-primary) !important;
        }}
        """
        
        # Adaugă stiluri pentru imaginea de fundal
        if theme.background.image_data or theme.background.image_path:
            bg_image = ""
            if theme.background.image_data:
                bg_image = f"data:image/png;base64,{theme.background.image_data}"
            elif theme.background.image_path:
                bg_image = theme.background.image_path
            
            css += f"""
            .stApp::before {{
                content: "";
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-image: url('{bg_image}');
                background-size: {theme.background.position};
                background-repeat: no-repeat;
                background-position: center;
                opacity: {theme.background.opacity};
                filter: blur({theme.background.blur}px);
                z-index: -1;
            }}
            """
        
        css += "</style>"
        return css


# Instanță globală pentru managerul de teme
_theme_manager = None

def get_theme_manager() -> ThemeManager:
    """Returnează instanța globală a managerului de teme."""
    global _theme_manager
    if _theme_manager is None:
        _theme_manager = ThemeManager()
    return _theme_manager
