"""
Local Stable Diffusion Integration
Supports Automatic1111 WebUI and Fooocus for local AI image generation
"""

import asyncio
import aiohttp
import logging
import json
import base64
import os
import subprocess
import time
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class LocalSDConfig:
    """Configuration for local Stable Diffusion"""
    width: int = 512
    height: int = 512
    steps: int = 20
    cfg_scale: float = 7.0
    sampler: str = "DPM++ 2M Karras"
    model: str = "sd-v1-5-pruned.ckpt"
    negative_prompt: str = "blurry, low quality, distorted, deformed, ugly, bad anatomy"

class Automatic1111Client:
    """Client for Automatic1111 WebUI API"""
    
    def __init__(self, base_url: str = "http://localhost:7860"):
        self.base_url = base_url
        self.session = None
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=120)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def is_available(self) -> bool:
        """Check if Automatic1111 WebUI is running"""
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/sdapi/v1/options") as response:
                return response.status == 200
        except Exception:
            return False
    
    async def get_models(self) -> List[str]:
        """Get available models"""
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/sdapi/v1/sd-models") as response:
                if response.status == 200:
                    models = await response.json()
                    return [model["model_name"] for model in models]
        except Exception as e:
            logger.debug(f"Error getting models: {e}")
        return []
    
    async def generate_image(self, prompt: str, config: LocalSDConfig) -> Optional[bytes]:
        """Generate image using Automatic1111 WebUI"""
        try:
            session = await self._get_session()
            
            payload = {
                "prompt": prompt,
                "negative_prompt": config.negative_prompt,
                "width": config.width,
                "height": config.height,
                "steps": config.steps,
                "cfg_scale": config.cfg_scale,
                "sampler_name": config.sampler,
                "batch_size": 1,
                "n_iter": 1,
                "restore_faces": False,
                "tiling": False,
                "do_not_save_samples": True,
                "do_not_save_grid": True
            }
            
            async with session.post(f"{self.base_url}/sdapi/v1/txt2img", json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    if "images" in result and result["images"]:
                        # Decode base64 image
                        image_b64 = result["images"][0]
                        image_data = base64.b64decode(image_b64)
                        logger.info(f"Generated image with Automatic1111: {len(image_data)} bytes")
                        return image_data
                else:
                    logger.error(f"Automatic1111 API error: {response.status}")
            
            return None
            
        except Exception as e:
            logger.error(f"Automatic1111 generation error: {e}")
            return None
    
    async def cleanup(self):
        """Clean up resources"""
        if self.session and not self.session.closed:
            await self.session.close()

class FoocusClient:
    """Client for Fooocus (alternative to Automatic1111)"""
    
    def __init__(self, base_url: str = "http://localhost:8888"):
        self.base_url = base_url
        self.session = None
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=120)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def is_available(self) -> bool:
        """Check if Fooocus is running"""
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/ping") as response:
                return response.status == 200
        except Exception:
            return False
    
    async def generate_image(self, prompt: str, config: LocalSDConfig) -> Optional[bytes]:
        """Generate image using Fooocus"""
        try:
            session = await self._get_session()
            
            payload = {
                "prompt": prompt,
                "negative_prompt": config.negative_prompt,
                "image_number": 1,
                "image_seed": -1,
                "sharpness": 2.0,
                "guidance_scale": config.cfg_scale,
                "base_model_name": "juggernautXL_v6Rundiffusion.safetensors",
                "performance_selection": "Speed",
                "aspect_ratios_selection": f"{config.width}×{config.height}",
                "style_selections": ["Fooocus V2", "Fooocus Enhance", "Fooocus Sharp"]
            }
            
            async with session.post(f"{self.base_url}/v1/generation/text-to-image", json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    if result and len(result) > 0:
                        # Fooocus returns image data directly
                        image_data = base64.b64decode(result[0])
                        logger.info(f"Generated image with Fooocus: {len(image_data)} bytes")
                        return image_data
                else:
                    logger.error(f"Fooocus API error: {response.status}")
            
            return None
            
        except Exception as e:
            logger.error(f"Fooocus generation error: {e}")
            return None
    
    async def cleanup(self):
        """Clean up resources"""
        if self.session and not self.session.closed:
            await self.session.close()

class LocalStableDiffusionManager:
    """Manager for local Stable Diffusion installations"""
    
    def __init__(self):
        self.automatic1111 = Automatic1111Client()
        self.fooocus = FoocusClient()
        self.available_clients = []
        
    async def initialize(self):
        """Initialize and detect available local SD installations"""
        logger.info("🔍 Detecting local Stable Diffusion installations...")
        
        # Check Automatic1111
        if await self.automatic1111.is_available():
            self.available_clients.append(("Automatic1111", self.automatic1111))
            models = await self.automatic1111.get_models()
            logger.info(f"✅ Automatic1111 WebUI detected with {len(models)} models")
        
        # Check Fooocus
        if await self.fooocus.is_available():
            self.available_clients.append(("Fooocus", self.fooocus))
            logger.info("✅ Fooocus detected")
        
        if not self.available_clients:
            logger.warning("❌ No local Stable Diffusion installations found")
            return False
        
        logger.info(f"🎉 Found {len(self.available_clients)} local SD installation(s)")
        return True
    
    async def generate_image(self, prompt: str, width: int = 512, height: int = 512) -> Optional[bytes]:
        """Generate image using the best available local client"""
        if not self.available_clients:
            await self.initialize()
        
        if not self.available_clients:
            logger.warning("No local Stable Diffusion clients available")
            return None
        
        # Create config
        config = LocalSDConfig(
            width=width,
            height=height,
            steps=20,
            cfg_scale=7.0
        )
        
        # Try each available client
        for client_name, client in self.available_clients:
            try:
                logger.info(f"🎨 Generating image with {client_name}...")
                result = await client.generate_image(prompt, config)
                if result:
                    logger.info(f"✅ Successfully generated image with {client_name}")
                    return result
            except Exception as e:
                logger.error(f"❌ {client_name} failed: {e}")
                continue
        
        logger.error("❌ All local SD clients failed")
        return None
    
    async def cleanup(self):
        """Clean up all clients"""
        await self.automatic1111.cleanup()
        await self.fooocus.cleanup()

class LocalSDInstaller:
    """Helper class for installing local Stable Diffusion"""
    
    @staticmethod
    def get_installation_guide() -> str:
        """Get installation guide for local Stable Diffusion"""
        return """
🚀 LOCAL STABLE DIFFUSION INSTALLATION GUIDE

📋 SYSTEM REQUIREMENTS:
- GPU: 4GB+ VRAM (NVIDIA recommended)
- RAM: 8GB+ system memory
- Storage: 10GB+ free space
- OS: Windows 10/11, Linux, macOS

🔧 OPTION 1: AUTOMATIC1111 WEBUI (Recommended)
1. Install Git: https://git-scm.com/downloads
2. Install Python 3.10.6: https://www.python.org/downloads/
3. Clone repository:
   git clone https://github.com/AUTOMATIC1111/stable-diffusion-webui
4. Run with low VRAM optimization:
   cd stable-diffusion-webui
   ./webui.bat --medvram --precision full --no-half
5. Download model (place in models/Stable-diffusion/):
   - sd-v1-5-pruned.ckpt (2.5GB)
   - https://huggingface.co/stable-diffusion-v1-5/stable-diffusion-v1-5

🎯 OPTION 2: FOOOCUS (Easier)
1. Download from: https://github.com/lllyasviel/Fooocus
2. Extract and run Fooocus.exe
3. Automatically downloads models on first run
4. Optimized for low VRAM GPUs

💡 TIPS FOR 4GB VRAM:
- Use --medvram flag for Automatic1111
- Enable "Optimize VRAM usage" in settings
- Use 512x512 resolution for faster generation
- Close other GPU-intensive applications

🔗 USEFUL LINKS:
- Automatic1111: https://github.com/AUTOMATIC1111/stable-diffusion-webui
- Fooocus: https://github.com/lllyasviel/Fooocus
- Models: https://huggingface.co/models?pipeline_tag=text-to-image
"""
    
    @staticmethod
    def check_system_requirements() -> Dict[str, Any]:
        """Check if system meets requirements for local SD"""
        import psutil
        import platform
        
        requirements = {
            "os": platform.system(),
            "ram_gb": round(psutil.virtual_memory().total / (1024**3), 1),
            "cpu_cores": psutil.cpu_count(),
            "gpu_available": False,
            "gpu_memory_gb": 0,
            "meets_requirements": False
        }
        
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                requirements["gpu_available"] = True
                requirements["gpu_memory_gb"] = round(gpus[0].memoryTotal / 1024, 1)
        except ImportError:
            pass
        
        # Check if meets minimum requirements
        requirements["meets_requirements"] = (
            requirements["ram_gb"] >= 8 and
            requirements["gpu_memory_gb"] >= 4
        )
        
        return requirements
