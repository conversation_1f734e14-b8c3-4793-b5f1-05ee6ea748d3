#!/usr/bin/env python3
"""
Test script for Fallback Image Generator

This script tests the fallback image generation system to ensure it works
when AI providers are unavailable.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

from app.services.fallback_image_generator import FallbackImageGenerator


async def test_fallback_generator():
    """Test the fallback image generator"""
    print("🎨 Testing Fallback Image Generator...")
    
    generator = FallbackImageGenerator()
    
    # Test different scene types and prompts
    test_cases = [
        {
            "prompt": "medieval Moldovan warrior <PERSON><PERSON><PERSON> cel <PERSON> in command tent",
            "scene_type": "dialogue",
            "emotional_tone": "dramatic",
            "description": "Historical dialogue scene"
        },
        {
            "prompt": "epic battle scene with Moldovan warriors fighting Skibidi Toilets",
            "scene_type": "battle", 
            "emotional_tone": "epic",
            "description": "Epic battle scene"
        },
        {
            "prompt": "beautiful landscape of Moldovan countryside with mountains",
            "scene_type": "landscape",
            "emotional_tone": "peaceful",
            "description": "Peaceful landscape"
        },
        {
            "prompt": "dynamic action scene with warriors jumping and fighting",
            "scene_type": "action",
            "emotional_tone": "dramatic",
            "description": "Dynamic action scene"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases):
        print(f"\n  🖼️ Test {i+1}: {test_case['description']}")
        print(f"     Prompt: {test_case['prompt'][:50]}...")
        
        try:
            image_data = await generator.generate_contextual_image(
                prompt=test_case['prompt'],
                width=512,
                height=512,
                scene_type=test_case['scene_type'],
                emotional_tone=test_case['emotional_tone']
            )
            
            if image_data:
                print(f"     ✅ Generated: {len(image_data)} bytes")
                
                # Save test image
                test_dir = "storage/test_images"
                os.makedirs(test_dir, exist_ok=True)
                
                filename = f"test_{i+1}_{test_case['scene_type']}.png"
                filepath = os.path.join(test_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(image_data)
                
                print(f"     💾 Saved: {filepath}")
                results.append(True)
            else:
                print(f"     ❌ Failed: No image data returned")
                results.append(False)
                
        except Exception as e:
            print(f"     ❌ Error: {e}")
            results.append(False)
    
    await generator.cleanup()
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 Results: {success_count}/{total_count} tests passed")
    
    if success_count == total_count:
        print("🎉 All fallback image generation tests passed!")
        return True
    else:
        print("⚠️ Some tests failed")
        return False


async def test_ai_manager_with_fallback():
    """Test the AI manager with fallback integration"""
    print("\n🤖 Testing AI Manager with Fallback...")
    
    try:
        from app.services.ai_video_source_manager import AIVideoSourceManager, AIImageConfig
        
        manager = AIVideoSourceManager()
        
        # Test with a simple prompt that should trigger fallback
        config = AIImageConfig()
        config.width = 512
        config.height = 512
        
        test_prompt = "medieval warrior in battle scene"
        
        print(f"  🔍 Testing prompt: {test_prompt}")
        
        result = await manager.generate_image(test_prompt, config)
        
        if result.success:
            print(f"  ✅ Success! Generated {len(result.image_data)} bytes")
            print(f"  🔧 Provider used: {result.provider_used}")
            print(f"  ⏱️ Generation time: {result.generation_time:.2f}s")
            
            # Save the result
            test_dir = "storage/test_images"
            os.makedirs(test_dir, exist_ok=True)
            
            filepath = os.path.join(test_dir, "ai_manager_test.png")
            with open(filepath, 'wb') as f:
                f.write(result.image_data)
            
            print(f"  💾 Saved: {filepath}")
            return True
        else:
            print(f"  ❌ Failed: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting Fallback Image Generator Tests")
    print("=" * 50)
    
    try:
        # Test 1: Direct fallback generator
        test1_success = await test_fallback_generator()
        
        # Test 2: AI manager with fallback integration
        test2_success = await test_ai_manager_with_fallback()
        
        print("\n" + "=" * 50)
        
        if test1_success and test2_success:
            print("🎉 ALL TESTS PASSED!")
            print("\n✅ The fallback image generator is working correctly")
            print("✅ AI manager integration is working")
            print("✅ Contextual image generation will work even when AI providers fail")
            
            print("\n📁 Check the 'storage/test_images' directory to see generated images")
            return True
        else:
            print("❌ SOME TESTS FAILED")
            if not test1_success:
                print("  - Fallback generator test failed")
            if not test2_success:
                print("  - AI manager integration test failed")
            return False
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
