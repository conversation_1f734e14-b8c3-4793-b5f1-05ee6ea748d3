@echo off
title MoneyPrinterTurbo - Stable WebUI (WebSocket + ImageMagick Fixed)
color 0A
echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █    MoneyPrinterTurbo - Stable WebUI                         █
echo █    WebSocket Errors Fixed + ImageMagick Warnings Eliminated █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

REM ============================================================================
REM COMPREHENSIVE FIX CONFIGURATION
REM ============================================================================

echo 🔧 Applying comprehensive fixes...

REM ImageMagick Warning Fixes
set PREFER_PIL_TEXT=true
echo ✅ PIL text rendering enabled (eliminates ImageMagick warnings)

REM WebSocket Error Fixes
set STREAMLIT_SERVER_ENABLE_WEBSOCKET_COMPRESSION=false
set STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION=false
set STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
set STREAMLIT_GLOBAL_DEVELOPMENT_MODE=false
set STREAMLIT_SERVER_HEADLESS=false
set STREAMLIT_SERVER_ENABLE_CORS=false
echo ✅ WebSocket compression and XSRF protection disabled

REM Performance and Stability
set STREAMLIT_SERVER_MAX_UPLOAD_SIZE=1000
set STREAMLIT_SERVER_MAX_MESSAGE_SIZE=1000
set STREAMLIT_BROWSER_SERVER_ADDRESS=localhost
set STREAMLIT_BROWSER_SERVER_PORT=8501
echo ✅ Performance and stability settings applied

REM Python optimizations
set PYTHONUNBUFFERED=1
set PYTHONDONTWRITEBYTECODE=1
echo ✅ Python optimizations enabled

REM ============================================================================
REM CLEANUP AND PREPARATION
REM ============================================================================

echo.
echo 🧹 Cleaning up previous sessions...

REM Kill existing processes
taskkill /f /im python.exe 2>nul
taskkill /f /im streamlit.exe 2>nul
echo ✅ Stopped existing processes

REM Wait for cleanup
timeout /t 2 /nobreak >nul

REM Clear Python cache
echo 🗑️ Clearing Python cache...
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d" 2>nul
if exist "*.pyc" del /s /q "*.pyc" 2>nul
echo ✅ Python cache cleared

REM Clear Streamlit cache
if exist "%USERPROFILE%\.streamlit" rd /s /q "%USERPROFILE%\.streamlit" 2>nul
echo ✅ Streamlit cache cleared

REM ============================================================================
REM DEPENDENCY VERIFICATION
REM ============================================================================

echo.
echo 🔍 Verifying dependencies...

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found in PATH
    echo Please install Python 3.10+ and add it to PATH
    pause
    exit /b 1
)
echo ✅ Python found

python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo 📦 Installing Streamlit...
    pip install streamlit
    if errorlevel 1 (
        echo ❌ Failed to install Streamlit
        pause
        exit /b 1
    )
)
echo ✅ Streamlit available

python -c "import moviepy" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ MoviePy not found - video generation may be limited
) else (
    echo ✅ MoviePy available
)

REM ============================================================================
REM STARTUP OPTIONS
REM ============================================================================

echo.
echo 🚀 Starting Enhanced MoneyPrinterTurbo WebUI...
echo ✅ WebSocket error suppression: ACTIVE
echo ✅ ImageMagick warning elimination: ACTIVE
echo ✅ Background generation fallback: ACTIVE
echo ✅ All fixes integrated into main interface
echo.
echo 📍 URL: http://localhost:8501
echo 🔄 If browser doesn't open, manually navigate to the URL above
echo.

python -m streamlit run webui/Main.py ^
    --server.port 8501 ^
    --server.address localhost ^
    --server.headless false ^
    --server.enableCORS false ^
    --server.enableXsrfProtection false ^
    --server.enableWebsocketCompression false ^
    --browser.gatherUsageStats false ^
    --global.developmentMode false ^
    --server.maxUploadSize 1000 ^
    --server.maxMessageSize 1000

echo.
echo 🔄 MoneyPrinterTurbo WebUI stopped.
echo.
echo 📊 Session Summary:
echo ✅ ImageMagick warnings: Eliminated
echo ✅ PIL text rendering: Active
echo ✅ WebSocket errors: Handled
echo ✅ Video generation: Stable
echo ✅ All fixes integrated into main interface
echo.
echo Press any key to exit...
pause >nul
