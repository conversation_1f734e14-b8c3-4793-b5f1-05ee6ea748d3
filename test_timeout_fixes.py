#!/usr/bin/env python3
"""
Test Timeout Fixes

Tests the timeout handling and fallback mechanisms.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))


async def test_timeout_fixes():
    """Test the timeout fixes and fallback mechanisms"""
    print("🔧 Testing Timeout Fixes and Fallback Mechanisms...")
    print("=" * 60)
    
    try:
        # Test fallback functions
        from webui.components.viral_automation_interface import (
            create_fallback_topic,
            create_fallback_script
        )
        from app.services.one_click_viral_generator import ViralVideoConfig
        
        print("✅ Fallback functions imported successfully")
        
        # Test configuration
        config = ViralVideoConfig(
            category="motivation",
            target_audience="tineri români 18-35",
            platform="tiktok",
            duration=60
        )
        
        print("✅ Configuration created")
        
        # Test fallback topic creation
        print("\n🧪 Testing fallback topic creation...")
        fallback_topic = create_fallback_topic(config)
        
        print(f"✅ Fallback topic created:")
        print(f"   Title: {fallback_topic.title}")
        print(f"   Viral potential: {fallback_topic.viral_potential:.1f}/10")
        print(f"   Romanian relevance: {fallback_topic.romanian_relevance:.1f}/10")
        print(f"   Keywords: {fallback_topic.trending_keywords}")
        
        # Test fallback script creation
        print("\n🧪 Testing fallback script creation...")
        fallback_script = create_fallback_script(fallback_topic, config)
        
        print(f"✅ Fallback script created:")
        print(f"   Length: {len(fallback_script.script_text)} characters")
        print(f"   Duration: {fallback_script.estimated_duration}s")
        print(f"   Hook: {fallback_script.hook}")
        print(f"   Engagement elements: {len(fallback_script.engagement_elements)}")
        
        # Test different categories
        print("\n🧪 Testing different category fallbacks...")
        categories = ["lifestyle_romania", "food_romania", "travel_romania"]
        
        for category in categories:
            config.category = category
            topic = create_fallback_topic(config)
            print(f"   {category}: {topic.title}")
        
        print("\n🎉 Timeout fixes and fallback mechanisms test completed successfully!")
        print("🚀 The system now has robust fallback mechanisms for when AI generation fails or times out!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    try:
        success = asyncio.run(test_timeout_fixes())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
