# 🎙️ Podcast Clipper - Extragere Automată de Clipuri din Podcast-uri

## 📋 Descriere

Podcast Clipper este o funcționalitate avansată integrată în MoneyPrinterTurbo care permite extragerea automată de clipuri verticale din podcast-uri pentru social media. Folosește tehnologii AI de ultimă generație pentru a detecta persoane, analiza vorbitori și genera clipuri optimizate.

## ✨ Funcționalități Principale

### 🎯 Detectare Inteligentă a Persoanelor
- **YOLO v3** pentru detectarea precisă a persoanelor în video
- **Suport multi-persoană** - detectează până la 5 persoane simultan
- **Cadrare automată** - focalizează pe vorbitorul activ
- **Format vertical** - conversie automată în 9:16 pentru social media

### 🗣️ Diarizare Avansată a Vorbitorilor
- **Separare vorbitori** - identifică automat vorbitorii distincți
- **Sincronizare audio-video** - asociază vorbitorii cu persoanele vizibile
- **Segmentare inteligentă** - creează clipuri bazate pe schimbările de vorbitor
- **Filtrare zgomot** - elimină segmentele cu zgomot sau pauze lungi

### 📝 Subtitrări Inteligente
- **WhisperX** pentru transcripție de înaltă precizie
- **Sincronizare perfectă** - subtitrări aliniate la nivel de cuvânt
- **Evidențiere dinamică** - cuvintele se evidențiază pe măsură ce sunt pronunțate
- **Stiluri personalizabile** - multiple opțiuni de design pentru subtitrări

### 🎨 Personalizare Avansată
- **Rezoluții multiple** - 1080x1920, 720x1280, 540x960
- **Calitate video** - opțiuni High/Medium/Low
- **Îmbunătățire audio** - reducere zgomot și normalizare volum
- **Stiluri subtitrări** - Modern, Classic, Bold, Minimal

## 🚀 Instalare

### Instalare Automată (Recomandată)

```bash
python install_podcast_clipper.py
```

Acest script va instala automat:
- Toate dependințele Python necesare
- PyTorch cu suport CUDA (dacă este disponibil)
- Fișierele YOLO pentru detectarea persoanelor
- Modelele Whisper pentru transcripție

### Instalare Manuală

1. **Instalează dependințele:**
```bash
pip install -r requirements_podcast_clipper.txt
```

2. **Descarcă fișierele YOLO:**
```bash
# Creează directorul models
mkdir models

# Descarcă configurația YOLO
wget https://raw.githubusercontent.com/pjreddie/darknet/master/cfg/yolov3.cfg -O models/yolov3.cfg

# Descarcă greutățile YOLO (~250MB)
wget https://pjreddie.com/media/files/yolov3.weights -O models/yolov3.weights

# Descarcă etichetele COCO
wget https://raw.githubusercontent.com/pjreddie/darknet/master/data/coco.names -O models/coco.names
```

## 📖 Utilizare

### 1. Accesare Funcționalitate
- Deschide MoneyPrinterTurbo
- Navighează la tab-ul **"🎙️ Podcast Clipper"**

### 2. Încărcare Video
- Selectează videoclipul podcast-ului (MP4, MOV, AVI, MKV, WEBM)
- Formatul recomandat: MP4 cu rezoluție HD
- Durata optimă: 10-60 minute

### 3. Configurare Setări

#### 🎯 Detectare Persoane
- **Prag încredere**: Cât de sigură să fie detectarea (0.3-0.9)
- **Numărul maxim de persoane**: 1-5 persoane detectate simultan

#### ✂️ Configurare Clipuri
- **Durata clipuri**: 15-120 secunde per clip
- **Timp minim vorbitor**: 3-15 secunde pentru segmente valide

#### 📝 Subtitrări
- **Activare/dezactivare** subtitrări
- **Stil subtitrări**: Modern, Classic, Bold, Minimal
- **Evidențiere cuvinte**: Highlight dinamic pe măsură ce sunt pronunțate

### 4. Setări Avansate

#### 🎥 Video
- **Rezoluție**: 1080x1920 (Full HD), 720x1280 (HD), 540x960 (SD)
- **Frame rate**: 24, 30, sau 60 FPS
- **Calitate**: High, Medium, Low

#### 🎵 Audio
- **Îmbunătățire audio**: Filtre pentru calitate superioară
- **Reducere zgomot**: Elimină zgomotul de fundal
- **Normalizare volum**: Uniformizează volumul audio

### 5. Procesare
- Apasă **"🚀 Procesează Podcast"**
- Urmărește progresul în timp real
- Clipurile generate vor fi salvate în directorul de ieșire

## 🔧 Arhitectura Tehnică

### Componente Principale

1. **PodcastClipperService** (`app/services/podcast_clipper_service.py`)
   - Serviciul principal pentru procesarea podcast-urilor
   - Integrează toate componentele AI și de procesare video

2. **UI Component** (`webui/components/podcast_clipper_ui.py`)
   - Interfața Streamlit pentru configurare și control
   - Gestionează încărcarea fișierelor și setările utilizatorului

3. **Models Directory** (`models/`)
   - Conține fișierele YOLO pentru detectarea persoanelor
   - Modelele Whisper sunt descărcate automat la prima utilizare

### Fluxul de Procesare

```
Video Input → Person Detection → Speaker Diarization → Transcription → Clip Generation → Caption Addition → Final Output
```

1. **Detectare Persoane**: YOLO identifică persoanele în primul frame
2. **Diarizare Vorbitori**: Analizează audio-ul pentru separarea vorbitorilor
3. **Transcripție**: WhisperX generează transcriptul cu timing precis
4. **Generare Clipuri**: Creează clipuri focalizate pe vorbitorul activ
5. **Adăugare Subtitrări**: Aplică subtitrări stilizate cu sincronizare perfectă

## 📊 Performanță și Optimizare

### Cerințe de Sistem
- **CPU**: Intel i5/AMD Ryzen 5 sau superior
- **RAM**: Minim 8GB, recomandat 16GB+
- **GPU**: NVIDIA cu CUDA (opțional, pentru accelerare)
- **Spațiu**: ~2GB pentru modele și dependințe

### Optimizări
- **Procesare batch**: Procesează multiple segmente simultan
- **Cache inteligent**: Reutilizează rezultatele pentru eficiență
- **Compresie adaptivă**: Optimizează calitatea vs. dimensiunea fișierului
- **Procesare asincronă**: UI responsiv în timpul procesării

## 🐛 Depanare

### Probleme Comune

#### 1. Eroare la încărcarea modelului YOLO
```
❌ Nu s-au putut încărca fișierele YOLO
```
**Soluție**: Rulează `python install_podcast_clipper.py` pentru a descărca fișierele.

#### 2. Eroare de memorie
```
❌ CUDA out of memory
```
**Soluție**: Reduce rezoluția video sau folosește procesarea CPU.

#### 3. Calitate slabă a detectării
```
⚠️ Nu s-au detectat persoane
```
**Soluție**: Reduce pragul de încredere sau verifică calitatea video-ului.

### Loguri și Debugging
- Logurile sunt afișate în timp real în interfață
- Pentru debugging avansat, verifică fișierele de log din `logs/`

## 🔮 Dezvoltări Viitoare

### Funcționalități Planificate
- **🎭 Detectare emoții**: Analiză sentiment pentru clipuri mai captivante
- **📊 Analiză trending**: Optimizare bazată pe conținutul viral
- **🌐 Suport multilingv**: Transcripție și subtitrări în multiple limbi
- **🤖 AI Director**: Selecție automată a celor mai bune momente
- **📱 Export direct**: Încărcare automată pe platforme social media

### Îmbunătățiri Tehnice
- **⚡ Procesare în timp real**: Stream processing pentru podcast-uri live
- **🔄 Procesare distribuită**: Suport pentru multiple GPU-uri
- **📈 Analiză performanță**: Metrici detaliate pentru optimizare
- **🎨 Efecte avansate**: Tranziții și animații pentru clipuri

## 📞 Suport

Pentru probleme sau întrebări:
1. Verifică această documentație
2. Rulează scriptul de verificare: `python install_podcast_clipper.py`
3. Consultă logurile pentru erori detaliate
4. Raportează probleme în repository-ul GitHub

## 📄 Licență

Această funcționalitate este parte din MoneyPrinterTurbo și urmează aceeași licență.

---

**🎉 Bucură-te de generarea automată de clipuri din podcast-uri cu Podcast Clipper!**
