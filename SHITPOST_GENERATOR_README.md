# 🤖 Enhanced Shitpost Video Generator

An AI-powered shitpost video generator that creates viral meme videos using GPT4Free for text generation and multiple free AI image generation services.

## 🌟 Features

### Core Functionality
- **GPT4Free Integration**: Generate hilarious, meme-style text content using free AI models
- **Multiple AI Image Services**: Support for Pollinations, Hugging Face, and local Stable Diffusion
- **Advanced Video Effects**: Chaotic visual effects with customizable intensity levels
- **Romanian Culture Support**: Specialized templates and humor for Romanian audiences
- **Batch Generation**: Create multiple videos with varied parameters
- **Command Line Interface**: Full CLI support for automation and scripting

### AI-Powered Features
- **Smart Prompt Engineering**: Optimized prompts for maximum viral potential
- **Style-Aware Generation**: Different AI behaviors for various meme styles
- **Chaos Level Control**: Adjustable absurdity from 1 (mild) to 10 (interdimensional chaos)
- **Cultural Context**: Romanian memes, gaming references, philosophical absurdity
- **Viral Optimization**: Content specifically tuned for social media engagement

### Technical Features
- **Free AI Services**: No API keys required for basic functionality
- **Intelligent Caching**: Avoid regenerating similar content
- **Fallback Systems**: Multiple AI services ensure reliability
- **Performance Monitoring**: Track generation times and success rates
- **Error Handling**: Robust error recovery and logging

## 🚀 Quick Start

### Web Interface
1. Start the web interface:
   ```bash
   python webui.bat  # Windows
   sh webui.sh       # Linux/Mac
   ```

2. Navigate to the "😂 Shitpost Generator" tab

3. Choose your chaos level and theme, then click generate!

### Command Line Interface
```bash
# Generate a single shitpost
python shitpost_cli.py generate --theme romanian --chaos 8 --duration 15

# Generate multiple videos
python shitpost_cli.py batch --count 5 --theme gaming --chaos-range 6 9

# AI-powered generation with custom prompt
python shitpost_cli.py ai-generate --prompt "confused romanian guy at 3am" --style absurd

# Test AI services
python shitpost_cli.py config --test-services
```

## 📋 Installation

### Prerequisites
- Python 3.8+
- FFmpeg (for video processing)
- ImageMagick (for text effects)

### Basic Installation
```bash
# Clone the repository
git clone https://github.com/harry0703/MoneyPrinterTurbo.git
cd MoneyPrinterTurbo

# Install dependencies
pip install -r requirements.txt

# Optional: Install additional AI dependencies
pip install aiohttp psutil
```

### Optional AI Services Setup

#### GPT4Free (Recommended)
```bash
pip install -U g4f[all]
```

#### Hugging Face (Optional)
```bash
pip install huggingface-hub transformers
# Set environment variable (optional, works without token but with limits)
export HUGGINGFACE_API_TOKEN="your_token_here"
```

#### Local Stable Diffusion (Advanced)
1. Install [AUTOMATIC1111 WebUI](https://github.com/AUTOMATIC1111/stable-diffusion-webui)
2. Start with `--api` flag
3. The generator will automatically detect and use it

## 🎨 Usage Examples

### Themes and Styles

#### Romanian Memes
```python
# Web interface: Select "🇷🇴 Romanian Memes" tab
# CLI:
python shitpost_cli.py generate --theme romanian --chaos 7
```
**Example Output**: "Bunica: 'Mănâncă că ești slab' / Eu cu 90kg: 'Dar bunico...' / Bunica: 'PIELE ȘI OS!'"

#### Gaming Shitposts
```python
# Web interface: Select "🎮 Gaming Shitpost" tab  
# CLI:
python shitpost_cli.py generate --theme gaming --chaos 6
```
**Example Output**: "Când mori pentru a 47-a oară la același boss / Dark Souls players: 'This is fine'"

#### Philosophical Absurdity
```python
# Web interface: Select "🧠 Big Brain" tab
# CLI:
python shitpost_cli.py generate --theme philosophical --chaos 8
```
**Example Output**: "Dacă timpul este bani / Atunci ceasurile sunt bănci / *galaxy brain intensifies*"

#### Pure Chaos
```python
# Web interface: Select "🎲 Random Chaos" tab
# CLI:
python shitpost_cli.py generate --theme chaos --chaos 10
```
**Example Output**: "Covrigul cosmic + dansează breakdance = Universul se resetează"

### Chaos Levels Explained

| Level | Description | Example Effects |
|-------|-------------|-----------------|
| 1-2   | Mild humor, relatable | Clean visuals, simple text |
| 3-4   | Moderately absurd | Some visual distortion, random elements |
| 5-6   | Chaotic energy | Color distortion, rotation, zoom effects |
| 7-8   | Deep fried memes | Extreme saturation, multiple effects |
| 9-10  | Interdimensional chaos | Reality-breaking visuals, maximum absurdity |

### AI-Powered Generation

#### Using GPT4Free for Text
```python
# The system automatically uses GPT4Free when available
# Prompts are optimized for each theme:

# Romanian theme prompt:
"Creează un meme românesc viral cu umor local și referințe culturale..."

# Gaming theme prompt:  
"Creează un meme gaming viral cu referințe la jocuri populare..."
```

#### Free AI Image Generation
```python
# Pollinations.ai (completely free)
# Automatically enhances prompts for meme generation
# Example: "confused guy" becomes "confused guy, internet meme style, viral content, high quality"

# Hugging Face Inference (free with limits)
# Uses Stable Diffusion models
# Fallback when Pollinations is unavailable
```

## 🔧 Configuration

### Basic Configuration
Edit `config.toml`:
```toml
[app]
# Enable/disable AI features
stable_diffusion_enabled = true
gpt4free_enabled = true

# Shitpost generator settings
default_chaos_level = 7
default_duration = 15
enable_ai_images = true
```

### Environment Variables
```bash
# Optional API tokens
export HUGGINGFACE_API_TOKEN="your_token"
export OPENAI_API_KEY="your_key"  # For premium features

# Performance settings
export SHITPOST_CACHE_ENABLED=true
export SHITPOST_MAX_RETRIES=3
```

## 📱 Social Media Integration

### Optimal Settings by Platform

#### TikTok/Instagram Reels
```bash
python shitpost_cli.py generate --theme romanian --chaos 8 --duration 15
# Vertical format (9:16) automatically applied
# High chaos for maximum engagement
```

#### YouTube Shorts
```bash
python shitpost_cli.py generate --theme gaming --chaos 6 --duration 30
# Slightly longer duration
# Gaming content performs well
```

#### Twitter/X
```bash
python shitpost_cli.py generate --theme philosophical --chaos 7 --duration 10
# Short, punchy content
# Big brain memes for engagement
```

### Hashtag Generation
The CLI can generate optimized hashtags:
```bash
python shitpost_cli.py ai-generate --prompt "your content" --style romanian
# Automatically suggests: #romania #meme #viral #shitpost #bunica
```

## 🎯 Advanced Features

### Batch Generation
```bash
# Generate 10 videos with varied parameters
python shitpost_cli.py batch \
  --count 10 \
  --theme random \
  --chaos-range 5 9 \
  --duration-range 10 20 \
  --output-dir ./viral_content/
```

### Custom AI Prompts
```bash
# Use specific prompts for image and text generation
python shitpost_cli.py ai-generate \
  --prompt "Romanian grandmother discovers TikTok" \
  --image-prompt "confused elderly woman with smartphone, meme style" \
  --style romanian \
  --chaos 8
```

### Performance Monitoring
```bash
# Check system status and AI service availability
python shitpost_cli.py status --verbose

# Test all AI services
python shitpost_cli.py config --test-services
```

## 🐛 Troubleshooting

### Common Issues

#### "GPT4Free not available"
```bash
# Install GPT4Free
pip install -U g4f[all]

# Test installation
python -c "import g4f; print('GPT4Free installed successfully')"
```

#### "AI Image generation failed"
```bash
# Check internet connection (required for Pollinations)
# Verify Hugging Face token (if using)
# Check if local Stable Diffusion is running (if configured)

# Test services individually
python shitpost_cli.py config --test-services
```

#### "Video generation failed"
```bash
# Check FFmpeg installation
ffmpeg -version

# Check ImageMagick installation  
magick -version

# Verify storage directory permissions
ls -la storage/videos/
```

### Performance Optimization

#### For Low-End Systems
```toml
[app]
# Reduce video resolution
video_width = 720
video_height = 1280

# Disable AI images if too slow
enable_ai_images = false

# Reduce chaos effects
max_chaos_effects = 3
```

#### For High-End Systems
```toml
[app]
# Enable all features
stable_diffusion_enabled = true
enable_ai_images = true
enable_batch_processing = true

# Higher quality settings
video_width = 1080  
video_height = 1920
max_chaos_effects = 6
```

## 🤝 Contributing

### Adding New Themes
1. Edit `app/services/shitpost_generator.py`
2. Add theme to `enhanced_themes` dictionary
3. Create corresponding templates in `_load_text_templates()`
4. Add word banks in `_load_word_banks()`

### Adding New AI Services
1. Create service class in `app/services/free_ai_services.py`
2. Implement `generate_image()` method
3. Add to `FreeAIImageGenerator.services` list
4. Test with `python shitpost_cli.py config --test-services`

### Adding New Effects
1. Add effect function to `ShitpostGenerator` class
2. Follow naming pattern: `effect_name(self, clip, intensity)`
3. Add effect name to `chaos_effects` list
4. Test with different chaos levels

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **MoneyPrinterTurbo**: Base video generation framework
- **GPT4Free**: Free access to premium AI models
- **Pollinations.ai**: Free AI image generation
- **Hugging Face**: Open-source AI model hosting
- **AUTOMATIC1111**: Stable Diffusion WebUI

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/harry0703/MoneyPrinterTurbo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/harry0703/MoneyPrinterTurbo/discussions)
- **Documentation**: This README and inline code comments

---

**Happy Shitposting! 🚀😂**
