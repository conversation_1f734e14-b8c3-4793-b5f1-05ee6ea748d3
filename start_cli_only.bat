@echo off
echo 🤖 CLI-Only Shitpost Generator (No GUI Issues)
echo.

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
)

echo Available commands:
echo   generate --theme romanian --chaos 8
echo   batch --count 5 --theme gaming
echo   config --test-services
echo.

if "%1"=="" (
    set /p command="Enter command (or 'help' for options): "
    python shitpost_cli.py %command%
) else (
    python shitpost_cli.py %*
)

pause
