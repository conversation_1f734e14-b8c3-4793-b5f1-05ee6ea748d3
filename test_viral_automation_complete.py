#!/usr/bin/env python3
"""
Test Complete Viral Automation System

Testează toate serviciile de automatizare virală implementate.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))


async def test_all_viral_services():
    """Testează toate serviciile de automatizare virală"""
    print("🧪 Testing All Viral Automation Services...")
    print("=" * 50)
    
    results = {}
    
    # Test 1: Viral Content Generator
    print("1️⃣ Testing Viral Content Generator...")
    try:
        from app.services.viral_content_generator import test_viral_content_generator
        result1 = await test_viral_content_generator()
        results["viral_content_generator"] = result1
        print(f"   Result: {'✅ PASSED' if result1 else '❌ FAILED'}")
    except Exception as e:
        results["viral_content_generator"] = False
        print(f"   Result: ❌ FAILED - {e}")
    
    print()
    
    # Test 2: Contextual Image AI
    print("2️⃣ Testing Contextual Image AI...")
    try:
        from app.services.contextual_image_ai import test_contextual_image_ai
        result2 = await test_contextual_image_ai()
        results["contextual_image_ai"] = result2
        print(f"   Result: {'✅ PASSED' if result2 else '❌ FAILED'}")
    except Exception as e:
        results["contextual_image_ai"] = False
        print(f"   Result: ❌ FAILED - {e}")
    
    print()
    
    # Test 3: One-Click Viral Generator
    print("3️⃣ Testing One-Click Viral Generator...")
    try:
        from app.services.one_click_viral_generator import test_one_click_viral_generator
        result3 = await test_one_click_viral_generator()
        results["one_click_viral_generator"] = result3
        print(f"   Result: {'✅ PASSED' if result3 else '❌ FAILED'}")
    except Exception as e:
        results["one_click_viral_generator"] = False
        print(f"   Result: ❌ FAILED - {e}")
    
    print()
    
    # Test 4: Romanian Content Optimizer
    print("4️⃣ Testing Romanian Content Optimizer...")
    try:
        from app.services.romanian_content_optimizer import test_romanian_content_optimizer
        result4 = await test_romanian_content_optimizer()
        results["romanian_content_optimizer"] = result4
        print(f"   Result: {'✅ PASSED' if result4 else '❌ FAILED'}")
    except Exception as e:
        results["romanian_content_optimizer"] = False
        print(f"   Result: ❌ FAILED - {e}")
    
    print()
    
    # Test 5: Performance Analyzer
    print("5️⃣ Testing Performance Analyzer...")
    try:
        from app.services.performance_analyzer import test_performance_analyzer
        result5 = await test_performance_analyzer()
        results["performance_analyzer"] = result5
        print(f"   Result: {'✅ PASSED' if result5 else '❌ FAILED'}")
    except Exception as e:
        results["performance_analyzer"] = False
        print(f"   Result: ❌ FAILED - {e}")
    
    print()
    print("=" * 50)
    print("📊 FINAL RESULTS:")
    print("=" * 50)
    
    passed_count = sum(1 for result in results.values() if result)
    total_count = len(results)
    
    for service_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"  {status} {service_name.replace('_', ' ').title()}")
    
    print()
    print(f"📈 Success Rate: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 ALL VIRAL AUTOMATION SERVICES ARE WORKING!")
        print("🚀 MoneyPrinterTurbo is ready for complete viral automation!")
    elif passed_count > 0:
        print("⚠️ Some services are working, but there are issues to resolve.")
    else:
        print("❌ No services are working. Check GPT4Free installation.")
    
    return passed_count == total_count


def main():
    """Funcția principală"""
    try:
        success = asyncio.run(test_all_viral_services())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
