"""
Free AI Services Integration
Provides access to multiple free AI services for image generation and text processing
"""

import os
import asyncio
import aiohttp
import requests
import base64
import hashlib
import time
import random
from typing import Optional, Dict, Any, List, Union
from PIL import Image
from io import BytesIO
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class AIServiceConfig:
    """Configuration for AI services"""
    timeout: int = 30
    max_retries: int = 3
    cache_enabled: bool = True
    rate_limit_delay: float = 1.0

class PollinationsAPI:
    """Pollinations.ai free image generation service"""
    
    def __init__(self, config: AIServiceConfig = None):
        self.config = config or AIServiceConfig()
        self.base_url = "https://image.pollinations.ai/prompt"
        self.rate_limiter = {}
    
    async def _rate_limit(self):
        """Simple rate limiting"""
        now = time.time()
        if 'last_call' in self.rate_limiter:
            elapsed = now - self.rate_limiter['last_call']
            if elapsed < self.config.rate_limit_delay:
                await asyncio.sleep(self.config.rate_limit_delay - elapsed)
        self.rate_limiter['last_call'] = time.time()
    
    async def generate_image(self, prompt: str, width: int = 512, height: int = 512, 
                           style: str = "meme", seed: int = None) -> Optional[bytes]:
        """Generate image using Pollinations API"""
        try:
            await self._rate_limit()
            
            # Enhance prompt for meme generation
            enhanced_prompt = self._enhance_prompt_for_memes(prompt, style)
            
            # Build URL with parameters
            params = {
                "width": width,
                "height": height,
                "nologo": "true",
                "enhance": "true"
            }
            
            if seed:
                params["seed"] = seed
            
            # URL encode the prompt
            import urllib.parse
            encoded_prompt = urllib.parse.quote(enhanced_prompt)
            url = f"{self.base_url}/{encoded_prompt}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=self.config.timeout) as response:
                    if response.status == 200:
                        image_data = await response.read()
                        logger.info(f"Generated image with Pollinations: {len(image_data)} bytes")
                        return image_data
                    else:
                        logger.warning(f"Pollinations API returned status {response.status}")
                        
        except Exception as e:
            logger.error(f"Pollinations generation failed: {e}")
        
        return None
    
    def _enhance_prompt_for_memes(self, prompt: str, style: str) -> str:
        """Enhance prompt specifically for meme generation"""
        style_modifiers = {
            "meme": "internet meme style, viral content, social media",
            "absurd": "surreal, absurd, chaotic, deep fried meme aesthetic",
            "classic": "classic meme format, clean, simple, recognizable",
            "romanian": "romanian culture, balkan humor, eastern european",
            "gaming": "gaming meme, gamer culture, video game reference",
            "chaos": "chaotic energy, random, nonsensical, fever dream"
        }
        
        modifier = style_modifiers.get(style, style_modifiers["meme"])
        enhanced = f"{prompt}, {modifier}, high quality, clear, vibrant colors"
        
        return enhanced

class HuggingFaceInference:
    """Hugging Face Inference API for free AI services"""
    
    def __init__(self, config: AIServiceConfig = None):
        self.config = config or AIServiceConfig()
        self.api_token = os.getenv('HUGGINGFACE_API_TOKEN')  # Optional, works without token but with limits
        self.base_url = "https://api-inference.huggingface.co/models"
        
    async def generate_image(self, prompt: str, model: str = "stabilityai/stable-diffusion-2-1") -> Optional[bytes]:
        """Generate image using Hugging Face Inference API"""
        try:
            headers = {}
            if self.api_token:
                headers["Authorization"] = f"Bearer {self.api_token}"
            
            payload = {"inputs": prompt}
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/{model}",
                    headers=headers,
                    json=payload,
                    timeout=self.config.timeout
                ) as response:
                    if response.status == 200:
                        image_data = await response.read()
                        logger.info(f"Generated image with HuggingFace: {len(image_data)} bytes")
                        return image_data
                    else:
                        logger.warning(f"HuggingFace API returned status {response.status}")
                        
        except Exception as e:
            logger.error(f"HuggingFace generation failed: {e}")
        
        return None
    
    async def generate_text(self, prompt: str, model: str = "microsoft/DialoGPT-medium") -> Optional[str]:
        """Generate text using Hugging Face models"""
        try:
            headers = {}
            if self.api_token:
                headers["Authorization"] = f"Bearer {self.api_token}"
            
            payload = {"inputs": prompt}
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/{model}",
                    headers=headers,
                    json=payload,
                    timeout=self.config.timeout
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if isinstance(result, list) and len(result) > 0:
                            return result[0].get('generated_text', '')
                        
        except Exception as e:
            logger.error(f"HuggingFace text generation failed: {e}")
        
        return None

class PerchanceAPI:
    """Perchance.org AI image generation (unofficial API)"""
    
    def __init__(self, config: AIServiceConfig = None):
        self.config = config or AIServiceConfig()
        self.session = None
    
    async def generate_image(self, prompt: str, style: str = "anime") -> Optional[bytes]:
        """Generate image using Perchance (requires web scraping approach)"""
        # Note: This is a simplified implementation
        # In practice, you might need to use selenium or similar for full functionality
        try:
            # This is a placeholder - actual implementation would require
            # more complex web interaction with Perchance
            logger.info("Perchance integration requires web scraping - placeholder implementation")
            return None
            
        except Exception as e:
            logger.error(f"Perchance generation failed: {e}")
        
        return None

class FreeAIImageGenerator:
    """Unified interface for multiple free AI image generation services"""
    
    def __init__(self, config: AIServiceConfig = None):
        self.config = config or AIServiceConfig()
        self.pollinations = PollinationsAPI(config)
        self.huggingface = HuggingFaceInference(config)
        self.perchance = PerchanceAPI(config)
        
        # Service priority order (most reliable first)
        self.services = [
            ("pollinations", self.pollinations),
            ("huggingface", self.huggingface),
            ("perchance", self.perchance)
        ]
        
        # Cache for generated images
        self.cache = {}
        self.cache_dir = "storage/ai_images"
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def _get_cache_key(self, prompt: str, width: int, height: int, style: str) -> str:
        """Generate cache key for image"""
        cache_string = f"{prompt}_{width}_{height}_{style}"
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    async def generate_meme_image(self, prompt: str, width: int = 512, height: int = 512,
                                 style: str = "meme", chaos_level: int = 5) -> Optional[bytes]:
        """Generate meme image with fallback across multiple services"""
        
        # Check cache first
        if self.config.cache_enabled:
            cache_key = self._get_cache_key(prompt, width, height, style)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.jpg")
            
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, 'rb') as f:
                        logger.info(f"Using cached image for: {prompt[:50]}...")
                        return f.read()
                except Exception as e:
                    logger.error(f"Error reading cached image: {e}")
        
        # Enhance prompt based on chaos level
        enhanced_prompt = self._enhance_prompt_for_chaos(prompt, style, chaos_level)
        
        # Try each service in order
        for service_name, service in self.services:
            try:
                logger.info(f"Trying {service_name} for image generation...")
                
                if service_name == "pollinations":
                    image_data = await service.generate_image(enhanced_prompt, width, height, style)
                elif service_name == "huggingface":
                    image_data = await service.generate_image(enhanced_prompt)
                elif service_name == "perchance":
                    image_data = await service.generate_image(enhanced_prompt, style)
                else:
                    continue
                
                if image_data:
                    # Cache successful generation
                    if self.config.cache_enabled:
                        try:
                            with open(cache_file, 'wb') as f:
                                f.write(image_data)
                            logger.info(f"Cached image from {service_name}")
                        except Exception as e:
                            logger.error(f"Error caching image: {e}")
                    
                    return image_data
                    
            except Exception as e:
                logger.warning(f"Service {service_name} failed: {e}")
                continue
        
        logger.error("All free AI image services failed")
        return None
    
    def _enhance_prompt_for_chaos(self, prompt: str, style: str, chaos_level: int) -> str:
        """Enhance prompt based on chaos level for maximum meme potential"""
        
        chaos_modifiers = {
            1: "clean, simple",
            2: "slightly exaggerated", 
            3: "moderately chaotic",
            4: "absurd elements",
            5: "surreal, chaotic",
            6: "very chaotic, oversaturated",
            7: "extremely chaotic, deep fried",
            8: "fever dream, nonsensical",
            9: "reality-breaking, impossible",
            10: "interdimensional chaos, maximum absurdity"
        }
        
        chaos_modifier = chaos_modifiers.get(chaos_level, chaos_modifiers[5])
        
        # Add style-specific enhancements
        if style == "romanian":
            cultural_elements = "romanian culture, balkan humor, eastern european vibes"
            enhanced = f"{prompt}, {cultural_elements}, {chaos_modifier}"
        elif style == "gaming":
            gaming_elements = "gaming culture, video game aesthetic, gamer meme"
            enhanced = f"{prompt}, {gaming_elements}, {chaos_modifier}"
        else:
            enhanced = f"{prompt}, {chaos_modifier}, meme style"
        
        # Add quality and format specifications
        enhanced += ", high quality, clear, vibrant colors, internet meme format"
        
        return enhanced
    
    def apply_meme_effects(self, image_data: bytes, chaos_level: int) -> bytes:
        """Apply meme-style effects to generated image"""
        try:
            image = Image.open(BytesIO(image_data))
            
            # Apply effects based on chaos level
            if chaos_level >= 7:
                # Deep fried effect
                from PIL import ImageEnhance, ImageFilter
                
                # Increase contrast and saturation
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(2.0)
                
                enhancer = ImageEnhance.Color(image)
                image = enhancer.enhance(2.5)
                
                # Add some noise/grain effect
                image = image.filter(ImageFilter.SHARPEN)
                
            elif chaos_level >= 4:
                # Moderate enhancement
                from PIL import ImageEnhance
                
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(1.5)
                
                enhancer = ImageEnhance.Color(image)
                image = enhancer.enhance(1.3)
            
            # Convert back to bytes
            output = BytesIO()
            image.save(output, format='JPEG', quality=85)
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error applying meme effects: {e}")
            return image_data
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get status of all AI services"""
        return {
            "pollinations": {"available": True, "free": True, "rate_limited": True},
            "huggingface": {"available": True, "free": True, "rate_limited": True},
            "perchance": {"available": False, "free": True, "note": "Requires web scraping"},
            "cache_enabled": self.config.cache_enabled,
            "cache_dir": self.cache_dir
        }

# Global instance
free_ai_generator = FreeAIImageGenerator()
