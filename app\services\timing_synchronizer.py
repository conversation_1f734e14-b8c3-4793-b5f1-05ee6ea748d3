"""
Timing Synchronization System for Contextual Images

This service synchronizes contextually generated images with audio narration
and subtitles to create coherent video sequences.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import json

from app.services.contextual_image_generator import ContextualImage

logger = logging.getLogger(__name__)


@dataclass
class SubtitleSegment:
    """Represents a subtitle segment with timing"""
    index: int
    start_time: float
    end_time: float
    text: str
    duration: float


@dataclass
class SynchronizedSegment:
    """Represents a synchronized segment with image, audio, and subtitle timing"""
    start_time: float
    end_time: float
    duration: float
    image_path: Optional[str]
    subtitle_text: str
    script_context: str
    transition_type: str = "fade"
    priority: float = 0.5


class TimingSynchronizer:
    """Synchronizes contextual images with audio and subtitles"""
    
    def __init__(self):
        self.subtitle_segments = []
        self.contextual_images = []
        self.synchronized_segments = []
        
    def load_subtitles(self, subtitle_file_path: str) -> List[SubtitleSegment]:
        """Load and parse subtitle file"""
        try:
            with open(subtitle_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            segments = self._parse_srt_content(content)
            self.subtitle_segments = segments
            logger.info(f"Loaded {len(segments)} subtitle segments")
            return segments
            
        except Exception as e:
            logger.error(f"Failed to load subtitles: {e}")
            return []

    def _parse_srt_content(self, content: str) -> List[SubtitleSegment]:
        """Parse SRT subtitle content"""
        segments = []
        
        # Split by subtitle blocks
        blocks = re.split(r'\n\s*\n', content.strip())
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) < 3:
                continue
                
            try:
                # Parse index
                index = int(lines[0])
                
                # Parse timing
                timing_line = lines[1]
                time_match = re.match(r'(\d{2}):(\d{2}):(\d{2}),(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2}),(\d{3})', timing_line)
                
                if not time_match:
                    continue
                
                start_time = self._time_to_seconds(
                    int(time_match.group(1)),  # hours
                    int(time_match.group(2)),  # minutes
                    int(time_match.group(3)),  # seconds
                    int(time_match.group(4))   # milliseconds
                )
                
                end_time = self._time_to_seconds(
                    int(time_match.group(5)),  # hours
                    int(time_match.group(6)),  # minutes
                    int(time_match.group(7)),  # seconds
                    int(time_match.group(8))   # milliseconds
                )
                
                # Parse text
                text = '\n'.join(lines[2:]).strip()
                
                segment = SubtitleSegment(
                    index=index,
                    start_time=start_time,
                    end_time=end_time,
                    text=text,
                    duration=end_time - start_time
                )
                
                segments.append(segment)
                
            except Exception as e:
                logger.warning(f"Failed to parse subtitle block: {e}")
                continue
        
        return segments

    def _time_to_seconds(self, hours: int, minutes: int, seconds: int, milliseconds: int) -> float:
        """Convert time components to total seconds"""
        return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0

    def synchronize_with_images(self, contextual_images: List[ContextualImage]) -> List[SynchronizedSegment]:
        """Synchronize contextual images with subtitle timing"""
        self.contextual_images = contextual_images
        
        if not self.subtitle_segments:
            logger.warning("No subtitle segments available for synchronization")
            return self._create_image_only_segments(contextual_images)
        
        synchronized_segments = []
        
        # Create segments based on subtitle timing
        for subtitle in self.subtitle_segments:
            # Find the best matching contextual image for this subtitle
            best_image = self._find_best_matching_image(subtitle, contextual_images)
            
            segment = SynchronizedSegment(
                start_time=subtitle.start_time,
                end_time=subtitle.end_time,
                duration=subtitle.duration,
                image_path=best_image.image_path if best_image else None,
                subtitle_text=subtitle.text,
                script_context=best_image.script_text if best_image else "",
                transition_type=self._determine_transition_type(subtitle, best_image),
                priority=best_image.priority if best_image else 0.3
            )
            
            synchronized_segments.append(segment)
        
        # Fill gaps with contextual images that don't have matching subtitles
        synchronized_segments = self._fill_gaps_with_images(synchronized_segments, contextual_images)
        
        # Optimize timing for smooth transitions
        synchronized_segments = self._optimize_transitions(synchronized_segments)
        
        self.synchronized_segments = synchronized_segments
        logger.info(f"Created {len(synchronized_segments)} synchronized segments")
        
        return synchronized_segments

    def _find_best_matching_image(self, subtitle: SubtitleSegment, 
                                images: List[ContextualImage]) -> Optional[ContextualImage]:
        """Find the best matching contextual image for a subtitle segment"""
        if not images:
            return None
        
        best_image = None
        best_score = 0.0
        
        for image in images:
            score = self._calculate_matching_score(subtitle, image)
            if score > best_score:
                best_score = score
                best_image = image
        
        # Only return image if score is above threshold
        return best_image if best_score > 0.3 else None

    def _calculate_matching_score(self, subtitle: SubtitleSegment, 
                                image: ContextualImage) -> float:
        """Calculate how well a subtitle matches a contextual image"""
        score = 0.0
        
        # Time overlap score (0.0 to 0.4)
        overlap = self._calculate_time_overlap(
            subtitle.start_time, subtitle.end_time,
            image.start_time, image.end_time
        )
        score += overlap * 0.4
        
        # Text similarity score (0.0 to 0.3)
        text_similarity = self._calculate_text_similarity(subtitle.text, image.script_text)
        score += text_similarity * 0.3
        
        # Priority score (0.0 to 0.3)
        score += image.priority * 0.3
        
        return score

    def _calculate_time_overlap(self, start1: float, end1: float, 
                              start2: float, end2: float) -> float:
        """Calculate time overlap between two segments (0.0 to 1.0)"""
        overlap_start = max(start1, start2)
        overlap_end = min(end1, end2)
        
        if overlap_end <= overlap_start:
            return 0.0
        
        overlap_duration = overlap_end - overlap_start
        total_duration = max(end1, end2) - min(start1, start2)
        
        return overlap_duration / total_duration if total_duration > 0 else 0.0

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate text similarity between subtitle and image context"""
        if not text1 or not text2:
            return 0.0
        
        # Simple word overlap calculation
        words1 = set(re.findall(r'\b\w+\b', text1.lower()))
        words2 = set(re.findall(r'\b\w+\b', text2.lower()))
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0

    def _determine_transition_type(self, subtitle: SubtitleSegment, 
                                 image: Optional[ContextualImage]) -> str:
        """Determine the best transition type for a segment"""
        if not image:
            return "fade"
        
        # Determine transition based on scene type and emotional tone
        if image.scene_type == "battle":
            return "cut"  # Quick cuts for action
        elif image.scene_type == "dialogue":
            return "fade"  # Smooth fades for dialogue
        elif image.emotional_tone == "dramatic":
            return "slide"  # Dramatic slides
        elif image.emotional_tone == "peaceful":
            return "dissolve"  # Gentle dissolves
        else:
            return "fade"  # Default

    def _create_image_only_segments(self, images: List[ContextualImage]) -> List[SynchronizedSegment]:
        """Create segments when no subtitles are available"""
        segments = []
        
        for image in images:
            segment = SynchronizedSegment(
                start_time=image.start_time,
                end_time=image.end_time,
                duration=image.duration,
                image_path=image.image_path,
                subtitle_text="",
                script_context=image.script_text,
                transition_type="fade",
                priority=image.priority
            )
            segments.append(segment)
        
        return segments

    def _fill_gaps_with_images(self, segments: List[SynchronizedSegment], 
                             images: List[ContextualImage]) -> List[SynchronizedSegment]:
        """Fill timing gaps with unused contextual images"""
        # Find images that haven't been used
        used_images = {seg.image_path for seg in segments if seg.image_path}
        unused_images = [img for img in images if img.image_path not in used_images]
        
        if not unused_images:
            return segments
        
        # Find gaps in timing
        segments.sort(key=lambda x: x.start_time)
        filled_segments = []
        
        for i, segment in enumerate(segments):
            filled_segments.append(segment)
            
            # Check for gap after this segment
            next_start = segments[i + 1].start_time if i + 1 < len(segments) else float('inf')
            gap_start = segment.end_time
            gap_duration = next_start - gap_start
            
            # If there's a significant gap, try to fill it with an unused image
            if gap_duration > 2.0 and unused_images:  # 2 second minimum gap
                best_image = self._find_image_for_gap(gap_start, gap_duration, unused_images)
                if best_image:
                    gap_segment = SynchronizedSegment(
                        start_time=gap_start,
                        end_time=min(gap_start + best_image.duration, next_start),
                        duration=min(best_image.duration, gap_duration),
                        image_path=best_image.image_path,
                        subtitle_text="",
                        script_context=best_image.script_text,
                        transition_type="fade",
                        priority=best_image.priority
                    )
                    filled_segments.append(gap_segment)
                    unused_images.remove(best_image)
        
        return filled_segments

    def _find_image_for_gap(self, gap_start: float, gap_duration: float, 
                          images: List[ContextualImage]) -> Optional[ContextualImage]:
        """Find the best image to fill a timing gap"""
        if not images:
            return None
        
        # Prefer images with timing close to the gap
        best_image = None
        best_score = 0.0
        
        for image in images:
            # Calculate proximity score
            time_diff = abs(image.start_time - gap_start)
            proximity_score = max(0, 1.0 - time_diff / 10.0)  # 10 second window
            
            # Combine with priority
            total_score = proximity_score * 0.7 + image.priority * 0.3
            
            if total_score > best_score:
                best_score = total_score
                best_image = image
        
        return best_image

    def _optimize_transitions(self, segments: List[SynchronizedSegment]) -> List[SynchronizedSegment]:
        """Optimize segment transitions for smooth playback with no overlaps"""
        if len(segments) <= 1:
            return segments

        # Sort segments by start time
        segments.sort(key=lambda x: x.start_time)

        optimized = []

        for i, segment in enumerate(segments):
            # Skip segments without images
            if not segment.image_path:
                continue

            # Adjust timing to prevent gaps and overlaps
            if optimized:
                prev_segment = optimized[-1]

                # Check for overlap
                if segment.start_time < prev_segment.end_time:
                    # Calculate overlap duration
                    overlap = prev_segment.end_time - segment.start_time

                    # If overlap is significant, skip this segment or adjust timing
                    if overlap > segment.duration * 0.5:
                        logger.debug(f"Skipping overlapping segment at {segment.start_time:.1f}s (overlap: {overlap:.1f}s)")
                        continue
                    else:
                        # Adjust start time to avoid overlap
                        segment.start_time = prev_segment.end_time + 0.1  # Small gap
                        segment.duration = max(1.0, segment.end_time - segment.start_time)
                        segment.end_time = segment.start_time + segment.duration

                # Check for very short gaps (less than 0.5s) and extend previous segment
                elif segment.start_time - prev_segment.end_time < 0.5:
                    gap = segment.start_time - prev_segment.end_time
                    if gap > 0:
                        # Extend previous segment to fill small gap
                        prev_segment.end_time = segment.start_time
                        prev_segment.duration = prev_segment.end_time - prev_segment.start_time

            # Ensure minimum duration
            if segment.duration < 1.0:
                segment.duration = 1.0
                segment.end_time = segment.start_time + segment.duration

            # Ensure maximum duration (prevent very long overlays)
            if segment.duration > 8.0:
                segment.duration = 8.0
                segment.end_time = segment.start_time + segment.duration

            optimized.append(segment)
        
        return optimized

    def get_timeline_data(self) -> List[Dict[str, Any]]:
        """Get timeline data for video generation"""
        timeline = []
        
        for segment in self.synchronized_segments:
            timeline.append({
                'start_time': segment.start_time,
                'end_time': segment.end_time,
                'duration': segment.duration,
                'image_path': segment.image_path,
                'subtitle_text': segment.subtitle_text,
                'transition_type': segment.transition_type,
                'priority': segment.priority
            })
        
        return timeline

    def export_synchronization_data(self, output_path: str):
        """Export synchronization data to JSON file"""
        data = {
            'subtitle_segments': len(self.subtitle_segments),
            'contextual_images': len(self.contextual_images),
            'synchronized_segments': len(self.synchronized_segments),
            'timeline': self.get_timeline_data(),
            'coverage_stats': self._calculate_coverage_stats()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Synchronization data exported to {output_path}")

    def _calculate_coverage_stats(self) -> Dict[str, Any]:
        """Calculate coverage statistics"""
        if not self.synchronized_segments:
            return {}
        
        total_duration = max(seg.end_time for seg in self.synchronized_segments)
        covered_duration = sum(seg.duration for seg in self.synchronized_segments if seg.image_path)
        
        return {
            'total_duration': total_duration,
            'covered_duration': covered_duration,
            'coverage_percentage': (covered_duration / total_duration * 100) if total_duration > 0 else 0,
            'segments_with_images': sum(1 for seg in self.synchronized_segments if seg.image_path),
            'segments_without_images': sum(1 for seg in self.synchronized_segments if not seg.image_path)
        }
