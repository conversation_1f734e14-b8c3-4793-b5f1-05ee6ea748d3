"""
Advanced AI Video Generator
Automatically generates AI images based on script content with intelligent segmentation and synchronization
"""

import asyncio
import logging
import os
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import hashlib

from .script_analyzer import <PERSON>riptAnalyzer, ScriptSegment
from .ai_video_source_manager import AIVideoSourceManager, AIImageConfig, AIProvider
from .material import MaterialInfo
from ..models.schema import VideoAspect

logger = logging.getLogger(__name__)

@dataclass
class AIVideoConfig:
    """Configuration for AI video generation"""
    style: str = "realistic"
    provider: AIProvider = AIProvider.AUTO
    video_aspect: VideoAspect = VideoAspect.portrait
    max_clip_duration: int = 3  # Reduced from 5 to 3 for better coverage
    min_images: int = 3
    max_images: int = 25  # Increased from 10 to 25 to handle longer videos
    quality_threshold: float = 0.3
    enable_caching: bool = True
    fallback_enabled: bool = True

    @classmethod
    def create_for_audio_duration(cls, audio_duration: float, clip_duration: int = 3, **kwargs):
        """Create config optimized for specific audio duration"""
        import math
        # Calculate required images to cover full audio duration
        required_images = max(3, math.ceil(audio_duration / clip_duration) + 1)  # +1 for safety margin

        return cls(
            max_clip_duration=clip_duration,
            min_images=max(3, required_images),
            max_images=max(25, required_images + 2),  # Allow extra images for quality selection
            **kwargs
        )

@dataclass
class AIVideoResult:
    """Result from AI video generation"""
    success: bool
    materials: List[MaterialInfo] = None
    segments_analyzed: int = 0
    images_generated: int = 0
    total_generation_time: float = 0.0
    provider_stats: Dict[str, int] = None
    error_message: Optional[str] = None
    cache_hits: int = 0

class AIVideoGenerator:
    """Advanced AI video generator with script analysis and intelligent image generation"""
    
    def __init__(self):
        self.script_analyzer = ScriptAnalyzer()
        self.ai_manager = AIVideoSourceManager()
        self.cache_dir = "storage/ai_video_cache"
        os.makedirs(self.cache_dir, exist_ok=True)
        
    async def generate_video_materials(
        self,
        script: str,
        audio_duration: float,
        config: AIVideoConfig
    ) -> AIVideoResult:
        """Generate AI video materials based on script analysis"""
        start_time = time.time()
        
        try:
            logger.info(f"🎬 Starting AI video generation for {audio_duration:.1f}s video")
            
            # Step 1: Analyze script
            segments = self.script_analyzer.analyze_script(script, audio_duration)
            logger.info(f"📝 Analyzed script into {len(segments)} segments")
            
            # Step 2: Select segments for image generation
            selected_segments = self._select_segments_for_generation(segments, config, audio_duration)
            logger.info(f"🎯 Selected {len(selected_segments)} segments for image generation")
            
            # Step 3: Generate images for selected segments
            materials, provider_stats, cache_hits = await self._generate_images_for_segments(
                selected_segments, config
            )
            
            # Step 4: Create result
            result = AIVideoResult(
                success=len(materials) > 0,
                materials=materials,
                segments_analyzed=len(segments),
                images_generated=len(materials),
                total_generation_time=time.time() - start_time,
                provider_stats=provider_stats,
                cache_hits=cache_hits
            )
            
            if result.success:
                logger.info(f"✅ Generated {len(materials)} AI images in {result.total_generation_time:.2f}s")
                logger.info(f"📊 Provider stats: {provider_stats}")
                logger.info(f"💾 Cache hits: {cache_hits}")
            else:
                result.error_message = "No images could be generated"
                logger.error("❌ Failed to generate any AI images")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ AI video generation failed: {e}")
            return AIVideoResult(
                success=False,
                error_message=str(e),
                total_generation_time=time.time() - start_time
            )
    
    def _select_segments_for_generation(
        self,
        segments: List[ScriptSegment],
        config: AIVideoConfig,
        audio_duration: float
    ) -> List[ScriptSegment]:
        """Select which segments should have images generated"""
        
        # Calculate how many images we need
        target_images = min(
            max(config.min_images, int(audio_duration / config.max_clip_duration)),
            config.max_images
        )
        
        # Sort segments by priority score (highest first)
        sorted_segments = sorted(segments, key=lambda s: s.priority_score, reverse=True)
        
        # Filter segments that meet quality threshold
        quality_segments = [s for s in sorted_segments if s.priority_score >= config.quality_threshold]
        
        # If we don't have enough quality segments, include lower quality ones
        if len(quality_segments) < config.min_images:
            remaining_needed = config.min_images - len(quality_segments)
            lower_quality = [s for s in sorted_segments if s.priority_score < config.quality_threshold]
            quality_segments.extend(lower_quality[:remaining_needed])
        
        # Select final segments
        selected = quality_segments[:target_images]
        
        # Ensure we have at least minimum images
        if len(selected) < config.min_images and len(segments) > 0:
            # Add more segments if available
            remaining = [s for s in segments if s not in selected]
            selected.extend(remaining[:config.min_images - len(selected)])
        
        return selected
    
    async def _generate_images_for_segments(
        self,
        segments: List[ScriptSegment],
        config: AIVideoConfig
    ) -> Tuple[List[MaterialInfo], Dict[str, int], int]:
        """Generate AI images for selected segments"""
        
        materials = []
        provider_stats = {}
        cache_hits = 0
        
        # Configure AI generation
        ai_config = AIImageConfig()
        ai_config.to_video_aspect_resolution(config.video_aspect.name)
        ai_config.style = config.style
        ai_config.provider = config.provider
        
        for i, segment in enumerate(segments):
            try:
                logger.info(f"🎨 Generating image {i+1}/{len(segments)}: {segment.text[:50]}...")
                
                # Check cache first
                if config.enable_caching:
                    cached_material = self._get_cached_material(segment, config)
                    if cached_material:
                        materials.append(cached_material)
                        cache_hits += 1
                        logger.info(f"💾 Using cached image for segment {i+1}")
                        continue
                
                # Generate prompt from segment
                prompt = self.ai_manager.prompt_generator.generate_prompt_from_segment(segment, config.style)
                logger.debug(f"📝 Generated prompt: {prompt}")
                
                # Generate image
                result = await self.ai_manager.generate_image(prompt, ai_config)
                
                if result.success:
                    # Create material info
                    material = self._create_material_from_result(result, segment, i)
                    materials.append(material)
                    
                    # Update provider stats
                    provider = str(result.provider_used)
                    provider_stats[provider] = provider_stats.get(provider, 0) + 1
                    
                    # Cache result
                    if config.enable_caching:
                        self._cache_material(segment, config, material)
                    
                    logger.info(f"✅ Generated image {i+1} with {provider}")
                else:
                    logger.warning(f"❌ Failed to generate image {i+1}: {result.error_message}")
                    
                    # Try fallback if enabled
                    if config.fallback_enabled:
                        fallback_material = self._create_fallback_material(segment, i)
                        if fallback_material:
                            materials.append(fallback_material)
                            provider_stats["fallback"] = provider_stats.get("fallback", 0) + 1
                            logger.info(f"🔄 Using fallback for image {i+1}")
                
            except Exception as e:
                logger.error(f"❌ Error generating image {i+1}: {e}")
                
                # Try fallback
                if config.fallback_enabled:
                    fallback_material = self._create_fallback_material(segment, i)
                    if fallback_material:
                        materials.append(fallback_material)
                        provider_stats["fallback"] = provider_stats.get("fallback", 0) + 1
        
        return materials, provider_stats, cache_hits
    
    def _create_material_from_result(self, result, segment: ScriptSegment, index: int) -> MaterialInfo:
        """Create MaterialInfo from AI generation result"""
        
        # Save image to file
        filename = f"ai_generated_{index}_{int(time.time())}.png"
        filepath = os.path.join(self.cache_dir, filename)
        
        with open(filepath, 'wb') as f:
            f.write(result.image_data)
        
        # Create material info
        material = MaterialInfo()
        material.url = filepath
        material.duration = int(segment.duration)  # Convert to int
        material.provider = f"ai_{result.provider_used}"
        
        return material
    
    def _create_fallback_material(self, segment: ScriptSegment, index: int) -> Optional[MaterialInfo]:
        """Create fallback material when AI generation fails"""
        try:
            # This could create a simple colored image or use a default image
            # For now, return None to indicate no fallback available
            return None
        except Exception as e:
            logger.error(f"Failed to create fallback material: {e}")
            return None
    
    def _get_cache_key(self, segment: ScriptSegment, config: AIVideoConfig) -> str:
        """Generate cache key for segment and config"""
        content = f"{segment.text}_{config.style}_{config.provider}_{segment.emotional_tone}_{segment.scene_type}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cached_material(self, segment: ScriptSegment, config: AIVideoConfig) -> Optional[MaterialInfo]:
        """Get cached material if available"""
        if not config.enable_caching:
            return None
        
        try:
            cache_key = self._get_cache_key(segment, config)
            cache_file = os.path.join(self.cache_dir, f"cache_{cache_key}.png")
            
            if os.path.exists(cache_file):
                # Check if cache is still valid (24 hours)
                if time.time() - os.path.getmtime(cache_file) < 24 * 3600:
                    material = MaterialInfo()
                    material.url = cache_file
                    material.duration = int(segment.duration)
                    material.provider = "ai_cached"
                    return material
        except Exception as e:
            logger.debug(f"Cache lookup failed: {e}")
        
        return None
    
    def _cache_material(self, segment: ScriptSegment, config: AIVideoConfig, material: MaterialInfo):
        """Cache material for future use"""
        if not config.enable_caching:
            return
        
        try:
            cache_key = self._get_cache_key(segment, config)
            cache_file = os.path.join(self.cache_dir, f"cache_{cache_key}.png")
            
            # Copy material file to cache
            if os.path.exists(material.url):
                import shutil
                shutil.copy2(material.url, cache_file)
                logger.debug(f"Cached material: {cache_key}")
        except Exception as e:
            logger.debug(f"Failed to cache material: {e}")
    
    async def cleanup(self):
        """Clean up resources"""
        await self.ai_manager.cleanup()
    
    def get_generation_stats(self) -> Dict[str, Any]:
        """Get statistics about AI generation"""
        return {
            "cache_dir": self.cache_dir,
            "available_providers": self.ai_manager.get_available_providers(),
            "cache_size": len([f for f in os.listdir(self.cache_dir) if f.endswith('.png')]) if os.path.exists(self.cache_dir) else 0
        }
