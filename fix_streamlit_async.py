#!/usr/bin/env python3
"""
Streamlit Async Fix
Fixes WebSocket and asyncio issues in the Streamlit interface
"""

import os
import sys
from pathlib import Path

def fix_streamlit_config():
    """Create Streamlit config to reduce WebSocket issues"""
    
    # Create .streamlit directory if it doesn't exist
    streamlit_dir = Path(".streamlit")
    streamlit_dir.mkdir(exist_ok=True)
    
    config_content = """
[server]
# Reduce WebSocket timeout issues
enableWebsocketCompression = false
enableXsrfProtection = false
maxUploadSize = 200

[browser]
# Reduce browser-related issues
gatherUsageStats = false
serverAddress = "localhost"

[theme]
# Optional: Set a theme
primaryColor = "#FF6B6B"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"

[logger]
# Reduce logging noise
level = "warning"
"""
    
    config_path = streamlit_dir / "config.toml"
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(config_content.strip())
        
        print(f"✅ Created Streamlit config: {config_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create Streamlit config: {e}")
        return False

def create_streamlit_wrapper():
    """Create a wrapper script that handles async issues"""
    
    wrapper_content = '''#!/usr/bin/env python3
"""
Streamlit Wrapper for Shitpost Generator
Handles async operations safely
"""

import os
import sys
import asyncio
import warnings
from pathlib import Path

# Suppress specific warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*WebSocketClosedError.*")

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

def setup_asyncio():
    """Setup asyncio to work better with Streamlit"""
    try:
        # Set event loop policy for Windows
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # Create and set a new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
    except Exception as e:
        print(f"Warning: Could not setup asyncio: {e}")

def main():
    """Main wrapper function"""
    print("🤖 Starting Enhanced Shitpost Generator Web Interface...")
    print("🔧 Setting up async environment...")
    
    # Setup asyncio
    setup_asyncio()
    
    # Import and run Streamlit
    try:
        import streamlit.web.cli as stcli
        import streamlit as st
        
        # Set page config early
        try:
            st.set_page_config(
                page_title="Enhanced Shitpost Generator",
                page_icon="🤖",
                layout="wide",
                initial_sidebar_state="auto"
            )
        except:
            pass  # Ignore if already set
        
        # Run the main webui
        webui_path = str(Path(__file__).parent / "webui" / "Main.py")
        
        if os.path.exists(webui_path):
            print(f"✅ Starting web interface: {webui_path}")
            sys.argv = ["streamlit", "run", webui_path, "--server.address", "localhost"]
            stcli.main()
        else:
            print(f"❌ WebUI not found: {webui_path}")
            print("Make sure you're running from the project root directory")
            
    except ImportError as e:
        print(f"❌ Streamlit not available: {e}")
        print("Install with: pip install streamlit")
    except Exception as e:
        print(f"❌ Failed to start web interface: {e}")

if __name__ == "__main__":
    main()
'''
    
    wrapper_path = Path("start_shitpost_webui.py")
    
    try:
        with open(wrapper_path, 'w', encoding='utf-8') as f:
            f.write(wrapper_content)
        
        print(f"✅ Created Streamlit wrapper: {wrapper_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create wrapper: {e}")
        return False

def create_batch_files():
    """Create Windows batch files for easy startup"""
    
    # Windows batch file
    batch_content = '''@echo off
echo 🤖 Enhanced Shitpost Generator
echo Starting web interface...

REM Activate virtual environment if it exists
if exist "venv\\Scripts\\activate.bat" (
    echo Activating virtual environment...
    call venv\\Scripts\\activate.bat
)

REM Run the wrapper
python start_shitpost_webui.py

pause
'''
    
    batch_path = Path("start_shitpost.bat")
    
    try:
        with open(batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"✅ Created batch file: {batch_path}")
        
        # Also create a CLI batch file
        cli_batch_content = '''@echo off
echo 🤖 Enhanced Shitpost Generator CLI
echo.

REM Activate virtual environment if it exists
if exist "venv\\Scripts\\activate.bat" (
    echo Activating virtual environment...
    call venv\\Scripts\\activate.bat
)

REM Show help if no arguments
if "%1"=="" (
    echo Usage examples:
    echo   %0 generate --theme romanian --chaos 8
    echo   %0 batch --count 5 --theme gaming
    echo   %0 ai-generate --prompt "confused guy" --style absurd
    echo   %0 config --test-services
    echo.
    python shitpost_cli.py --help
) else (
    python shitpost_cli.py %*
)

pause
'''
        
        cli_batch_path = Path("shitpost_cli.bat")
        
        with open(cli_batch_path, 'w', encoding='utf-8') as f:
            f.write(cli_batch_content)
        
        print(f"✅ Created CLI batch file: {cli_batch_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create batch files: {e}")
        return False

def create_shell_scripts():
    """Create shell scripts for Linux/Mac"""
    
    # Shell script for web interface
    shell_content = '''#!/bin/bash
echo "🤖 Enhanced Shitpost Generator"
echo "Starting web interface..."

# Activate virtual environment if it exists
if [ -f "venv/bin/activate" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

# Run the wrapper
python3 start_shitpost_webui.py
'''
    
    shell_path = Path("start_shitpost.sh")
    
    try:
        with open(shell_path, 'w', encoding='utf-8') as f:
            f.write(shell_content)
        
        # Make executable
        os.chmod(shell_path, 0o755)
        
        print(f"✅ Created shell script: {shell_path}")
        
        # CLI shell script
        cli_shell_content = '''#!/bin/bash
echo "🤖 Enhanced Shitpost Generator CLI"

# Activate virtual environment if it exists
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
fi

# Show help if no arguments
if [ $# -eq 0 ]; then
    echo "Usage examples:"
    echo "  $0 generate --theme romanian --chaos 8"
    echo "  $0 batch --count 5 --theme gaming"
    echo "  $0 ai-generate --prompt 'confused guy' --style absurd"
    echo "  $0 config --test-services"
    echo
    python3 shitpost_cli.py --help
else
    python3 shitpost_cli.py "$@"
fi
'''
        
        cli_shell_path = Path("shitpost_cli.sh")
        
        with open(cli_shell_path, 'w', encoding='utf-8') as f:
            f.write(cli_shell_content)
        
        os.chmod(cli_shell_path, 0o755)
        
        print(f"✅ Created CLI shell script: {cli_shell_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create shell scripts: {e}")
        return False

def main():
    """Main fix function"""
    print("🔧 Fixing Streamlit Async Issues")
    print("=" * 40)
    
    success_count = 0
    total_fixes = 4
    
    # Apply fixes
    if fix_streamlit_config():
        success_count += 1
    
    if create_streamlit_wrapper():
        success_count += 1
    
    if create_batch_files():
        success_count += 1
    
    if create_shell_scripts():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Fix Summary")
    print("=" * 40)
    print(f"Fixes Applied: {success_count}/{total_fixes}")
    
    if success_count == total_fixes:
        print("\n✅ All fixes applied successfully!")
        print("\n🚀 How to start the application:")
        print("Windows:")
        print("  Double-click: start_shitpost.bat")
        print("  Or run: python start_shitpost_webui.py")
        print("\nLinux/Mac:")
        print("  Run: ./start_shitpost.sh")
        print("  Or run: python3 start_shitpost_webui.py")
        print("\nCLI Usage:")
        print("  Windows: shitpost_cli.bat generate --theme romanian")
        print("  Linux/Mac: ./shitpost_cli.sh generate --theme romanian")
        
    else:
        print("\n⚠️ Some fixes failed. Check the output above.")
    
    print("\n💡 These fixes should resolve:")
    print("  • WebSocket timeout errors")
    print("  • Asyncio event loop conflicts")
    print("  • Streamlit connection issues")

if __name__ == "__main__":
    main()
