# 📚 Documentație Funcționalități Noi - MoneyPrinterTurbo

## 🎯 Prezentare Generală

Acest document descrie noile funcționalități implementate în **MoneyPrinterTurbo - Aparat de scos masele la fraieri**, care extind capabilitățile aplicației fără a modifica funcționalitatea existentă de generare video.

## 🔧 1. Selector de Procesare Hardware

### Descriere
Sistem avansat pentru detectarea și configurarea hardware-ului disponibil pentru procesarea video, cu suport pentru CPU și GPU (NVIDIA CUDA, AMD, Intel).

### Funcționalități
- **Detectare automată hardware**: Identifică automat toate dispozitivele disponibile
- **Configurare optimizată**: Configurează MoviePy și alte biblioteci pentru hardware-ul selectat
- **Informații performanță**: Afișează estimări de viteză și recomandări
- **Suport multi-platform**: Funcționează pe Windows 10/11 cu diferite configurații

### Utilizare
1. Accesează secțiunea **"🔧 Configurare Hardware Procesare"**
2. Selectează hardware-ul dorit din lista detectată
3. Apasă **"🚀 Aplică Configurația Hardware"**
4. Verifică badge-ul de status pentru confirmarea configurării

### Hardware Suportat
- **NVIDIA GPU**: Suport complet CUDA cu encoder h264_nvenc
- **AMD GPU**: Optimizări CPU pentru compatibilitate maximă
- **Intel GPU**: Suport QuickSync pentru GPU-uri noi (ARC/Xe)
- **CPU**: Procesare multi-threaded optimizată

### Fișiere Implementate
- `app/utils/hardware_detector.py` - Detectarea hardware-ului
- `app/utils/hardware_config.py` - Configurarea pentru procesare
- `webui/components/hardware_selector.py` - Interfața utilizator

---

## 🎨 2. Sistem de Teme Personalizabile

### Descriere
Editor complet de teme inspirat din designul Bandcamp, cu suport pentru culori personalizate și imagini de fundal.

### Funcționalități
- **Editor culori**: Modifică toate culorile interfeței în timp real
- **Imagini de fundal**: Upload și configurare imagini personalizate
- **Teme predefinite**: 5 teme inspirate din Bandcamp
- **Salvare/Încărcare**: Gestionează teme personalizate
- **Export/Import**: Partajează teme între instalări

### Teme Predefinite
1. **Bandcamp Classic** - Albastru și roz clasic
2. **Dark Bandcamp** - Versiune întunecată
3. **Sunset Vibes** - Culori calde de apus
4. **Ocean Breeze** - Nuanțe de albastru și verde
5. **Purple Dreams** - Violet și lavandă creativă

### Utilizare
1. Accesează **"🎨 Editor Teme Personalizabile"**
2. Folosește tab-urile pentru:
   - **Selectare Temă**: Alege și aplică teme existente
   - **Editor Culori**: Personalizează culorile
   - **Fundal**: Configurează imagini de fundal
   - **Salvare/Gestionare**: Salvează și gestionează teme

### Opțiuni Fundal
- **Poziționare**: cover, contain, repeat, center
- **Opacitate**: 0.0 - 1.0
- **Blur**: 0-20 pixeli
- **Format suportat**: PNG, JPG, JPEG, GIF

### Fișiere Implementate
- `app/utils/theme_manager.py` - Gestionarea temelor
- `webui/components/theme_editor.py` - Interfața editor
- `storage/themes/` - Directorul pentru teme salvate

---

## 🛠️ 3. Instrumente pentru Dezvoltatori

### Descriere
Suită completă de instrumente pentru mentenanță, debugging și monitorizare aplicație.

### Funcționalități

#### 🧹 Curățare
- **Fișiere temporare**: Șterge conținutul din `/storage/temp/`
- **Cache video**: Curăță cache-ul din `/storage/cache_videos/`
- **Videoclipuri vechi**: Păstrează ultimele N videoclipuri
- **Curățare completă**: Efectuează toate operațiunile odată

#### 📊 Monitorizare Disk
- **Utilizare spațiu**: Grafice interactive cu Plotly
- **Statistici directoare**: Dimensiuni și numărul de fișiere
- **Alerte capacitate**: Indicatori de stare (Bun/Atenție/Critic)
- **Detalii storage**: Informații despre toate directoarele

#### 📋 Informații Sistem
- **Utilizare CPU**: Procentaj și numărul de nuclee
- **Memorie RAM**: Utilizare și capacitate totală
- **Spațiu disk**: Utilizare și spațiu liber
- **Informații aplicație**: Versiune Python, directorul de lucru

#### 📝 Logs Viewer
- **Citire logs**: Afișează fișierele de log disponibile
- **Filtrare**: Caută după text specific
- **Configurabil**: Numărul de linii și direcția de afișare
- **Download**: Descarcă logs pentru analiză

### Utilizare
1. Accesează **"🛠️ Instrumente Dezvoltatori"**
2. Folosește tab-urile pentru diferite funcții
3. Monitorizează spațiul disk regulat
4. Efectuează curățări periodice pentru performanțe optime

### Fișiere Implementate
- `app/utils/dev_tools.py` - Logica instrumentelor
- `webui/components/dev_tools_ui.py` - Interfața utilizator

---

## 📦 Dependințe Noi

Următoarele pachete au fost adăugate pentru noile funcționalități:

```bash
pip install psutil plotly pandas
```

- **psutil**: Informații sistem și monitorizare hardware
- **plotly**: Grafice interactive pentru monitorizare
- **pandas**: Procesarea datelor pentru statistici

---

## 🚀 Instalare și Configurare

### Pași de Instalare
1. Asigură-te că ai toate dependințele instalate
2. Restartează aplicația pentru a încărca noile module
3. Verifică că toate secțiunile sunt disponibile în interfață

### Verificare Funcționalitate
```bash
# Testează detectarea hardware
python -c "from app.utils.hardware_detector import get_hardware_detector; print('Hardware OK')"

# Testează managerul de teme
python -c "from app.utils.theme_manager import get_theme_manager; print('Teme OK')"

# Testează instrumentele dev
python -c "from app.utils.dev_tools import get_dev_tools_manager; print('Dev Tools OK')"
```

---

## 🔧 Configurare Avansată

### Hardware Personalizat
Pentru configurări hardware specifice, modifică fișierul `app/utils/hardware_config.py`:

```python
# Exemplu configurare personalizată NVIDIA
custom_config = {
    "codec": "h264_nvenc",
    "ffmpeg_params": ["-preset", "fast", "-b:v", "8M"],
    "threads": 6
}
```

### Teme Personalizate
Temele sunt salvate în format JSON în `storage/themes/`. Exemplu structură:

```json
{
  "name": "Tema Mea",
  "description": "Descriere temă",
  "colors": {
    "primary": "#1976d2",
    "secondary": "#dc004e",
    "background": "#ffffff"
  },
  "background": {
    "image_data": "base64_data_here",
    "position": "cover",
    "opacity": 1.0
  }
}
```

---

## 🐛 Depanare

### Probleme Comune

#### Hardware nu este detectat
- Verifică driverele GPU
- Asigură-te că CUDA este instalat pentru NVIDIA
- Restartează aplicația

#### Temele nu se aplică
- Verifică permisiunile pentru directorul `storage/themes/`
- Reîmprospătează pagina browser-ului
- Verifică console-ul pentru erori JavaScript

#### Dev Tools nu funcționează
- Verifică că `psutil` și `plotly` sunt instalate
- Asigură-te că ai permisiuni pentru directoarele de storage
- Verifică spațiul disk disponibil

### Logs și Debugging
Toate funcționalitățile noi folosesc sistemul de logging existent. Verifică logs pentru:
- Detectarea hardware: `🔍 Detectare hardware disponibil...`
- Încărcarea temelor: `📚 Încărcate X teme`
- Operațiuni dev tools: `🧹 Curățare completă...`

---

## 📞 Suport

Pentru probleme sau întrebări legate de noile funcționalități:
1. Verifică logs-urile în secțiunea Dev Tools
2. Testează funcționalitatea pas cu pas
3. Asigură-te că toate dependințele sunt instalate corect

---

## 🔄 Actualizări Viitoare

Funcționalități planificate pentru versiunile următoare:
- Suport pentru mai multe tipuri de GPU
- Teme animate și efecte vizuale
- Backup automat pentru configurații
- Integrare cu sisteme de monitorizare externe

---

*Documentație generată pentru MoneyPrinterTurbo v1.2.6+*
*Data: 26 Iulie 2025*
