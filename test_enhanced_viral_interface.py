#!/usr/bin/env python3
"""
Test Enhanced Viral Automation Interface

Tests the new real-time progress feedback functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))


async def test_enhanced_viral_interface():
    """Test the enhanced viral automation interface"""
    print("🧪 Testing Enhanced Viral Automation Interface...")
    print("=" * 60)
    
    try:
        # Test imports
        from webui.components.viral_automation_interface import (
            VIRAL_SERVICES_AVAILABLE,
            generate_viral_video_with_feedback,
            generate_title_and_description_fallback,
            generate_hashtags_fallback
        )
        
        print(f"✅ Enhanced interface imports successful")
        print(f"📊 VIRAL_SERVICES_AVAILABLE: {VIRAL_SERVICES_AVAILABLE}")
        
        if not VIRAL_SERVICES_AVAILABLE:
            print("❌ Viral services not available for testing")
            return False
        
        # Test configuration
        from app.services.one_click_viral_generator import ViralVideoConfig
        
        config = ViralVideoConfig(
            category="motivation",
            target_audience="tineri români 18-35",
            platform="tiktok",
            duration=60,
            use_contextual_images=True,
            max_contextual_images=5,
            include_subtitles=True,
            optimize_for_seo=True
        )
        
        print(f"✅ Test configuration created")
        print(f"📂 Category: {config.category}")
        print(f"👥 Target audience: {config.target_audience}")
        print(f"📱 Platform: {config.platform}")
        
        # Test fallback functions
        print("\n🔧 Testing fallback functions...")
        
        # Create mock topic and script for testing
        from app.services.viral_content_generator import ViralTopic, ViralScript
        
        mock_topic = ViralTopic(
            title="Test Viral Topic",
            description="Test description for viral content",
            category="motivation",
            viral_potential=8.5,
            target_audience="tineri români 18-35",
            trending_keywords=["motivație", "succes", "viață", "schimbare"],
            romanian_relevance=9.0,
            estimated_engagement="high",
            content_type="motivational"
        )
        
        mock_script = ViralScript(
            topic=mock_topic,
            script_text="Aceasta este o testare a scriptului viral pentru motivație...",
            hook="Hook puternic pentru început",
            main_content="Conținutul principal al scriptului viral cu sfaturi de motivație...",
            call_to_action="Apasă follow pentru mai mult conținut!",
            estimated_duration=60,
            engagement_elements=["întrebări retorice", "statistici surprinzătoare", "povești personale"],
            romanian_cultural_refs=["România", "succes", "motivație"]
        )
        
        # Test title and description generation
        title, description = await generate_title_and_description_fallback(
            mock_topic, mock_script, "tiktok"
        )
        
        print(f"✅ Title generation: {title}")
        print(f"✅ Description generation: {description[:50]}...")
        
        # Test hashtags generation
        hashtags = await generate_hashtags_fallback(
            mock_topic, mock_script, "tiktok"
        )
        
        print(f"✅ Hashtags generation: {len(hashtags)} hashtags")
        print(f"📋 Sample hashtags: {hashtags[:5]}")
        
        print("\n🎉 Enhanced viral interface test completed successfully!")
        print("🚀 The interface is ready for real-time progress feedback!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    try:
        success = asyncio.run(test_enhanced_viral_interface())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
