@echo off
echo ========================================
echo Schimbarea numelui aplicatiei
echo ========================================
echo.

echo Selectati optiunea:
echo 1. "Aparat de scos masele la fraieri" (numele actual)
echo 2. "MoneyPrinterTurbo" (numele original)
echo.

set /p choice="Introduceti alegerea (1 sau 2): "

if "%choice%"=="1" (
    echo Setez numele la "Aparat de scos masele la fraieri"...
    powershell -Command "(Get-Content webui\Main.py) -replace 'st\.title\(\".*\"\)', 'st.title(\"Aparat de scos masele la fraieri\")' | Set-Content webui\Main.py"
    powershell -Command "(Get-Content webui\Main.py) -replace 'page_title=\".*\"', 'page_title=\"Aparat de scos masele la fraieri\"' | Set-Content webui\Main.py"
    echo Numele a fost schimbat cu succes!
) else if "%choice%"=="2" (
    echo Setez numele la "MoneyPrinterTurbo"...
    powershell -Command "(Get-Content webui\Main.py) -replace 'st\.title\(\".*\"\)', 'st.title(\"MoneyPrinterTurbo\")' | Set-Content webui\Main.py"
    powershell -Command "(Get-Content webui\Main.py) -replace 'page_title=\".*\"', 'page_title=\"MoneyPrinterTurbo\"' | Set-Content webui\Main.py"
    echo Numele a fost schimbat cu succes!
) else (
    echo Alegere invalida. Va rugam sa introduceti 1 sau 2.
    pause
    goto :eof
)

echo.
echo Pentru a vedea schimbarile, restartati aplicatia:
echo .\start_webui.bat
echo.
pause
