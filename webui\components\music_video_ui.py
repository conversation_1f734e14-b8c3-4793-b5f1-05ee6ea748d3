"""
Music Video UI Component
Interfața pentru funcționalitatea de editare automată a videoclipurilor muzicale
"""

import streamlit as st
import os
import tempfile
from typing import Dict, Optional
from app.services.music_video_service import MusicVideoService

def render_music_video_tab():
    """Renderează tab-ul pentru Music Video Auto-Edit"""
    st.header("🎵 Video Muzical Auto-Edit")
    st.markdown("**Editează automat videoclipuri muzicale bazat pe gen, beat și emoții**")
    
    # Inițializează serviciul
    if 'music_video_service' not in st.session_state:
        st.session_state.music_video_service = MusicVideoService()
    
    service = st.session_state.music_video_service
    
    # Upload video
    st.subheader("📤 Upload Video Muzical")
    uploaded_video = st.file_uploader(
        "Selectează videoclipul muzical",
        type=['mp4', 'avi', 'mov', 'mkv'],
        help="Suportă videoclipuri cu audio"
    )
    
    if uploaded_video:
        # Salvează fișierul temporar
        temp_path = _save_uploaded_file(uploaded_video)
        
        if temp_path:
            # Afișează informații despre fișier
            _display_file_info(temp_path, service)
            
            # Tabs pentru diferite opțiuni
            tab1, tab2, tab3 = st.tabs([
                "🎯 Auto-Detect & Edit", 
                "🔍 Preview & Analyze", 
                "🎨 Custom Edit"
            ])
            
            with tab1:
                _render_auto_edit_tab(temp_path, service)
            
            with tab2:
                _render_preview_tab(temp_path, service)
            
            with tab3:
                _render_custom_edit_tab(temp_path, service)

def _save_uploaded_file(uploaded_file) -> Optional[str]:
    """Salvează fișierul uploadat temporar"""
    try:
        temp_dir = tempfile.gettempdir()
        temp_path = os.path.join(temp_dir, f"music_video_{uploaded_file.name}")
        
        with open(temp_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        return temp_path
    except Exception as e:
        st.error(f"Eroare la salvarea fișierului: {str(e)}")
        return None

def _display_file_info(file_path: str, service: MusicVideoService):
    """Afișează informații despre fișierul uploadat"""
    try:
        info = service.get_video_info(file_path)
        
        if info:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("⏱️ Durată", f"{info.get('duration', 0):.1f}s")
            
            with col2:
                st.metric("🎬 FPS", f"{info.get('fps', 0):.1f}")
            
            with col3:
                size = info.get('size', (0, 0))
                st.metric("📐 Rezoluție", f"{size[0]}x{size[1]}")
            
            with col4:
                file_size = info.get('file_size', 0) / (1024 * 1024)
                st.metric("💾 Mărime", f"{file_size:.1f} MB")
            
            # Audio check
            if info.get('has_audio'):
                st.success("✅ Fișierul conține audio")
            else:
                st.warning("⚠️ Fișierul nu conține audio")
                
    except Exception as e:
        st.warning(f"Nu s-au putut obține informații despre fișier: {str(e)}")

def _render_auto_edit_tab(file_path: str, service: MusicVideoService):
    """Renderează tab-ul pentru editare automată"""
    st.subheader("🎯 Editare Automată")
    st.markdown("Detectează automat genul și aplică efectele potrivite")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**⚙️ Setări Generale**")
        
        intensity = st.slider(
            "🔥 Intensitate Efecte", 
            0.1, 2.0, 1.0, 0.1,
            help="Cât de intense să fie efectele vizuale"
        )
        
        use_secondary = st.checkbox(
            "➕ Folosește efecte secundare", 
            value=False,
            help="Adaugă efecte suplimentare pentru mai mult impact"
        )
        
        export_format = st.selectbox(
            "📱 Format Export",
            [
                "Original (păstrează aspectul)",
                "Instagram Reels (9:16)",
                "TikTok (9:16)", 
                "YouTube Shorts (9:16)",
                "YouTube Standard (16:9)"
            ]
        )
    
    with col2:
        st.markdown("**🎵 Genuri Suportate**")
        supported_genres = service.get_supported_genres()
        
        genre_display = {
            'manele': '🎤 Manele/Lăutărească',
            'rock': '🎸 Rock/Metal',
            'hip_hop': '🎵 Hip-Hop/Rap',
            'edm': '🎹 EDM/Electronic',
            'pop': '💫 Pop'
        }
        
        for genre in supported_genres:
            display_name = genre_display.get(genre, genre.title())
            st.write(f"• {display_name}")
    
    # Buton pentru procesare
    if st.button("🎬 Procesează Video Muzical", type="primary", use_container_width=True):
        _process_music_video(file_path, service, {
            'intensity': intensity,
            'use_secondary_effects': use_secondary,
            'export_format': export_format
        })

def _render_preview_tab(file_path: str, service: MusicVideoService):
    """Renderează tab-ul pentru preview și analiză"""
    st.subheader("🔍 Preview & Analiză")
    st.markdown("Analizează videoclipul fără procesare pentru a vedea ce efecte vor fi aplicate")
    
    if st.button("🔍 Analizează Video", use_container_width=True):
        with st.spinner("🎵 Analizez videoclipul..."):
            try:
                analysis = service.preview_analysis(file_path)
                
                if analysis['success']:
                    _display_analysis_results(analysis)
                else:
                    st.error(f"Eroare în analiză: {analysis.get('error', 'Eroare necunoscută')}")
                    
            except Exception as e:
                st.error(f"Eroare în analiză: {str(e)}")

def _render_custom_edit_tab(file_path: str, service: MusicVideoService):
    """Renderează tab-ul pentru editare personalizată"""
    st.subheader("🎨 Editare Personalizată")
    st.markdown("Configurează manual efectele și stilul")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**🎯 Gen Manual**")
        manual_genre = st.selectbox(
            "Forțează genul",
            ["Auto-Detect"] + service.get_supported_genres()
        )
        
        st.markdown("**✨ Efecte Vizuale**")
        available_effects = [
            "text_overlay_emotional",
            "text_overlay_rapid", 
            "color_warm_boost",
            "color_dark_dramatic",
            "zoom_dramatic",
            "shake_energy",
            "fade_smooth"
        ]
        
        selected_effects = st.multiselect(
            "Selectează efectele",
            available_effects,
            help="Alege efectele care vor fi aplicate"
        )
    
    with col2:
        st.markdown("**🎨 Personalizare Stil**")
        
        custom_colors = st.checkbox("🎨 Culori personalizate")
        
        if custom_colors:
            primary_color = st.color_picker("Culoare primară", "#FFD700")
            secondary_color = st.color_picker("Culoare secundară", "#FF6B6B")
        
        font_size = st.slider("📝 Mărime font", 30, 120, 60)
        
        animation_speed = st.selectbox(
            "⚡ Viteză animații",
            ["slow", "medium", "fast", "very_fast"]
        )
    
    # Configurația personalizată
    custom_config = {
        'effects': selected_effects,
        'font_size': font_size,
        'animation_speed': animation_speed
    }
    
    if custom_colors:
        custom_config.update({
            'primary_color': primary_color,
            'secondary_color': secondary_color
        })
    
    if manual_genre != "Auto-Detect":
        custom_config['forced_genre'] = manual_genre
    
    if st.button("🎨 Creează Edit Personalizat", type="primary", use_container_width=True):
        _create_custom_edit(file_path, service, custom_config)

def _display_analysis_results(analysis: Dict):
    """Afișează rezultatele analizei"""
    st.success("✅ Analiză completă!")
    
    # Informații despre gen
    genre_info = analysis['genre']
    st.subheader(f"🎵 Gen detectat: {genre_info['primary_genre'].title()}")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "🎯 Confidence", 
            f"{genre_info['confidence']:.1%}",
            help="Cât de sigur este sistemul de genul detectat"
        )
    
    with col2:
        audio_info = analysis['audio_info']
        st.metric(
            "🥁 Tempo (BPM)", 
            f"{audio_info['tempo']:.0f}",
            help="Ritmul melodiei în bătăi pe minut"
        )
    
    with col3:
        st.metric(
            "⚡ Energie", 
            f"{audio_info['energy_level']:.2f}",
            help="Nivelul de energie al melodiei"
        )
    
    # Efecte recomandate
    st.subheader("✨ Efecte Recomandate")
    recommended = analysis['recommended_effects']
    
    effects_display = {
        'text_overlay_emotional': '📝 Text overlay emoțional',
        'text_overlay_rapid': '⚡ Text overlay rapid',
        'color_warm_boost': '🌅 Boost culori calde',
        'color_dark_dramatic': '🌑 Culori dramatice',
        'zoom_dramatic': '🔍 Zoom dramatic',
        'shake_energy': '📳 Shake energic',
        'fade_smooth': '🌊 Tranziții smooth'
    }
    
    for effect in recommended:
        display_name = effects_display.get(effect, effect.replace('_', ' ').title())
        st.write(f"• {display_name}")

def _process_music_video(file_path: str, service: MusicVideoService, preferences: Dict):
    """Procesează videoclipul muzical"""
    with st.spinner("🎵 Procesez videoclipul muzical..."):
        try:
            # Generează calea de output
            output_path = os.path.join("storage", "videos", f"music_edit_{os.path.basename(file_path)}")
            
            # Procesează videoclipul
            result = service.process_music_video(file_path, output_path, preferences)
            
            if result['success']:
                st.success("🎉 Videoclip muzical procesat cu succes!")
                
                # Afișează informații despre procesare
                processing_info = result['processing_info']
                
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("🎵 Gen", processing_info['genre_detected'].title())
                
                with col2:
                    st.metric("🎯 Confidence", f"{processing_info['confidence']:.1%}")
                
                with col3:
                    st.metric("✨ Efecte", processing_info['effects_applied'])
                
                # Link pentru download
                if os.path.exists(result['output_path']):
                    with open(result['output_path'], "rb") as file:
                        st.download_button(
                            label="📥 Descarcă Video Editat",
                            data=file.read(),
                            file_name=os.path.basename(result['output_path']),
                            mime="video/mp4",
                            use_container_width=True
                        )
            else:
                st.error(f"❌ Eroare în procesare: {result.get('error', 'Eroare necunoscută')}")
                
        except Exception as e:
            st.error(f"❌ Eroare în procesare: {str(e)}")

def _create_custom_edit(file_path: str, service: MusicVideoService, config: Dict):
    """Creează un edit personalizat"""
    with st.spinner("🎨 Creez edit personalizat..."):
        try:
            output_path = os.path.join("storage", "videos", f"custom_edit_{os.path.basename(file_path)}")
            
            result = service.create_custom_edit(file_path, output_path, config)
            
            if result['success']:
                st.success("🎉 Edit personalizat creat cu succes!")
                
                if os.path.exists(result['output_path']):
                    with open(result['output_path'], "rb") as file:
                        st.download_button(
                            label="📥 Descarcă Edit Personalizat",
                            data=file.read(),
                            file_name=os.path.basename(result['output_path']),
                            mime="video/mp4",
                            use_container_width=True
                        )
            else:
                st.error(f"❌ Eroare în crearea edit-ului: {result.get('error', 'Eroare necunoscută')}")
                
        except Exception as e:
            st.error(f"❌ Eroare în crearea edit-ului: {str(e)}")
