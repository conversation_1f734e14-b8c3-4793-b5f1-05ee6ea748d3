import math
import os.path
import re
from os import path

from loguru import logger

from app.config import config
from app.models import const
from app.models.schema import VideoConcatMode, VideoParams
from app.services import llm, material, subtitle, video, voice
from app.services import state as sm
from app.services.contextual_image_generator import ContextualImageGenerator
from app.services.timing_synchronizer import TimingSynchronizer
from app.services.style_consistency_manager import StyleConsistencyManager
from app import utils
from app.utils import utils


def generate_script(task_id, params):
    logger.info("\n\n## generating video script")
    video_script = params.video_script.strip()
    if not video_script:
        video_script = llm.generate_script(
            video_subject=params.video_subject,
            language=params.video_language,
            paragraph_number=params.paragraph_number,
        )
    else:
        logger.debug(f"video script: \n{video_script}")

    if not video_script:
        sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
        logger.error("failed to generate video script.")
        return None

    return video_script


def generate_terms(task_id, params, video_script):
    logger.info("\n\n## generating video terms")
    video_terms = params.video_terms
    if not video_terms:
        video_terms = llm.generate_terms(
            video_subject=params.video_subject, video_script=video_script, amount=5
        )
    else:
        if isinstance(video_terms, str):
            video_terms = [term.strip() for term in re.split(r"[,，]", video_terms)]
        elif isinstance(video_terms, list):
            video_terms = [term.strip() for term in video_terms]
        else:
            raise ValueError("video_terms must be a string or a list of strings.")

        logger.debug(f"video terms: {utils.to_json(video_terms)}")

    if not video_terms:
        sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
        logger.error("failed to generate video terms.")
        return None

    return video_terms


def save_script_data(task_id, video_script, video_terms, params):
    script_file = path.join(utils.task_dir(task_id), "script.json")
    script_data = {
        "script": video_script,
        "search_terms": video_terms,
        "params": params,
    }

    with open(script_file, "w", encoding="utf-8") as f:
        f.write(utils.to_json(script_data))


def generate_audio(task_id, params, video_script):
    logger.info("\n\n## generating audio")
    audio_file = path.join(utils.task_dir(task_id), "audio.mp3")
    sub_maker = voice.tts(
        text=video_script,
        voice_name=voice.parse_voice_name(params.voice_name),
        voice_rate=params.voice_rate,
        voice_file=audio_file,
    )
    if sub_maker is None:
        sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
        logger.error(
            """failed to generate audio:
1. check if the language of the voice matches the language of the video script.
2. check if the network is available. If you are in China, it is recommended to use a VPN and enable the global traffic mode.
        """.strip()
        )
        return None, None, None

    audio_duration = math.ceil(voice.get_audio_duration(sub_maker))
    return audio_file, audio_duration, sub_maker


def generate_subtitle(task_id, params, video_script, sub_maker, audio_file):
    if not params.subtitle_enabled:
        return ""

    subtitle_path = path.join(utils.task_dir(task_id), "subtitle.srt")
    subtitle_provider = config.app.get("subtitle_provider", "edge").strip().lower()
    logger.info(f"\n\n## generating subtitle, provider: {subtitle_provider}")

    subtitle_fallback = False
    if subtitle_provider == "edge":
        voice.create_subtitle(
            text=video_script, sub_maker=sub_maker, subtitle_file=subtitle_path
        )
        if not os.path.exists(subtitle_path):
            subtitle_fallback = True
            logger.warning("subtitle file not found, fallback to whisper")

    if subtitle_provider == "whisper" or subtitle_fallback:
        subtitle.create(audio_file=audio_file, subtitle_file=subtitle_path)
        logger.info("\n\n## correcting subtitle")
        subtitle.correct(subtitle_file=subtitle_path, video_script=video_script)

    subtitle_lines = subtitle.file_to_subtitles(subtitle_path)
    if not subtitle_lines:
        logger.warning(f"subtitle file is invalid: {subtitle_path}")
        return ""

    return subtitle_path


async def generate_contextual_images(task_id, params, video_script, audio_duration):
    """Generate contextual AI images based on script analysis"""
    # Check if contextual image generation is enabled
    if not getattr(params, 'contextual_images_enabled', False):
        logger.info("Contextual image generation disabled")
        return []

    logger.info("\n\n## generating contextual images")

    try:
        # Initialize services
        image_generator = ContextualImageGenerator()
        style_manager = StyleConsistencyManager()

        # Set up style consistency
        ai_style = getattr(params, 'ai_style', 'realistic')
        style_manager.set_style_profile(ai_style)

        # Initialize the image generator
        await image_generator.initialize()

        # Generate contextual images
        contextual_images = await image_generator.generate_contextual_images(
            script=video_script,
            audio_duration=audio_duration,
            params=params,
            task_id=task_id
        )

        if contextual_images:
            logger.info(f"Generated {len(contextual_images)} contextual images")

            # Export generation data for debugging
            export_path = path.join(utils.task_dir(task_id), "contextual_images_data.json")
            image_generator.export_image_data(contextual_images, export_path)

            # Export style consistency data
            style_export_path = path.join(utils.task_dir(task_id), "style_consistency_data.json")
            style_manager.export_style_data(style_export_path)
        else:
            logger.warning("No contextual images generated")

        # Cleanup
        await image_generator.cleanup()

        return contextual_images

    except Exception as e:
        logger.error(f"Contextual image generation failed: {e}")
        return []


async def generate_ai_video_materials(task_id, params, video_terms, audio_duration):
    """Generate AI images and convert them to video materials"""
    logger.info(f"\n\n## generating AI images for video source: {params.video_source}")

    try:
        from app.services.ai_video_source_manager import AIVideoSourceManager, AIImageConfig, AIProvider

        # Initialize AI manager
        ai_manager = AIVideoSourceManager()

        # Configure AI image generation
        config = AIImageConfig()
        config.width = 1080 if params.video_aspect.value == "9:16" else 1920
        config.height = 1920 if params.video_aspect.value == "9:16" else 1080
        config.style = getattr(params, 'ai_style', 'realistic')

        # Determine AI provider based on video source
        if params.video_source == "ai":
            config.provider = AIProvider(getattr(params, 'ai_provider', 'auto'))
        elif params.video_source == "ai_perchance":
            config.provider = AIProvider.PERCHANCE
        elif params.video_source == "ai_openai":
            config.provider = AIProvider.OPENAI
        elif params.video_source == "ai_stable_diffusion":
            config.provider = AIProvider.STABLE_DIFFUSION
        elif params.video_source == "ai_local_sd":
            config.provider = AIProvider.LOCAL_SD
        else:
            config.provider = AIProvider.AUTO

        # Calculate required number of images based on audio duration
        import math
        clip_duration = params.video_clip_duration or 3
        total_duration_needed = audio_duration * params.video_count
        required_images = max(5, math.ceil(total_duration_needed / clip_duration) + 2)

        logger.info(f"🎬 Need {required_images} AI images for {total_duration_needed:.1f}s total duration (clip duration: {clip_duration}s)")

        # Generate prompts based on script/subject and terms
        script_context = params.video_script or params.video_subject

        # Create multiple prompts for variety - ensure we have enough prompts
        prompts = []
        if video_terms:
            # Use all available terms and repeat if necessary
            base_terms = video_terms[:10]  # Use up to 10 terms
            while len(prompts) < required_images:
                for term in base_terms:
                    if len(prompts) >= required_images:
                        break
                    prompt = f"{term}, {config.style} style, high quality, detailed"
                    prompts.append(prompt)
        else:
            # Generate varied prompts from script context
            base_prompt = f"{script_context}, {config.style} style, high quality, detailed"
            variations = [
                f"{script_context}, {config.style} style, high quality, detailed",
                f"{script_context}, cinematic {config.style}, professional lighting",
                f"{script_context}, artistic {config.style}, vibrant colors",
                f"{script_context}, modern {config.style}, sharp focus",
                f"{script_context}, elegant {config.style}, beautiful composition"
            ]
            # Repeat variations to reach required count
            while len(prompts) < required_images:
                prompts.extend(variations[:required_images - len(prompts)])

        # Generate images
        generated_images = []

        for i, prompt in enumerate(prompts[:required_images]):
            logger.info(f"Generating AI image {i+1}/{required_images}: {prompt[:50]}...")

            try:
                result = await ai_manager.generate_image(prompt, config)

                if result.success:
                    # Save image to task directory
                    task_dir = utils.task_dir(task_id)
                    image_filename = f"ai_image_{i+1:03d}.png"
                    image_path = os.path.join(task_dir, image_filename)

                    with open(image_path, 'wb') as f:
                        f.write(result.image_data)

                    logger.info(f"AI image saved: {image_path}")
                    generated_images.append(image_path)
                else:
                    logger.warning(f"AI image generation failed: {result.error_message}")

            except Exception as e:
                logger.error(f"Error generating AI image {i+1}: {e}")
                continue

        await ai_manager.cleanup()

        if not generated_images:
            logger.error("No AI images were generated successfully")
            return None

        # Convert images to video clips
        logger.info(f"\n\n## converting {len(generated_images)} AI images to video clips")
        video_clips = []

        for image_path in generated_images:
            try:
                # Convert image to video clip using existing preprocessing
                from app.models.schema import MaterialInfo
                material_info = MaterialInfo(url=image_path, provider="ai")

                # Process the image (this will convert it to video)
                processed_materials = video.preprocess_video(
                    materials=[material_info],
                    clip_duration=params.video_clip_duration
                )

                if processed_materials:
                    video_clips.extend([m.url for m in processed_materials])

            except Exception as e:
                logger.error(f"Failed to convert AI image to video: {e}")
                continue

        if not video_clips:
            logger.error("Failed to convert AI images to video clips")
            return None

        logger.success(f"Generated {len(video_clips)} AI video clips")
        return video_clips

    except Exception as e:
        logger.error(f"AI video material generation failed: {e}")
        return None


def get_video_materials(task_id, params, video_terms, audio_duration):
    if params.video_source == "local":
        logger.info("\n\n## preprocess local materials")
        materials = video.preprocess_video(
            materials=params.video_materials, clip_duration=params.video_clip_duration
        )
        if not materials:
            sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
            logger.error(
                "no valid materials found, please check the materials and try again."
            )
            return None
        return [material_info.url for material_info in materials]
    elif params.video_source.startswith("ai"):
        # Handle AI-generated video materials
        logger.info(f"\n\n## generating AI video materials from {params.video_source}")

        # Use asyncio to run the async function
        import asyncio
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        try:
            ai_videos = loop.run_until_complete(
                generate_ai_video_materials(task_id, params, video_terms, audio_duration)
            )

            if not ai_videos:
                sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
                logger.error("failed to generate AI video materials")
                return None

            return ai_videos

        except Exception as e:
            logger.error(f"AI video generation error: {e}")
            sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
            return None
    else:
        logger.info(f"\n\n## downloading videos from {params.video_source}")
        downloaded_videos = material.download_videos(
            task_id=task_id,
            search_terms=video_terms,
            source=params.video_source,
            video_aspect=params.video_aspect,
            video_contact_mode=params.video_concat_mode,
            audio_duration=audio_duration * params.video_count,
            max_clip_duration=params.video_clip_duration,
            script_context=params.video_script or params.video_subject,
            ai_style=getattr(params, 'ai_style', 'realistic'),
            ai_provider=getattr(params, 'ai_provider', 'auto')
        )
        if not downloaded_videos:
            sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
            logger.error(
                "failed to download videos, maybe the network is not available. if you are in China, please use a VPN."
            )
            return None
        return downloaded_videos


def generate_final_videos(
    task_id, params, downloaded_videos, audio_file, subtitle_path, contextual_images=None
):
    final_video_paths = []
    combined_video_paths = []
    video_concat_mode = (
        params.video_concat_mode if params.video_count == 1 else VideoConcatMode.random
    )
    video_transition_mode = params.video_transition_mode

    _progress = 50
    for i in range(params.video_count):
        index = i + 1
        combined_video_path = path.join(
            utils.task_dir(task_id), f"combined-{index}.mp4"
        )
        logger.info(f"\n\n## combining video: {index} => {combined_video_path}")
        video.combine_videos(
            combined_video_path=combined_video_path,
            video_paths=downloaded_videos,
            audio_file=audio_file,
            video_aspect=params.video_aspect,
            video_concat_mode=video_concat_mode,
            video_transition_mode=video_transition_mode,
            max_clip_duration=params.video_clip_duration,
            threads=params.n_threads,
        )

        _progress += 50 / params.video_count / 2
        sm.state.update_task(task_id, progress=_progress)

        final_video_path = path.join(utils.task_dir(task_id), f"final-{index}.mp4")

        logger.info(f"\n\n## generating video: {index} => {final_video_path}")
        video.generate_video(
            video_path=combined_video_path,
            audio_path=audio_file,
            subtitle_path=subtitle_path,
            output_file=final_video_path,
            params=params,
            contextual_images=contextual_images,
        )

        _progress += 50 / params.video_count / 2
        sm.state.update_task(task_id, progress=_progress)

        final_video_paths.append(final_video_path)
        combined_video_paths.append(combined_video_path)

    return final_video_paths, combined_video_paths


def start(task_id, params: VideoParams, stop_at: str = "video"):
    logger.info(f"start task: {task_id}, stop_at: {stop_at}")
    sm.state.update_task(task_id, state=const.TASK_STATE_PROCESSING, progress=5)

    if type(params.video_concat_mode) is str:
        params.video_concat_mode = VideoConcatMode(params.video_concat_mode)

    # 1. Generate script
    video_script = generate_script(task_id, params)
    if not video_script or "Error: " in video_script:
        sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
        return

    sm.state.update_task(task_id, state=const.TASK_STATE_PROCESSING, progress=10)

    if stop_at == "script":
        sm.state.update_task(
            task_id, state=const.TASK_STATE_COMPLETE, progress=100, script=video_script
        )
        return {"script": video_script}

    # 2. Generate terms
    video_terms = ""
    if params.video_source != "local":
        video_terms = generate_terms(task_id, params, video_script)
        if not video_terms:
            sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
            return

    save_script_data(task_id, video_script, video_terms, params)

    if stop_at == "terms":
        sm.state.update_task(
            task_id, state=const.TASK_STATE_COMPLETE, progress=100, terms=video_terms
        )
        return {"script": video_script, "terms": video_terms}

    sm.state.update_task(task_id, state=const.TASK_STATE_PROCESSING, progress=20)

    # 3. Generate audio
    audio_file, audio_duration, sub_maker = generate_audio(
        task_id, params, video_script
    )
    if not audio_file:
        sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
        return

    sm.state.update_task(task_id, state=const.TASK_STATE_PROCESSING, progress=30)

    if stop_at == "audio":
        sm.state.update_task(
            task_id,
            state=const.TASK_STATE_COMPLETE,
            progress=100,
            audio_file=audio_file,
        )
        return {"audio_file": audio_file, "audio_duration": audio_duration}

    # 4. Generate subtitle
    subtitle_path = generate_subtitle(
        task_id, params, video_script, sub_maker, audio_file
    )

    if stop_at == "subtitle":
        sm.state.update_task(
            task_id,
            state=const.TASK_STATE_COMPLETE,
            progress=100,
            subtitle_path=subtitle_path,
        )
        return {"subtitle_path": subtitle_path}

    sm.state.update_task(task_id, state=const.TASK_STATE_PROCESSING, progress=40)

    # 4.5. Generate contextual images (if enabled)
    contextual_images = []
    if getattr(params, 'contextual_images_enabled', False):
        try:
            import asyncio
            contextual_images = asyncio.run(generate_contextual_images(
                task_id, params, video_script, audio_duration
            ))
        except Exception as e:
            logger.error(f"Contextual image generation failed: {e}")
            # Continue with regular video generation even if contextual images fail

    # 5. Get video materials
    downloaded_videos = get_video_materials(
        task_id, params, video_terms, audio_duration
    )
    if not downloaded_videos:
        sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
        return

    if stop_at == "materials":
        sm.state.update_task(
            task_id,
            state=const.TASK_STATE_COMPLETE,
            progress=100,
            materials=downloaded_videos,
        )
        return {"materials": downloaded_videos}

    sm.state.update_task(task_id, state=const.TASK_STATE_PROCESSING, progress=50)

    # 6. Generate final videos
    final_video_paths, combined_video_paths = generate_final_videos(
        task_id, params, downloaded_videos, audio_file, subtitle_path, contextual_images
    )

    if not final_video_paths:
        sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
        return

    logger.success(
        f"task {task_id} finished, generated {len(final_video_paths)} videos."
    )

    kwargs = {
        "videos": final_video_paths,
        "combined_videos": combined_video_paths,
        "script": video_script,
        "terms": video_terms,
        "audio_file": audio_file,
        "audio_duration": audio_duration,
        "subtitle_path": subtitle_path,
        "materials": downloaded_videos,
    }
    sm.state.update_task(
        task_id, state=const.TASK_STATE_COMPLETE, progress=100, **kwargs
    )
    return kwargs


if __name__ == "__main__":
    task_id = "task_id"
    params = VideoParams(
        video_subject="金钱的作用",
        voice_name="zh-CN-XiaoyiNeural-Female",
        voice_rate=1.0,
    )
    start(task_id, params, stop_at="video")
