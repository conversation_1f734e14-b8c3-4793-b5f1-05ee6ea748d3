#!/usr/bin/env python3
"""
Enhanced Shitpost Generator Setup Script
Automated setup for the AI-powered shitpost video generator
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
from pathlib import Path

class ShitpostSetup:
    """Setup manager for the Enhanced Shitpost Generator"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.python_version = sys.version_info
        self.project_root = Path(__file__).parent
        self.errors = []
        self.warnings = []
        
    def print_header(self):
        """Print setup header"""
        print("🤖 Enhanced Shitpost Generator Setup")
        print("=" * 50)
        print(f"System: {platform.system()} {platform.release()}")
        print(f"Python: {sys.version.split()[0]}")
        print(f"Project Root: {self.project_root}")
        print()
    
    def check_python_version(self):
        """Check if Python version is compatible"""
        print("🐍 Checking Python version...")
        
        if self.python_version < (3, 8):
            self.errors.append("Python 3.8+ is required")
            print("❌ Python 3.8+ is required")
            return False
        else:
            print(f"✅ Python {sys.version.split()[0]} is compatible")
            return True
    
    def install_basic_requirements(self):
        """Install basic Python requirements"""
        print("\n📦 Installing basic requirements...")
        
        try:
            # Install core requirements
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print("✅ Basic requirements installed")
            return True
        except subprocess.CalledProcessError as e:
            self.errors.append(f"Failed to install requirements: {e}")
            print(f"❌ Failed to install requirements: {e}")
            return False
    
    def install_gpt4free(self):
        """Install GPT4Free for AI text generation"""
        print("\n🤖 Installing GPT4Free...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-U", "g4f[all]"
            ])
            print("✅ GPT4Free installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            self.warnings.append(f"GPT4Free installation failed: {e}")
            print(f"⚠️ GPT4Free installation failed: {e}")
            print("   You can still use the basic features")
            return False
    
    def install_optional_ai_deps(self):
        """Install optional AI dependencies"""
        print("\n🎨 Installing optional AI dependencies...")
        
        optional_deps = [
            "aiohttp>=3.8.0",
            "psutil>=5.9.0"
        ]
        
        success_count = 0
        for dep in optional_deps:
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", dep
                ])
                print(f"✅ Installed {dep}")
                success_count += 1
            except subprocess.CalledProcessError as e:
                self.warnings.append(f"Failed to install {dep}: {e}")
                print(f"⚠️ Failed to install {dep}")
        
        print(f"📊 Installed {success_count}/{len(optional_deps)} optional dependencies")
        return success_count > 0
    
    def check_ffmpeg(self):
        """Check if FFmpeg is available"""
        print("\n🎬 Checking FFmpeg...")
        
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode == 0:
                print("✅ FFmpeg is available")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        print("⚠️ FFmpeg not found")
        print("   FFmpeg is required for video processing")
        
        if self.system == "windows":
            print("   Download from: https://www.gyan.dev/ffmpeg/builds/")
        elif self.system == "darwin":  # macOS
            print("   Install with: brew install ffmpeg")
        else:  # Linux
            print("   Install with: sudo apt install ffmpeg")
        
        self.warnings.append("FFmpeg not found")
        return False
    
    def check_imagemagick(self):
        """Check if ImageMagick is available"""
        print("\n🖼️ Checking ImageMagick...")
        
        commands_to_try = ["magick", "convert"]
        
        for cmd in commands_to_try:
            try:
                result = subprocess.run(
                    [cmd, "-version"], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
                if result.returncode == 0:
                    print(f"✅ ImageMagick is available ({cmd})")
                    return True
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        print("⚠️ ImageMagick not found")
        print("   ImageMagick is required for text effects")
        
        if self.system == "windows":
            print("   Download from: https://imagemagick.org/script/download.php")
            print("   ⚠️ Make sure to download the STATIC version!")
        elif self.system == "darwin":  # macOS
            print("   Install with: brew install imagemagick")
        else:  # Linux
            print("   Install with: sudo apt install imagemagick")
        
        self.warnings.append("ImageMagick not found")
        return False
    
    def create_directories(self):
        """Create necessary directories"""
        print("\n📁 Creating directories...")
        
        directories = [
            "storage/videos",
            "storage/ai_images", 
            "storage/image_cache",
            "storage/temp",
            "storage/meme_templates"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ Created {directory}")
        
        return True
    
    def test_basic_functionality(self):
        """Test basic functionality"""
        print("\n🧪 Testing basic functionality...")
        
        try:
            # Test imports
            from app.services.shitpost_generator import ShitpostGenerator
            print("✅ Shitpost generator import successful")
            
            # Test GPT4Free if available
            try:
                from app.services.gpt4free_service import gpt4free_service
                if gpt4free_service.is_available():
                    print("✅ GPT4Free service available")
                else:
                    print("⚠️ GPT4Free service not available")
            except ImportError:
                print("⚠️ GPT4Free service not imported")
            
            # Test free AI services
            try:
                from app.services.free_ai_services import free_ai_generator
                print("✅ Free AI services available")
            except ImportError:
                print("⚠️ Free AI services not available")
            
            return True
            
        except ImportError as e:
            self.errors.append(f"Import test failed: {e}")
            print(f"❌ Import test failed: {e}")
            return False
    
    def create_example_config(self):
        """Create example configuration"""
        print("\n⚙️ Creating example configuration...")
        
        config_content = """
# Enhanced Shitpost Generator Configuration
[app]
# Basic settings
default_chaos_level = 7
default_duration = 15
enable_ai_images = true

# AI Services
gpt4free_enabled = true
stable_diffusion_enabled = false  # Set to true if you have local SD
pollinations_enabled = true

# Performance
cache_enabled = true
max_retries = 3
timeout_seconds = 30

# Video settings
video_width = 720
video_height = 1280
fps = 30

[shitpost]
# Shitpost-specific settings
max_chaos_effects = 5
enable_deep_fry = true
enable_romanian_culture = true
enable_gaming_refs = true

# Social media optimization
optimize_for_tiktok = true
optimize_for_instagram = true
auto_generate_hashtags = true
"""
        
        config_path = self.project_root / "config_shitpost_example.toml"
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(config_content.strip())
            print(f"✅ Example config created: {config_path}")
            return True
        except Exception as e:
            self.warnings.append(f"Failed to create config: {e}")
            print(f"⚠️ Failed to create config: {e}")
            return False
    
    def print_summary(self):
        """Print setup summary"""
        print("\n" + "=" * 50)
        print("📋 Setup Summary")
        print("=" * 50)
        
        if not self.errors:
            print("✅ Setup completed successfully!")
        else:
            print("❌ Setup completed with errors:")
            for error in self.errors:
                print(f"   • {error}")
        
        if self.warnings:
            print("\n⚠️ Warnings:")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        print("\n🚀 Next Steps:")
        print("1. Start the web interface:")
        print("   python webui.bat  # Windows")
        print("   sh webui.sh       # Linux/Mac")
        print()
        print("2. Or use the CLI:")
        print("   python shitpost_cli.py generate --theme romanian --chaos 8")
        print()
        print("3. Check the documentation:")
        print("   See SHITPOST_GENERATOR_README.md for detailed usage")
        print()
        
        if self.warnings:
            print("💡 To resolve warnings, install the missing dependencies")
            print("   and run this setup script again.")
    
    def run_setup(self):
        """Run the complete setup process"""
        self.print_header()
        
        # Check prerequisites
        if not self.check_python_version():
            self.print_summary()
            return False
        
        # Install dependencies
        self.install_basic_requirements()
        self.install_gpt4free()
        self.install_optional_ai_deps()
        
        # Check external tools
        self.check_ffmpeg()
        self.check_imagemagick()
        
        # Setup project
        self.create_directories()
        self.create_example_config()
        
        # Test functionality
        self.test_basic_functionality()
        
        # Show summary
        self.print_summary()
        
        return len(self.errors) == 0

def main():
    """Main setup function"""
    setup = ShitpostSetup()
    
    try:
        success = setup.run_setup()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during setup: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
