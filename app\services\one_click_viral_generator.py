#!/usr/bin/env python3
"""
One-Click Viral Video Generator

Acest serviciu implementează workflow-ul complet automatizat pentru generarea
de videoclipuri virale cu o singură acțiune, folosind GPT4Free pentru toate
aspectele creative și strategice.
"""

import asyncio
import json
import time
from datetime import datetime
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from loguru import logger

# Import required models first
from app.models.schema import VideoAspect, VideoParams, VideoConcatMode

try:
    from app.services.gpt4free_service import gpt4free_service
    from app.services.viral_content_generator import (
        viral_content_generator, ViralTopic, ViralScript, ViralVideoPackage
    )
    from app.services.contextual_image_ai import contextual_image_ai, ContextualImagePlan
    GPT4FREE_AVAILABLE = True
except ImportError:
    GPT4FREE_AVAILABLE = False
    logger.warning("GPT4Free services not available for one-click viral generator")


@dataclass
class ViralVideoConfig:
    """Configurația pentru generarea unui videoclip viral"""
    category: str = "lifestyle_romania"
    target_audience: str = "tineri români 18-35"
    platform: str = "tiktok"
    duration: int = 60
    style: str = "captivant"
    use_contextual_images: bool = True
    max_contextual_images: int = 8
    voice_name: str = "ro-RO-EmilNeural-Male"
    include_subtitles: bool = True
    generate_hashtags: bool = True
    generate_description: bool = True
    optimize_for_seo: bool = True
    generate_video_file: bool = True
    voice_rate: str = "+0%"
    bgm_volume: float = 0.2


@dataclass
class ViralVideoResult:
    """Rezultatul generării unui videoclip viral"""
    success: bool
    video_package: Optional[ViralVideoPackage]
    contextual_image_plan: Optional[ContextualImagePlan]
    generation_time: float
    video_files: List[str]
    error_message: Optional[str] = None
    performance_predictions: Optional[Dict[str, Any]] = None
    task_id: Optional[str] = None
    audio_file: Optional[str] = None
    subtitle_file: Optional[str] = None


class OneClickViralGenerator:
    """Generator complet automatizat pentru videoclipuri virale"""
    
    def __init__(self):
        self.gpt4free_available = GPT4FREE_AVAILABLE and gpt4free_service.is_available()
        
        # Configurații predefinite pentru diferite platforme
        self.platform_configs = {
            "tiktok": {
                "duration": 60,
                "aspect_ratio": "9:16",
                "style": "quick_engaging",
                "hashtags_count": 5,
                "max_title_length": 100
            },
            "instagram_reels": {
                "duration": 90,
                "aspect_ratio": "9:16", 
                "style": "aesthetic_engaging",
                "hashtags_count": 10,
                "max_title_length": 125
            },
            "youtube_shorts": {
                "duration": 60,
                "aspect_ratio": "9:16",
                "style": "informative_engaging", 
                "hashtags_count": 3,
                "max_title_length": 100
            },
            "facebook": {
                "duration": 120,
                "aspect_ratio": "16:9",
                "style": "story_driven",
                "hashtags_count": 5,
                "max_title_length": 150
            }
        }
    
    def is_available(self) -> bool:
        """Verifică dacă generatorul este disponibil"""
        return self.gpt4free_available
    
    async def generate_viral_video_package(self, config: ViralVideoConfig) -> ViralVideoResult:
        """Generează un pachet complet pentru videoclip viral"""
        
        start_time = time.time()
        
        if not self.is_available():
            return ViralVideoResult(
                success=False,
                video_package=None,
                contextual_image_plan=None,
                generation_time=0.0,
                video_files=[],
                error_message="GPT4Free services not available"
            )
        
        try:
            logger.info(f"🚀 Începe generarea automată videoclip viral pentru {config.platform}")
            
            # Pasul 1: Generează subiect viral
            logger.info("📊 Generez subiect viral...")
            topic = await viral_content_generator.generate_viral_topic(
                category=config.category,
                target_audience=config.target_audience,
                platform=config.platform
            )
            
            if not topic:
                raise Exception("Nu s-a putut genera subiect viral")
            
            logger.info(f"✅ Subiect generat: {topic.title}")
            
            # Pasul 2: Generează script viral
            logger.info("📝 Generez script viral...")
            script = await viral_content_generator.generate_viral_script(
                topic=topic,
                duration=config.duration,
                platform=config.platform
            )
            
            if not script:
                raise Exception("Nu s-a putut genera script viral")
            
            logger.info(f"✅ Script generat ({len(script.script_text)} caractere)")
            
            # Pasul 3: Generează termeni de căutare
            logger.info("🔍 Generez termeni de căutare...")
            search_terms = await gpt4free_service.generate_terms(
                subject=topic.title,
                count=5
            )
            
            if not search_terms:
                search_terms = topic.trending_keywords[:5]
            
            # Pasul 4: Generează titlu și descriere
            logger.info("📰 Generez titlu și descriere...")
            title, description = await self._generate_title_and_description(
                topic, script, config.platform
            )
            
            # Pasul 5: Generează hashtag-uri
            logger.info("🏷️ Generez hashtag-uri...")
            hashtags = await self._generate_hashtags(
                topic, script, config.platform
            )
            
            # Pasul 6: Generează plan pentru imagini contextuale
            contextual_image_plan = None
            contextual_image_prompts = []
            
            if config.use_contextual_images:
                logger.info("🖼️ Generez plan pentru imagini contextuale...")
                contextual_image_plan = await contextual_image_ai.create_contextual_image_plan(
                    script=script.script_text,
                    duration=config.duration,
                    max_images=config.max_contextual_images
                )
                
                if contextual_image_plan:
                    contextual_image_prompts = [
                        prompt.prompt for prompt in contextual_image_plan.image_prompts
                    ]
                    logger.info(f"✅ Plan creat cu {len(contextual_image_prompts)} imagini")
            
            # Pasul 7: Generează prompt pentru thumbnail
            logger.info("🖼️ Generez prompt pentru thumbnail...")
            thumbnail_prompt = await self._generate_thumbnail_prompt(topic, script)
            
            # Pasul 8: Generează keywords SEO
            logger.info("🔍 Generez keywords SEO...")
            seo_keywords = await self._generate_seo_keywords(topic, script)
            
            # Pasul 9: Determină platformele țintă
            target_platforms = [config.platform]
            if config.platform == "tiktok":
                target_platforms.extend(["instagram_reels", "youtube_shorts"])
            
            # Creează pachetul complet
            video_package = ViralVideoPackage(
                topic=topic,
                script=script,
                title=title,
                description=description,
                hashtags=hashtags,
                search_terms=search_terms,
                contextual_image_prompts=contextual_image_prompts,
                thumbnail_prompt=thumbnail_prompt,
                seo_keywords=seo_keywords,
                target_platforms=target_platforms
            )
            
            # Generează predicții de performanță
            logger.info("📈 Generez predicții de performanță...")
            performance_predictions = await self._predict_performance(video_package)
            
            generation_time = time.time() - start_time
            
            logger.info(f"🎉 Pachet viral generat cu succes în {generation_time:.2f} secunde!")
            
            return ViralVideoResult(
                success=True,
                video_package=video_package,
                contextual_image_plan=contextual_image_plan,
                generation_time=generation_time,
                video_files=[],  # Va fi populat după generarea efectivă
                performance_predictions=performance_predictions
            )
            
        except Exception as e:
            generation_time = time.time() - start_time
            logger.error(f"❌ Eroare la generarea pachetului viral: {e}")
            
            return ViralVideoResult(
                success=False,
                video_package=None,
                contextual_image_plan=None,
                generation_time=generation_time,
                video_files=[],
                error_message=str(e)
            )

    async def generate_complete_viral_video(self, config: ViralVideoConfig) -> ViralVideoResult:
        """Generează un videoclip viral complet cu fișier video final"""
        start_time = time.time()

        try:
            logger.info("🎬 Începe generarea completă a videoclipului viral...")

            # Pasul 1: Generează pachetul de conținut viral
            content_result = await self.generate_viral_video_package(config)

            if not content_result.success:
                return content_result

            logger.info("✅ Pachet de conținut generat cu succes")

            # Pasul 2: Creează fișierul video complet
            video_result = await self._create_video_file(
                content_result.video_package,
                content_result.contextual_image_plan,
                config
            )

            # Actualizează rezultatul cu informațiile video
            content_result.video_files = video_result.get("video_files", [])
            content_result.task_id = video_result.get("task_id")
            content_result.audio_file = video_result.get("audio_file")
            content_result.subtitle_file = video_result.get("subtitle_file")
            content_result.generation_time = time.time() - start_time

            if content_result.video_files:
                logger.info(f"🎉 Videoclip viral complet generat: {content_result.video_files[0]}")
            else:
                logger.warning("⚠️ Videoclip generat dar fără fișiere video")

            return content_result

        except Exception as e:
            logger.error(f"❌ Eroare la generarea completă a videoclipului viral: {e}")
            return ViralVideoResult(
                success=False,
                video_package=None,
                contextual_image_plan=None,
                generation_time=time.time() - start_time,
                video_files=[],
                error_message=str(e)
            )

    async def _generate_title_and_description(self,
                                            topic: ViralTopic, 
                                            script: ViralScript,
                                            platform: str) -> Tuple[str, str]:
        """Generează titlu și descriere optimizate pentru platformă"""
        
        platform_config = self.platform_configs.get(platform, self.platform_configs["tiktok"])
        max_title_length = platform_config["max_title_length"]
        
        prompt = f"""
        Creează un titlu captivant și o descriere optimizată pentru platforma {platform}:
        
        Subiect: {topic.title}
        Script hook: {script.hook}
        Audiența țintă: {topic.target_audience}
        
        Cerințe:
        - Titlul să fie sub {max_title_length} caractere
        - Să fie captivant și să încurajeze click-urile
        - Să includă keywords relevante
        - Descrierea să fie optimizată SEO
        - Să includă call-to-action
        - Să fie în română
        
        Returnează în format JSON:
        {{
            "title": "titlu captivant",
            "description": "descriere detaliată cu call-to-action"
        }}
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o-mini",
                max_tokens=500,
                temperature=0.8
            )
            
            if response:
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group())
                    return data.get('title', topic.title), data.get('description', topic.description)
            
            return topic.title, topic.description
            
        except Exception as e:
            logger.error(f"❌ Eroare la generarea titlului și descrierii: {e}")
            return topic.title, topic.description
    
    async def _generate_hashtags(self, 
                               topic: ViralTopic, 
                               script: ViralScript,
                               platform: str) -> List[str]:
        """Generează hashtag-uri optimizate pentru platformă"""
        
        platform_config = self.platform_configs.get(platform, self.platform_configs["tiktok"])
        hashtags_count = platform_config["hashtags_count"]
        
        prompt = f"""
        Generează {hashtags_count} hashtag-uri virale pentru platforma {platform}:
        
        Subiect: {topic.title}
        Categorie: {topic.category}
        Keywords: {topic.trending_keywords}
        
        Hashtag-urile trebuie să:
        - Fie relevante pentru conținut
        - Aibă potențial viral
        - Includă hashtag-uri populare românești
        - Fie optimizate pentru {platform}
        - Includă mix de hashtag-uri generale și specifice
        
        Returnează doar hashtag-urile, câte unul pe linie, cu #.
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o-mini",
                max_tokens=200,
                temperature=0.7
            )
            
            if response:
                hashtags = [line.strip() for line in response.split('\n') if line.strip().startswith('#')]
                return hashtags[:hashtags_count]
            
            # Fallback hashtags
            return [f"#{keyword.replace(' ', '')}" for keyword in topic.trending_keywords[:hashtags_count]]
            
        except Exception as e:
            logger.error(f"❌ Eroare la generarea hashtag-urilor: {e}")
            return [f"#{keyword.replace(' ', '')}" for keyword in topic.trending_keywords[:hashtags_count]]

    async def _generate_thumbnail_prompt(self, topic: ViralTopic, script: ViralScript) -> str:
        """Generează prompt pentru thumbnail viral"""

        prompt = f"""
        Creează un prompt pentru thumbnail viral captivant:

        Subiect: {topic.title}
        Hook: {script.hook}
        Categorie: {topic.category}

        Thumbnail-ul trebuie să:
        - Fie extrem de captivant și să atragă click-uri
        - Reprezinte esența videoclipului
        - Fie optimizat pentru platformele sociale
        - Includă elemente vizuale puternice
        - Aibă potențial viral ridicat

        Returnează doar prompt-ul pentru thumbnail, fără explicații.
        """

        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o-mini",
                max_tokens=200,
                temperature=0.8
            )

            return response.strip() if response else f"Viral thumbnail for {topic.title}, captivating, high quality"

        except Exception as e:
            logger.error(f"❌ Eroare la generarea prompt-ului pentru thumbnail: {e}")
            return f"Viral thumbnail for {topic.title}, captivating, high quality"

    async def _generate_seo_keywords(self, topic: ViralTopic, script: ViralScript) -> List[str]:
        """Generează keywords SEO optimizate"""

        prompt = f"""
        Generează 10 keywords SEO pentru optimizarea videoclipului:

        Subiect: {topic.title}
        Categorie: {topic.category}
        Audiența țintă: {topic.target_audience}

        Keywords-urile trebuie să:
        - Fie relevante pentru conținut
        - Aibă volum de căutare ridicat
        - Includă variații în română
        - Fie optimizate pentru motoarele de căutare
        - Includă long-tail keywords

        Returnează doar keywords-urile, câte unul pe linie.
        """

        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o-mini",
                max_tokens=300,
                temperature=0.6
            )

            if response:
                keywords = [line.strip() for line in response.split('\n') if line.strip()]
                return keywords[:10]

            # Fallback keywords
            return topic.trending_keywords + [topic.title, topic.category]

        except Exception as e:
            logger.error(f"❌ Eroare la generarea keywords SEO: {e}")
            return topic.trending_keywords + [topic.title, topic.category]

    async def _create_video_file(self,
                               video_package: ViralVideoPackage,
                               contextual_image_plan: Optional[ContextualImagePlan],
                               config: ViralVideoConfig) -> Dict[str, Any]:
        """Creează fișierul video complet folosind pipeline-ul MoneyPrinterTurbo"""

        try:
            import uuid
            from app.services import task, voice, material, video, subtitle
            from app.services import state as sm
            from app.models import const

            # Generează un task ID unic
            task_id = str(uuid.uuid4())

            logger.info(f"🎬 Creez videoclip pentru task {task_id}")

            # Configurează parametrii video pentru platformă
            video_aspect = self._get_video_aspect_for_platform(config.platform)

            # Convertește voice_rate din string la float
            voice_rate_float = self._convert_voice_rate_to_float(config.voice_rate)

            logger.info(f"🔧 Voice rate conversion: {config.voice_rate} -> {voice_rate_float}")
            logger.info(f"🔧 Voice name: {config.voice_name}")
            logger.info(f"🔧 BGM volume: {config.bgm_volume}")

            # Creează parametrii video
            video_params = VideoParams(
                video_subject=video_package.topic.title,
                video_script=video_package.script.script_text,
                video_terms=video_package.search_terms,
                video_aspect=video_aspect,
                video_concat_mode=VideoConcatMode.random,
                video_clip_duration=5,  # 5 secunde per clip
                video_count=1,
                video_language="ro",  # Română
                video_source="pexels",  # Sursa video implicită
                voice_name=config.voice_name,  # Voce configurată
                voice_rate=voice_rate_float,  # Convertit la float
                voice_volume=1.0,  # Volum normal
                bgm_type="random",
                bgm_volume=config.bgm_volume,  # Volum configurat
                subtitle_enabled=config.include_subtitles,
                subtitle_position="bottom",
                font_name="Arial",
                text_fore_color="#FFFFFF",
                text_background_color="#000000",
                font_size=60,
                stroke_color="#000000",
                stroke_width=2,
                n_threads=2,
                paragraph_number=len(video_package.script.script_text.split('\n\n'))
            )

            # Inițializează task-ul
            sm.state.update_task(task_id, state=const.TASK_STATE_PROCESSING, progress=0)

            # Pasul 1: Generează audio din script
            logger.info("🎤 Generez audio din script...")
            audio_result = await self._generate_audio_with_details(task_id, video_params)

            if not audio_result or not audio_result.get("audio_file"):
                raise Exception("Nu s-a putut genera fișierul audio")

            audio_file = audio_result["audio_file"]
            sub_maker = audio_result.get("sub_maker")

            # Pasul 2: Generează sau obține imagini
            logger.info("🖼️ Generez imagini pentru video...")
            audio_duration = audio_result.get("audio_duration", config.duration)
            materials = await self._generate_materials(
                task_id,
                video_params,
                contextual_image_plan,
                config,
                audio_duration
            )

            if not materials:
                raise Exception("Nu s-au putut genera materialele video")

            # Pasul 3: Generează subtitluri
            logger.info("📝 Generez subtitluri...")
            subtitle_file = await self._generate_subtitles(task_id, video_params, audio_file, sub_maker)

            # Pasul 4: Combină totul într-un video final
            logger.info("🎬 Combinez audio, imagini și subtitluri...")
            video_files = await self._combine_video(
                task_id,
                video_params,
                audio_file,
                materials,
                subtitle_file
            )

            if not video_files:
                raise Exception("Nu s-a putut genera videoclipul final")

            # Marchează task-ul ca finalizat
            sm.state.update_task(task_id, state=const.TASK_STATE_COMPLETE, progress=100)

            return {
                "video_files": video_files,
                "task_id": task_id,
                "audio_file": audio_file,
                "subtitle_file": subtitle_file
            }

        except Exception as e:
            logger.error(f"❌ Eroare la crearea fișierului video: {e}")
            if 'task_id' in locals():
                sm.state.update_task(task_id, state=const.TASK_STATE_FAILED)
            raise e

    def _get_video_aspect_for_platform(self, platform: str) -> VideoAspect:
        """Returnează aspect ratio-ul video pentru platformă"""

        platform_aspects = {
            "tiktok": VideoAspect.portrait,
            "instagram_reels": VideoAspect.portrait,
            "youtube_shorts": VideoAspect.portrait,
            "youtube": VideoAspect.landscape,
            "facebook": VideoAspect.landscape
        }

        return platform_aspects.get(platform, VideoAspect.portrait)

    def _convert_voice_rate_to_float(self, voice_rate_str: str) -> float:
        """Convertește voice_rate din string (ex: '+10%') la float (ex: 1.1)"""
        try:
            # Elimină semnul '%' și convertește la float
            rate_str = voice_rate_str.replace('%', '')

            # Convertește la float și calculează multiplicatorul
            rate_percent = float(rate_str)
            rate_multiplier = 1.0 + (rate_percent / 100.0)

            # Limitează între 0.5 și 2.0 pentru siguranță
            return max(0.5, min(2.0, rate_multiplier))

        except (ValueError, TypeError):
            # Fallback la viteza normală
            return 1.0

    async def _generate_audio_with_details(self, task_id: str, video_params: VideoParams) -> Optional[Dict[str, Any]]:
        """Generează fișierul audio din script cu detalii complete"""
        try:
            from app.services.task import generate_audio

            # Generează audio folosind serviciul task
            audio_file, audio_duration, sub_maker = generate_audio(
                task_id=task_id,
                params=video_params,
                video_script=video_params.video_script
            )

            return {
                "audio_file": audio_file,
                "audio_duration": audio_duration,
                "sub_maker": sub_maker
            }

        except Exception as e:
            logger.error(f"❌ Eroare la generarea audio: {e}")
            return None

    async def _generate_materials(self,
                                task_id: str,
                                video_params: VideoParams,
                                contextual_image_plan: Optional[ContextualImagePlan],
                                config: ViralVideoConfig,
                                audio_duration: float = 60.0) -> Optional[List]:
        """Generează materialele video (imagini)"""
        try:
            from app.services.task import get_video_materials, generate_ai_video_materials
            from app.services.material import download_videos

            # Încearcă să genereze materiale AI dacă avem imagini contextuale
            if contextual_image_plan and contextual_image_plan.image_prompts:
                logger.info(f"📸 Generez {len(contextual_image_plan.image_prompts)} imagini AI contextuale")

                try:
                    # Setează sursa video la AI pentru generarea de imagini
                    video_params.video_source = "ai_stability"

                    # Extrage prompt-urile pentru generarea AI
                    ai_terms = [prompt.prompt for prompt in contextual_image_plan.image_prompts]

                    # Generează materiale AI
                    materials = await generate_ai_video_materials(
                        task_id=task_id,
                        params=video_params,
                        video_terms=ai_terms,
                        audio_duration=audio_duration
                    )

                    if materials:
                        logger.info(f"✅ Generat {len(materials)} materiale AI")
                        return materials

                except Exception as e:
                    logger.warning(f"⚠️ Generarea AI a eșuat: {e}, folosesc fallback")

            # Fallback: folosește materiale video tradiționale
            logger.info("📸 Generez materiale video tradiționale")

            # Setează sursa video la pexels pentru materiale tradiționale
            video_params.video_source = "pexels"

            # Folosește termenii de căutare din pachet
            search_terms = video_params.video_terms[:5]  # Limitează la 5 termeni

            # Descarcă materiale video
            materials = download_videos(
                task_id=task_id,
                search_terms=search_terms,
                source="pexels",
                video_aspect=video_params.video_aspect,
                video_contact_mode=video_params.video_concat_mode,
                audio_duration=audio_duration,
                max_clip_duration=video_params.video_clip_duration,
                script_context=video_params.video_script[:200],  # Primele 200 caractere pentru context
                ai_style="realistic",
                ai_provider="auto"
            )

            if materials:
                logger.info(f"✅ Descărcat {len(materials)} materiale video")
                return materials
            else:
                logger.warning("⚠️ Nu s-au putut descărca materiale video")
                return []

        except Exception as e:
            logger.error(f"❌ Eroare la generarea materialelor: {e}")
            return None

    async def _generate_subtitles(self,
                                task_id: str,
                                video_params: VideoParams,
                                audio_file: str,
                                sub_maker=None) -> Optional[str]:
        """Generează fișierul de subtitluri"""
        try:
            from app.services.task import generate_subtitle

            if not video_params.subtitle_enabled:
                return None

            # Generează subtitluri folosind serviciul task
            subtitle_file = generate_subtitle(
                task_id=task_id,
                params=video_params,
                video_script=video_params.video_script,
                sub_maker=sub_maker,
                audio_file=audio_file
            )

            return subtitle_file

        except Exception as e:
            logger.error(f"❌ Eroare la generarea subtitlurilor: {e}")
            return None

    async def _combine_video(self,
                           task_id: str,
                           video_params: VideoParams,
                           audio_file: str,
                           materials: List,
                           subtitle_file: Optional[str]) -> Optional[List[str]]:
        """Combină audio, imagini și subtitluri într-un video final"""
        try:
            from app.services.task import generate_video

            # Combină toate elementele într-un video final
            video_files = generate_video(
                task_id=task_id,
                params=video_params,
                audio_file=audio_file,
                materials=materials,
                subtitle_file=subtitle_file
            )

            return video_files

        except Exception as e:
            logger.error(f"❌ Eroare la combinarea video: {e}")
            return None

    async def _predict_performance(self, video_package: ViralVideoPackage) -> Dict[str, Any]:
        """Generează predicții de performanță pentru videoclip"""

        prompt = f"""
        Analizează și prezice performanța acestui videoclip viral:

        Subiect: {video_package.topic.title}
        Potențial viral: {video_package.topic.viral_potential}
        Relevanță română: {video_package.topic.romanian_relevance}
        Categorie: {video_package.topic.category}
        Platforme țintă: {video_package.target_platforms}

        Estimează în format JSON:
        {{
            "estimated_views": "10K-50K",
            "estimated_engagement_rate": "5-8%",
            "viral_probability": "high/medium/low",
            "best_posting_time": "18:00-21:00",
            "target_demographics": "18-30 ani, urban",
            "success_factors": ["factor1", "factor2"],
            "improvement_suggestions": ["sugestie1", "sugestie2"]
        }}
        """

        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o-mini",
                max_tokens=500,
                temperature=0.7
            )

            if response:
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())

            # Fallback predictions
            return {
                "estimated_views": "5K-25K",
                "estimated_engagement_rate": "4-7%",
                "viral_probability": "medium",
                "best_posting_time": "18:00-21:00",
                "target_demographics": video_package.topic.target_audience,
                "success_factors": ["conținut relevant", "timing bun"],
                "improvement_suggestions": ["optimizează hashtag-urile", "îmbunătățește hook-ul"]
            }

        except Exception as e:
            logger.error(f"❌ Eroare la predicția performanței: {e}")
            return {
                "estimated_views": "5K-25K",
                "estimated_engagement_rate": "4-7%",
                "viral_probability": "medium",
                "best_posting_time": "18:00-21:00",
                "target_demographics": video_package.topic.target_audience,
                "success_factors": ["conținut relevant"],
                "improvement_suggestions": ["optimizează conținutul"]
            }


# Global service instance
one_click_viral_generator = OneClickViralGenerator()


async def test_one_click_viral_generator():
    """Test pentru generatorul viral one-click"""
    print("🧪 Testing One-Click Viral Generator...")
    
    if not one_click_viral_generator.is_available():
        print("❌ One-Click Viral Generator not available")
        return False
    
    # Configurație de test
    config = ViralVideoConfig(
        category="food_romania",
        target_audience="tineri români 18-30",
        platform="tiktok",
        duration=60,
        use_contextual_images=True,
        max_contextual_images=5
    )
    
    print(f"🎯 Testing viral video package generation for {config.platform}...")
    result = await one_click_viral_generator.generate_viral_video_package(config)
    
    if result.success:
        print(f"✅ Viral video package generated successfully!")
        print(f"⏱️ Generation time: {result.generation_time:.2f} seconds")
        print(f"📰 Title: {result.video_package.title}")
        print(f"🏷️ Hashtags: {', '.join(result.video_package.hashtags[:3])}...")
        print(f"🔍 Search terms: {', '.join(result.video_package.search_terms[:3])}...")
        if result.contextual_image_plan:
            print(f"🖼️ Contextual images: {len(result.contextual_image_plan.image_prompts)}")
        return True
    else:
        print(f"❌ Generation failed: {result.error_message}")
        return False


if __name__ == "__main__":
    asyncio.run(test_one_click_viral_generator())
