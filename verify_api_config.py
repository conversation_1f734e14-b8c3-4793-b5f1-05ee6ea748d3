#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verification script for API configuration accessibility in MoneyPrinterTurbo
"""

import os
import sys
from pathlib import Path

# Add the root directory to the path
root_dir = Path(__file__).parent
sys.path.append(str(root_dir))

from app.config import config
from app.utils import utils

def check_basic_settings_visibility():
    """Check if Basic Settings panel is configured to be visible"""
    print("🔍 Checking Basic Settings panel visibility...")
    
    hide_config = config.app.get("hide_config", False)
    
    if hide_config:
        print("❌ Basic Settings panel is HIDDEN")
        print("   To fix: Set 'hide_config = false' in config.toml")
        return False
    else:
        print("✅ Basic Settings panel is VISIBLE")
        return True

def check_api_key_fields():
    """Check which API key fields are available in configuration"""
    print("\n🔑 Checking available API key configuration fields...")
    
    api_fields = {
        "Pexels API Keys": config.app.get("pexels_api_keys", []),
        "Pixabay API Keys": config.app.get("pixabay_api_keys", []),
        "OpenAI API Key": config.app.get("openai_api_key", ""),
        "Moonshot API Key": config.app.get("moonshot_api_key", ""),
        "Azure API Key": config.app.get("azure_api_key", ""),
        "Qwen API Key": config.app.get("qwen_api_key", ""),
        "DeepSeek API Key": config.app.get("deepseek_api_key", ""),
        "Gemini API Key": config.app.get("gemini_api_key", ""),
        "Azure Speech Key": config.azure.get("speech_key", ""),
        "Azure Speech Region": config.azure.get("speech_region", ""),
        "SiliconFlow API Key": config.siliconflow.get("api_key", ""),
    }
    
    configured_count = 0
    for field_name, field_value in api_fields.items():
        if field_value:
            if isinstance(field_value, list) and len(field_value) > 0:
                print(f"   ✅ {field_name}: {len(field_value)} key(s) configured")
                configured_count += 1
            elif isinstance(field_value, str) and field_value.strip():
                print(f"   ✅ {field_name}: Configured")
                configured_count += 1
            else:
                print(f"   ⚪ {field_name}: Available but not configured")
        else:
            print(f"   ⚪ {field_name}: Available but not configured")
    
    print(f"\n📊 Summary: {configured_count}/{len(api_fields)} API fields are configured")
    return configured_count

def check_romanian_translation():
    """Check if Romanian translations for API settings are available"""
    print("\n🇷🇴 Checking Romanian translations for API settings...")
    
    i18n_dir = root_dir / "webui" / "i18n"
    locales = utils.load_locales(str(i18n_dir))
    
    if "ro" not in locales:
        print("❌ Romanian translation file not found")
        return False
    
    ro_translations = locales["ro"].get("Translation", {})
    
    api_related_keys = [
        "Basic Settings",
        "API Key",
        "Pexels API Key",
        "Pixabay API Key",
        "LLM Provider",
        "Speech Key",
        "Speech Region",
        "Please Enter the LLM API Key",
        "Please Enter the Pexels API Key"
    ]
    
    missing_translations = []
    for key in api_related_keys:
        if key not in ro_translations:
            missing_translations.append(key)
        else:
            print(f"   ✅ '{key}' -> '{ro_translations[key][:50]}...'")
    
    if missing_translations:
        print(f"   ❌ Missing translations: {missing_translations}")
        return False
    else:
        print("   ✅ All API-related translations are available")
        return True

def check_current_language_setting():
    """Check current UI language setting"""
    print("\n🌐 Checking current language setting...")
    
    current_language = config.ui.get("language", "en")
    print(f"   Current UI language: {current_language}")
    
    if current_language == "ro":
        print("   ✅ Interface is set to Romanian")
    elif current_language == "en":
        print("   ℹ️ Interface is set to English")
    else:
        print(f"   ⚠️ Interface is set to: {current_language}")
    
    return current_language

def provide_quick_fixes():
    """Provide quick fix commands for common issues"""
    print("\n🔧 Quick Fix Commands:")
    print("=" * 50)
    
    print("\n1. To show Basic Settings panel:")
    print('   powershell -Command "(Get-Content config.toml) -replace \'hide_config = true\', \'hide_config = false\' | Set-Content config.toml"')
    
    print("\n2. To set interface to Romanian:")
    print('   powershell -Command "(Get-Content config.toml) -replace \'language = \\"en\\"\', \'language = \\"ro\\"\' | Set-Content config.toml"')
    
    print("\n3. To set interface to English:")
    print('   powershell -Command "(Get-Content config.toml) -replace \'language = \\"ro\\"\', \'language = \\"en\\"\' | Set-Content config.toml"')
    
    print("\n4. To restart web interface:")
    print("   .\\start_webui.bat")

def main():
    """Run all verification checks"""
    print("🔍 MoneyPrinterTurbo API Configuration Verification")
    print("=" * 60)
    
    # Check basic settings visibility
    settings_visible = check_basic_settings_visibility()
    
    # Check API key fields
    configured_apis = check_api_key_fields()
    
    # Check Romanian translations
    romanian_ok = check_romanian_translation()
    
    # Check current language
    current_lang = check_current_language_setting()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 60)
    
    if settings_visible:
        print("✅ Basic Settings panel is visible and accessible")
    else:
        print("❌ Basic Settings panel is hidden - users cannot access API configuration")
    
    if configured_apis > 0:
        print(f"✅ {configured_apis} API key(s) are already configured")
    else:
        print("⚠️ No API keys are configured yet - users need to add them")
    
    if romanian_ok:
        print("✅ Romanian translations for API settings are complete")
    else:
        print("❌ Romanian translations are incomplete")
    
    print(f"ℹ️ Current interface language: {current_lang}")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    if not settings_visible:
        print("   1. Run the quick fix command to show Basic Settings panel")
    if configured_apis == 0:
        print("   2. Configure at least Pexels API key and one LLM provider API key")
    if current_lang != "ro":
        print("   3. Consider setting interface to Romanian for Romanian users")
    
    print("\n🌐 Access the interface at: http://localhost:8501")
    
    # Provide quick fixes
    provide_quick_fixes()

if __name__ == "__main__":
    main()
