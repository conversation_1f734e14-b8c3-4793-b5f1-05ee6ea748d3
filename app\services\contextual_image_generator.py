"""
Contextual Image Generator for MoneyPrinterTurbo

This service generates AI images based on script context using the existing
AI image generation infrastructure (Perchance, Stable Diffusion, etc.)
"""

import asyncio
import logging
import os
import tempfile
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import time

from app.services.script_analyzer import ScriptAnalyzer
from app.services.ai_video_source_manager import AIVideoSourceManager, AIImageConfig, AIProvider
from app.models.schema import VideoParams

logger = logging.getLogger(__name__)


@dataclass
class ContextualImage:
    """Represents a contextually generated image"""
    image_path: str
    start_time: float
    end_time: float
    duration: float
    prompt: str
    script_text: str
    scene_type: str
    emotional_tone: str
    priority: float
    generation_time: float
    provider_used: str
    file_size: int


@dataclass
class ImageGenerationConfig:
    """Configuration for contextual image generation"""
    enabled: bool = True
    max_images: int = 10
    min_gap_seconds: float = 5.0
    image_duration: float = 3.0
    style: str = "realistic"
    ai_provider: str = "auto"
    width: int = 1080
    height: int = 1920
    quality_threshold: float = 0.5
    fallback_to_stock: bool = True


class ContextualImageGenerator:
    """Generates contextual AI images based on script analysis"""
    
    def __init__(self):
        self.script_analyzer = ScriptAnalyzer()
        self.ai_manager = None
        self.config = ImageGenerationConfig()
        
    async def initialize(self):
        """Initialize the AI image generation manager"""
        try:
            self.ai_manager = AIVideoSourceManager()
            logger.info("Contextual image generator initialized")
        except Exception as e:
            logger.error(f"Failed to initialize AI manager: {e}")
            self.ai_manager = None

    async def generate_contextual_images(self, script: str, audio_duration: float,
                                       params: VideoParams, task_id: str) -> List[ContextualImage]:
        """
        Generate contextual images based on script analysis
        
        Args:
            script: The video script text
            audio_duration: Duration of the audio in seconds
            params: Video generation parameters
            task_id: Task ID for file organization
            
        Returns:
            List of generated contextual images
        """
        if not self.config.enabled:
            logger.info("Contextual image generation disabled")
            return []
            
        if not self.ai_manager:
            await self.initialize()
            if not self.ai_manager:
                logger.error("AI manager not available for contextual image generation")
                return []
        
        logger.info(f"Starting contextual image generation for task {task_id}")
        
        try:
            # Update config from params
            self._update_config_from_params(params)
            
            # Analyze script for image generation
            image_data = self.script_analyzer.analyze_for_image_generation(
                script, audio_duration, self.config.style
            )
            
            if not image_data:
                logger.warning("No suitable segments found for image generation")
                return []
            
            # Limit number of images
            image_data = image_data[:self.config.max_images]
            
            # Generate images for each segment
            generated_images = []
            for i, segment_data in enumerate(image_data):
                try:
                    logger.info(f"Generating image {i+1}/{len(image_data)}: {segment_data['prompt'][:50]}...")
                    
                    image = await self._generate_single_image(segment_data, task_id, i)
                    if image:
                        generated_images.append(image)
                        logger.info(f"Successfully generated image {i+1}")
                    else:
                        logger.warning(f"Failed to generate image {i+1}")
                        
                except Exception as e:
                    logger.error(f"Error generating image {i+1}: {e}")
                    continue
            
            logger.info(f"Generated {len(generated_images)} contextual images")
            return generated_images
            
        except Exception as e:
            logger.error(f"Contextual image generation failed: {e}")
            return []

    async def _generate_single_image(self, segment_data: Dict[str, Any], 
                                   task_id: str, index: int) -> Optional[ContextualImage]:
        """Generate a single contextual image"""
        start_time = time.time()
        
        try:
            # Configure AI image generation
            config = AIImageConfig()
            config.width = self.config.width
            config.height = self.config.height
            config.provider = self._get_ai_provider()
            
            # Generate image
            result = await self.ai_manager.generate_image(segment_data['prompt'], config)
            
            if not result.success:
                logger.error(f"AI image generation failed: {result.error_message}")
                return None
            
            # Save image to task directory
            image_path = self._save_image(result.image_data, task_id, index)
            if not image_path:
                return None
            
            generation_time = time.time() - start_time
            
            # Create ContextualImage object
            contextual_image = ContextualImage(
                image_path=image_path,
                start_time=segment_data['start_time'],
                end_time=segment_data['end_time'],
                duration=segment_data['duration'],
                prompt=segment_data['prompt'],
                script_text=segment_data['text'],
                scene_type=segment_data['scene_type'],
                emotional_tone=segment_data['emotional_tone'],
                priority=segment_data['priority'],
                generation_time=generation_time,
                provider_used=result.provider_used,
                file_size=len(result.image_data)
            )
            
            return contextual_image
            
        except Exception as e:
            logger.error(f"Error in single image generation: {e}")
            return None

    def _save_image(self, image_data: bytes, task_id: str, index: int) -> Optional[str]:
        """Save generated image to task directory"""
        try:
            # Create task directory if it doesn't exist
            task_dir = os.path.join("storage", "tasks", task_id)
            os.makedirs(task_dir, exist_ok=True)
            
            # Create contextual images subdirectory
            images_dir = os.path.join(task_dir, "contextual_images")
            os.makedirs(images_dir, exist_ok=True)
            
            # Save image
            image_filename = f"contextual_image_{index:03d}.png"
            image_path = os.path.join(images_dir, image_filename)
            
            with open(image_path, 'wb') as f:
                f.write(image_data)
            
            logger.debug(f"Saved contextual image: {image_path}")
            return image_path
            
        except Exception as e:
            logger.error(f"Failed to save image: {e}")
            return None

    def _update_config_from_params(self, params: VideoParams):
        """Update generation config from video parameters"""
        # Update style from AI style parameter
        if hasattr(params, 'ai_style') and params.ai_style:
            self.config.style = params.ai_style
        
        # Update AI provider
        if hasattr(params, 'ai_provider') and params.ai_provider:
            self.config.ai_provider = params.ai_provider
        
        # Update dimensions based on video aspect ratio
        if params.video_aspect.value == "9:16":  # Portrait
            self.config.width = 1080
            self.config.height = 1920
        elif params.video_aspect.value == "16:9":  # Landscape
            self.config.width = 1920
            self.config.height = 1080
        else:  # Square
            self.config.width = 1080
            self.config.height = 1080

    def _get_ai_provider(self) -> AIProvider:
        """Get AI provider based on configuration"""
        provider_map = {
            'perchance': AIProvider.PERCHANCE,
            'stable_diffusion': AIProvider.STABLE_DIFFUSION,
            'auto': AIProvider.PERCHANCE  # Default to Perchance
        }
        
        return provider_map.get(self.config.ai_provider, AIProvider.PERCHANCE)

    def get_images_for_timeframe(self, images: List[ContextualImage], 
                                start_time: float, end_time: float) -> List[ContextualImage]:
        """Get contextual images that overlap with the given timeframe"""
        overlapping_images = []
        
        for image in images:
            # Check if image timeframe overlaps with requested timeframe
            if (image.start_time <= end_time and image.end_time >= start_time):
                overlapping_images.append(image)
        
        return overlapping_images

    def create_image_timeline(self, images: List[ContextualImage], 
                            total_duration: float) -> List[Dict[str, Any]]:
        """Create a timeline of when to show each contextual image"""
        timeline = []
        
        for image in images:
            timeline.append({
                'start_time': image.start_time,
                'end_time': min(image.end_time, image.start_time + self.config.image_duration),
                'image_path': image.image_path,
                'prompt': image.prompt,
                'scene_type': image.scene_type,
                'priority': image.priority
            })
        
        # Sort by start time
        timeline.sort(key=lambda x: x['start_time'])
        
        return timeline

    async def cleanup(self):
        """Cleanup resources"""
        if self.ai_manager:
            await self.ai_manager.cleanup()

    def get_generation_stats(self, images: List[ContextualImage]) -> Dict[str, Any]:
        """Get statistics about generated images"""
        if not images:
            return {}
        
        total_generation_time = sum(img.generation_time for img in images)
        total_file_size = sum(img.file_size for img in images)
        
        providers_used = {}
        scene_types = {}
        emotional_tones = {}
        
        for img in images:
            providers_used[img.provider_used] = providers_used.get(img.provider_used, 0) + 1
            scene_types[img.scene_type] = scene_types.get(img.scene_type, 0) + 1
            emotional_tones[img.emotional_tone] = emotional_tones.get(img.emotional_tone, 0) + 1
        
        return {
            'total_images': len(images),
            'total_generation_time': total_generation_time,
            'average_generation_time': total_generation_time / len(images),
            'total_file_size_mb': total_file_size / (1024 * 1024),
            'providers_used': providers_used,
            'scene_types': scene_types,
            'emotional_tones': emotional_tones,
            'coverage_percentage': (len(images) * self.config.image_duration) / max(
                img.end_time for img in images) * 100 if images else 0
        }

    def export_image_data(self, images: List[ContextualImage], output_path: str):
        """Export contextual image data to JSON file"""
        import json
        
        data = {
            'generation_config': {
                'max_images': self.config.max_images,
                'style': self.config.style,
                'ai_provider': self.config.ai_provider,
                'dimensions': f"{self.config.width}x{self.config.height}"
            },
            'images': [
                {
                    'image_path': img.image_path,
                    'start_time': img.start_time,
                    'end_time': img.end_time,
                    'duration': img.duration,
                    'prompt': img.prompt,
                    'script_text': img.script_text,
                    'scene_type': img.scene_type,
                    'emotional_tone': img.emotional_tone,
                    'priority': img.priority,
                    'generation_time': img.generation_time,
                    'provider_used': img.provider_used,
                    'file_size': img.file_size
                }
                for img in images
            ],
            'stats': self.get_generation_stats(images)
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Contextual image data exported to {output_path}")
