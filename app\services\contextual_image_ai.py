#!/usr/bin/env python3
"""
Contextual Image AI Service

Acest serviciu folosește GPT4Free pentru generarea automată de prompt-uri
pentru imagini contextuale bazate pe analiza detaliată a scriptului.
"""

import asyncio
import json
import re
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from loguru import logger

try:
    from app.services.gpt4free_service import gpt4free_service
    from app.services.viral_content_generator import ViralScript, ViralTopic
    GPT4FREE_AVAILABLE = True
except ImportError:
    GPT4FREE_AVAILABLE = False
    logger.warning("GPT4Free service not available for contextual image AI")


@dataclass
class ImagePrompt:
    """Prompt pentru generarea unei imagini contextuale"""
    segment_text: str
    prompt: str
    style: str
    negative_prompt: str
    timing: Tuple[float, float]  # (start_time, end_time)
    importance: float  # 0.0 - 1.0
    visual_elements: List[str]
    mood: str
    color_palette: List[str]


@dataclass
class ContextualImagePlan:
    """Plan complet pentru imaginile contextuale ale unui videoclip"""
    script_analysis: Dict[str, Any]
    image_prompts: List[ImagePrompt]
    style_consistency: Dict[str, str]
    thumbnail_prompt: str
    overall_mood: str
    visual_theme: str


class ContextualImageAI:
    """Generator AI pentru imagini contextuale"""
    
    def __init__(self):
        self.gpt4free_available = GPT4FREE_AVAILABLE and gpt4free_service.is_available()
        
        # Stiluri vizuale disponibile
        self.visual_styles = {
            "realistic": "photorealistic, high quality, detailed",
            "cinematic": "cinematic lighting, dramatic, film-like",
            "artistic": "artistic, creative, stylized",
            "minimalist": "clean, simple, minimalist design",
            "vibrant": "colorful, vibrant, energetic",
            "moody": "atmospheric, moody lighting, dramatic shadows",
            "modern": "modern, contemporary, sleek design",
            "traditional": "traditional, classic, timeless"
        }
        
        # Paleta de culori pentru diferite mood-uri
        self.mood_colors = {
            "energetic": ["bright orange", "electric blue", "vibrant yellow"],
            "calm": ["soft blue", "gentle green", "warm beige"],
            "dramatic": ["deep red", "dark purple", "golden yellow"],
            "professional": ["navy blue", "charcoal gray", "crisp white"],
            "romantic": ["soft pink", "warm gold", "lavender"],
            "mysterious": ["deep purple", "midnight blue", "silver"],
            "natural": ["forest green", "earth brown", "sky blue"],
            "festive": ["bright red", "gold", "emerald green"]
        }
    
    def is_available(self) -> bool:
        """Verifică dacă serviciul este disponibil"""
        return self.gpt4free_available
    
    async def analyze_script_for_visuals(self, script: str) -> Dict[str, Any]:
        """Analizează scriptul pentru a identifica elementele vizuale cheie"""
        
        if not self.is_available():
            logger.warning("Contextual Image AI not available")
            return {}
        
        prompt = f"""
        Analizează următorul script pentru videoclip și identifică elementele vizuale cheie
        pentru generarea de imagini contextuale:
        
        SCRIPT:
        {script}
        
        Analizează și returnează în format JSON:
        1. Segmentele principale ale scriptului cu timing estimat
        2. Elementele vizuale cheie pentru fiecare segment
        3. Mood-ul general și atmosfera
        4. Paleta de culori recomandată
        5. Stilul vizual potrivit
        6. Elemente culturale românești (dacă există)
        
        Format JSON:
        {{
            "segments": [
                {{
                    "text": "textul segmentului",
                    "start_time": 0.0,
                    "end_time": 10.0,
                    "visual_elements": ["element1", "element2"],
                    "mood": "energetic/calm/dramatic",
                    "importance": 0.8
                }}
            ],
            "overall_mood": "mood general",
            "visual_style": "realistic/cinematic/artistic",
            "color_palette": ["color1", "color2", "color3"],
            "cultural_elements": ["element românesc 1", "element românesc 2"],
            "visual_theme": "tema vizuală generală"
        }}
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o",
                max_tokens=2000,
                temperature=0.7
            )
            
            if response:
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group())
                    logger.info(f"✅ Analizat script pentru {len(data.get('segments', []))} segmente vizuale")
                    return data
            
            logger.warning("Nu s-a putut analiza scriptul pentru elemente vizuale")
            return {}
            
        except Exception as e:
            logger.error(f"❌ Eroare la analiza scriptului pentru vizuale: {e}")
            return {}
    
    async def generate_image_prompt(self, 
                                  segment_text: str,
                                  visual_elements: List[str],
                                  style: str = "realistic",
                                  mood: str = "neutral",
                                  cultural_context: str = "romanian") -> Optional[ImagePrompt]:
        """Generează un prompt detaliat pentru o imagine contextuală"""
        
        if not self.is_available():
            return None
        
        style_desc = self.visual_styles.get(style, self.visual_styles["realistic"])
        mood_colors = self.mood_colors.get(mood, ["neutral tones"])
        
        prompt = f"""
        Creează un prompt detaliat pentru generarea unei imagini contextuale bazată pe:
        
        Text segment: "{segment_text}"
        Elemente vizuale: {visual_elements}
        Stil: {style} ({style_desc})
        Mood: {mood}
        Context cultural: {cultural_context}
        Paleta de culori: {mood_colors}
        
        Cerințe pentru prompt:
        1. Să fie descriptiv și specific
        2. Să includă detalii tehnice pentru calitate înaltă
        3. Să respecte stilul și mood-ul cerut
        4. Să fie potrivit pentru AI image generation
        5. Să includă elemente culturale relevante (dacă aplicabil)
        6. Să evite conținut problematic sau copyright
        
        Returnează în format JSON:
        {{
            "prompt": "prompt principal detaliat pentru imagine",
            "style": "{style}",
            "negative_prompt": "elemente de evitat",
            "visual_elements": ["element1", "element2"],
            "mood": "{mood}",
            "color_palette": ["color1", "color2"],
            "technical_specs": "specificații tehnice (4K, HDR, etc.)"
        }}
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o",
                max_tokens=1000,
                temperature=0.8
            )
            
            if response:
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group())
                    
                    image_prompt = ImagePrompt(
                        segment_text=segment_text,
                        prompt=data.get('prompt', ''),
                        style=data.get('style', style),
                        negative_prompt=data.get('negative_prompt', ''),
                        timing=(0.0, 0.0),  # Va fi setat ulterior
                        importance=0.8,  # Va fi calculat ulterior
                        visual_elements=data.get('visual_elements', visual_elements),
                        mood=data.get('mood', mood),
                        color_palette=data.get('color_palette', mood_colors)
                    )
                    
                    logger.info(f"✅ Generat prompt pentru imagine contextuală")
                    return image_prompt
            
            logger.warning("Nu s-a putut genera prompt pentru imagine")
            return None
            
        except Exception as e:
            logger.error(f"❌ Eroare la generarea prompt-ului pentru imagine: {e}")
            return None
    
    async def create_contextual_image_plan(self, 
                                         script: str,
                                         duration: int = 60,
                                         max_images: int = 10) -> Optional[ContextualImagePlan]:
        """Creează un plan complet pentru imaginile contextuale"""
        
        if not self.is_available():
            return None
        
        # Analizează scriptul
        script_analysis = await self.analyze_script_for_visuals(script)
        if not script_analysis:
            return None
        
        # Generează prompt-uri pentru fiecare segment
        image_prompts = []
        segments = script_analysis.get('segments', [])
        
        # Limitează numărul de segmente la max_images
        if len(segments) > max_images:
            # Selectează segmentele cele mai importante
            segments = sorted(segments, key=lambda x: x.get('importance', 0.5), reverse=True)[:max_images]
        
        for i, segment in enumerate(segments):
            prompt = await self.generate_image_prompt(
                segment_text=segment.get('text', ''),
                visual_elements=segment.get('visual_elements', []),
                style=script_analysis.get('visual_style', 'realistic'),
                mood=segment.get('mood', script_analysis.get('overall_mood', 'neutral')),
                cultural_context="romanian"
            )
            
            if prompt:
                # Calculează timing-ul bazat pe poziția în script
                segment_duration = duration / len(segments)
                start_time = i * segment_duration
                end_time = start_time + segment_duration
                
                prompt.timing = (start_time, end_time)
                prompt.importance = segment.get('importance', 0.8)
                
                image_prompts.append(prompt)
        
        # Generează prompt pentru thumbnail
        thumbnail_prompt = await self._generate_thumbnail_prompt(script_analysis)
        
        plan = ContextualImagePlan(
            script_analysis=script_analysis,
            image_prompts=image_prompts,
            style_consistency={
                "style": script_analysis.get('visual_style', 'realistic'),
                "color_palette": script_analysis.get('color_palette', []),
                "mood": script_analysis.get('overall_mood', 'neutral')
            },
            thumbnail_prompt=thumbnail_prompt,
            overall_mood=script_analysis.get('overall_mood', 'neutral'),
            visual_theme=script_analysis.get('visual_theme', 'general')
        )
        
        logger.info(f"✅ Creat plan pentru {len(image_prompts)} imagini contextuale")
        return plan
    
    async def _generate_thumbnail_prompt(self, script_analysis: Dict[str, Any]) -> str:
        """Generează prompt pentru thumbnail-ul videoclipului"""
        
        prompt = f"""
        Creează un prompt pentru thumbnail captivant bazat pe analiza scriptului:
        
        Mood general: {script_analysis.get('overall_mood', 'neutral')}
        Stil vizual: {script_analysis.get('visual_style', 'realistic')}
        Tema vizuală: {script_analysis.get('visual_theme', 'general')}
        Elemente culturale: {script_analysis.get('cultural_elements', [])}
        
        Thumbnail-ul trebuie să:
        - Fie captivant și să atragă click-uri
        - Reprezinte esența videoclipului
        - Fie optimizat pentru platformele sociale
        - Includă elemente vizuale puternice
        - Respecte stilul general al videoclipului
        
        Returnează doar prompt-ul pentru thumbnail, fără explicații suplimentare.
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o-mini",
                max_tokens=300,
                temperature=0.8
            )
            
            return response.strip() if response else "Captivating thumbnail image, high quality, engaging"
            
        except Exception as e:
            logger.error(f"❌ Eroare la generarea prompt-ului pentru thumbnail: {e}")
            return "Captivating thumbnail image, high quality, engaging"


# Global service instance
contextual_image_ai = ContextualImageAI()


async def test_contextual_image_ai():
    """Test pentru generatorul de imagini contextuale"""
    print("🧪 Testing Contextual Image AI...")
    
    if not contextual_image_ai.is_available():
        print("❌ Contextual Image AI not available")
        return False
    
    # Script de test
    test_script = """
    Vrei să impresionezi pe toată lumea cu mâncarea ta? Astăzi îți arăt 3 rețete românești 
    rapide care se fac în doar 15 minute! Prima rețetă: mici de casă cu ingrediente secrete. 
    A doua: mămăligă cremă cu brânză și smântână. Și ultima: papanași cu dulceață de vișine. 
    Dacă ți-a plăcut, nu uita să dai like!
    """
    
    # Test analiza scriptului
    print("📊 Testing script analysis...")
    analysis = await contextual_image_ai.analyze_script_for_visuals(test_script)
    
    if analysis:
        print(f"✅ Script analyzed: {len(analysis.get('segments', []))} segments")
        print(f"🎨 Visual style: {analysis.get('visual_style', 'N/A')}")
        print(f"🌈 Mood: {analysis.get('overall_mood', 'N/A')}")
        
        # Test generarea planului complet
        print("🗺️ Testing contextual image plan...")
        plan = await contextual_image_ai.create_contextual_image_plan(
            script=test_script,
            duration=60,
            max_images=5
        )
        
        if plan:
            print(f"✅ Created plan with {len(plan.image_prompts)} image prompts")
            print(f"🖼️ Thumbnail prompt: {plan.thumbnail_prompt[:50]}...")
            return True
        else:
            print("❌ Plan creation failed")
            return False
    else:
        print("❌ Script analysis failed")
        return False


if __name__ == "__main__":
    asyncio.run(test_contextual_image_ai())
