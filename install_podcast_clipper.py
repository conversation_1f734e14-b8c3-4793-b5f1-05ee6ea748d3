#!/usr/bin/env python3
"""
Script de instalare pentru Podcast Clipper
In<PERSON><PERSON> toate dependințele necesare pentru funcționalitatea de extragere clipuri din podcast-uri
"""

import subprocess
import sys
import os
import urllib.request
from pathlib import Path
import zipfile
import shutil

def run_command(command, description=""):
    """Execută o comandă și afișează progresul"""
    print(f"\n🔄 {description}")
    print(f"Executând: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Succes!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Eroare!")
        print(f"Eroare: {e.stderr}")
        return False

def download_file(url, destination, description=""):
    """Descarcă un fișier de la o URL"""
    print(f"\n📥 Descărcare {description}...")
    print(f"URL: {url}")
    print(f"Destinație: {destination}")
    
    try:
        urllib.request.urlretrieve(url, destination)
        print(f"✅ {description} descărcat cu succes!")
        return True
    except Exception as e:
        print(f"❌ Eroare la descărcarea {description}: {e}")
        return False

def create_models_directory():
    """Creează directorul models dacă nu există"""
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    print(f"✅ Director models creat: {models_dir.absolute()}")
    return models_dir

def download_yolo_files(models_dir):
    """Descarcă fișierele YOLO necesare"""
    print("\n🎯 Descărcare fișiere YOLO pentru detectarea persoanelor...")
    
    yolo_files = [
        {
            "url": "https://raw.githubusercontent.com/pjreddie/darknet/master/cfg/yolov3.cfg",
            "filename": "yolov3.cfg",
            "description": "Configurație YOLO"
        },
        {
            "url": "https://pjreddie.com/media/files/yolov3.weights",
            "filename": "yolov3.weights",
            "description": "Greutăți YOLO (mare - ~250MB)"
        },
        {
            "url": "https://raw.githubusercontent.com/pjreddie/darknet/master/data/coco.names",
            "filename": "coco.names",
            "description": "Etichete COCO"
        }
    ]
    
    success_count = 0
    for file_info in yolo_files:
        destination = models_dir / file_info["filename"]
        
        # Skip dacă fișierul există deja
        if destination.exists():
            print(f"⏭️ {file_info['description']} există deja, sărim...")
            success_count += 1
            continue
        
        if download_file(file_info["url"], destination, file_info["description"]):
            success_count += 1
    
    return success_count == len(yolo_files)

def install_pytorch():
    """Instalează PyTorch cu suport CUDA dacă este disponibil"""
    print("\n🔥 Instalare PyTorch...")
    
    # Verifică dacă CUDA este disponibil
    try:
        import torch
        if torch.cuda.is_available():
            print("✅ PyTorch cu CUDA este deja instalat!")
            return True
    except ImportError:
        pass
    
    # Instalează PyTorch
    pytorch_command = "pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118"
    
    if not run_command(pytorch_command, "Instalare PyTorch cu suport CUDA"):
        # Fallback la versiunea CPU
        print("⚠️ Instalarea cu CUDA a eșuat, încerc versiunea CPU...")
        cpu_command = "pip install torch torchaudio"
        return run_command(cpu_command, "Instalare PyTorch CPU")
    
    return True

def install_podcast_clipper_dependencies():
    """Instalează dependințele pentru Podcast Clipper"""
    print("\n📦 Instalare dependințe Podcast Clipper...")
    
    # Verifică dacă fișierul requirements există
    requirements_file = "requirements_podcast_clipper.txt"
    if not os.path.exists(requirements_file):
        print(f"❌ Fișierul {requirements_file} nu a fost găsit!")
        return False
    
    # Instalează dependințele
    command = f"pip install -r {requirements_file}"
    return run_command(command, "Instalare dependințe Podcast Clipper")

def verify_installation():
    """Verifică dacă instalarea a fost realizată cu succes"""
    print("\n🔍 Verificare instalare...")
    
    required_packages = [
        "cv2",
        "whisper", 
        "moviepy",
        "torch",
        "numpy",
        "librosa"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "cv2":
                import cv2
            elif package == "whisper":
                import whisper
            elif package == "moviepy":
                import moviepy
            elif package == "torch":
                import torch
            elif package == "numpy":
                import numpy
            elif package == "librosa":
                import librosa
            
            print(f"✅ {package} - OK")
        except ImportError:
            print(f"❌ {package} - LIPSEȘTE")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Pachete lipsă: {', '.join(missing_packages)}")
        return False
    else:
        print("\n🎉 Toate pachetele sunt instalate corect!")
        return True

def main():
    """Funcția principală de instalare"""
    print("🎙️ PODCAST CLIPPER - SCRIPT DE INSTALARE")
    print("=" * 50)
    print("Acest script va instala toate dependințele necesare pentru")
    print("funcționalitatea de extragere clipuri din podcast-uri.")
    print()
    
    # Verifică Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ este necesar!")
        sys.exit(1)
    
    print(f"✅ Python {sys.version} detectat")
    
    # Pasul 1: Upgrade pip
    if not run_command("python -m pip install --upgrade pip", "Upgrade pip"):
        print("⚠️ Nu s-a putut actualiza pip, continuăm...")
    
    # Pasul 2: Instalează PyTorch
    if not install_pytorch():
        print("❌ Eroare la instalarea PyTorch!")
        sys.exit(1)
    
    # Pasul 3: Instalează dependințele
    if not install_podcast_clipper_dependencies():
        print("❌ Eroare la instalarea dependințelor!")
        sys.exit(1)
    
    # Pasul 4: Creează directorul models
    models_dir = create_models_directory()
    
    # Pasul 5: Descarcă fișierele YOLO
    if not download_yolo_files(models_dir):
        print("⚠️ Nu s-au putut descărca toate fișierele YOLO.")
        print("Poți să le descarci manual din:")
        print("- https://raw.githubusercontent.com/pjreddie/darknet/master/cfg/yolov3.cfg")
        print("- https://pjreddie.com/media/files/yolov3.weights")
        print("- https://raw.githubusercontent.com/pjreddie/darknet/master/data/coco.names")
    
    # Pasul 6: Verifică instalarea
    if verify_installation():
        print("\n🎉 INSTALARE COMPLETĂ CU SUCCES!")
        print("\n📋 Următorii pași:")
        print("1. Restartează aplicația MoneyPrinterTurbo")
        print("2. Accesează tab-ul '🎙️ Podcast Clipper'")
        print("3. Încarcă un videoclip podcast pentru a testa funcționalitatea")
        print("\n💡 Sfaturi:")
        print("- Folosește videoclipuri cu persoane vizibile pentru rezultate optime")
        print("- Formatul recomandat: MP4 cu rezoluție HD")
        print("- Durata recomandată: 10-60 minute pentru procesare eficientă")
    else:
        print("\n❌ INSTALAREA A EȘUAT!")
        print("Verifică erorile de mai sus și încearcă din nou.")
        sys.exit(1)

if __name__ == "__main__":
    main()
