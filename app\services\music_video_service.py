"""
Music Video Service
Serviciu principal pentru generarea de videoclipuri muzicale auto-editate
"""

import os
import logging
import tempfile
import shutil
from typing import Dict, List, Optional
from moviepy.editor import VideoFileClip
from .music_video_analyzer import MusicVideoAnalyzer
from .music_video_effects import MusicVideoEffects

logger = logging.getLogger(__name__)

class MusicVideoService:
    """Serviciu principal pentru procesarea videoclipurilor muzicale"""
    
    def __init__(self):
        self.analyzer = MusicVideoAnalyzer()
        self.effects = MusicVideoEffects()
        
    def process_music_video(self, video_path: str, output_path: str, 
                          user_preferences: Optional[Dict] = None) -> Dict:
        """
        Procesează un videoclip muzical complet
        
        Args:
            video_path: Calea către videoclipul original
            output_path: Calea pentru videoclipul procesat
            user_preferences: Preferințele utilizatorului
            
        Returns:
            Dict cu informații despre procesare
        """
        logger.info(f"🎵 Începe procesarea videoclipului muzical: {video_path}")
        
        try:
            # Verifică dacă fișierul există
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Videoclipul nu există: {video_path}")
            
            # Analizează videoclipul
            logger.info("🔍 Analizez videoclipul...")
            analysis = self.analyzer.analyze_video(video_path)
            
            # Încarcă videoclipul
            logger.info("📹 Încarc videoclipul...")
            video = VideoFileClip(video_path)
            
            # Aplică efectele bazate pe gen
            genre = analysis['genre']['primary_genre']
            confidence = analysis['genre']['confidence']
            
            logger.info(f"🎯 Gen detectat: {genre} (confidence: {confidence:.2f})")
            
            # Aplică efectele
            logger.info("🎨 Aplic efectele vizuale...")
            processed_video = self.effects.apply_genre_effects(
                video, genre, analysis, user_preferences
            )
            
            # Asigură-te că directorul de output există
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Salvează videoclipul procesat
            logger.info(f"💾 Salvez videoclipul procesat: {output_path}")
            processed_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # Cleanup
            video.close()
            processed_video.close()
            
            result = {
                'success': True,
                'output_path': output_path,
                'analysis': analysis,
                'processing_info': {
                    'genre_detected': genre,
                    'confidence': confidence,
                    'effects_applied': len(self.effects.genre_mappings[genre]['primary_effects']),
                    'duration': analysis['duration'],
                    'original_size': os.path.getsize(video_path),
                    'processed_size': os.path.getsize(output_path) if os.path.exists(output_path) else 0
                }
            }
            
            logger.info(f"✅ Videoclip muzical procesat cu succes!")
            return result
            
        except Exception as e:
            logger.error(f"❌ Eroare în procesarea videoclipului muzical: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'output_path': None
            }
    
    def get_supported_genres(self) -> List[str]:
        """Returnează lista genurilor suportate"""
        return list(self.effects.genre_mappings.keys())
    
    def get_genre_effects(self, genre: str) -> Dict:
        """Returnează efectele disponibile pentru un gen"""
        return self.effects.genre_mappings.get(genre, {})
    
    def preview_analysis(self, video_path: str) -> Dict:
        """
        Analizează videoclipul fără procesare pentru preview
        
        Args:
            video_path: Calea către videoclip
            
        Returns:
            Dict cu analiza pentru preview
        """
        try:
            logger.info(f"🔍 Preview analiza pentru: {video_path}")
            
            # Analizează videoclipul
            analysis = self.analyzer.analyze_video(video_path)
            
            # Adaugă informații pentru UI
            genre = analysis['genre']['primary_genre']
            effects_info = self.get_genre_effects(genre)
            
            preview_result = {
                'success': True,
                'genre': analysis['genre'],
                'audio_info': {
                    'tempo': analysis['audio']['tempo'],
                    'energy_level': analysis['genre']['energy_level'],
                    'duration': analysis['duration']
                },
                'lyrics_info': analysis['lyrics'],
                'emotional_peaks_count': len(analysis['emotional_peaks']),
                'recommended_effects': effects_info.get('primary_effects', []),
                'style_config': effects_info.get('style_config', {}),
                'song_structure': analysis['song_structure']
            }
            
            logger.info(f"✅ Preview completat pentru genul: {genre}")
            return preview_result
            
        except Exception as e:
            logger.error(f"❌ Eroare în preview analiza: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_custom_edit(self, video_path: str, output_path: str, 
                          custom_config: Dict) -> Dict:
        """
        Creează un edit personalizat bazat pe configurația utilizatorului
        
        Args:
            video_path: Calea către videoclip
            output_path: Calea pentru output
            custom_config: Configurația personalizată
            
        Returns:
            Dict cu rezultatul procesării
        """
        try:
            logger.info("🎨 Creez edit personalizat...")
            
            # Analizează videoclipul
            analysis = self.analyzer.analyze_video(video_path)
            
            # Încarcă videoclipul
            video = VideoFileClip(video_path)
            
            # Aplică configurația personalizată
            processed_video = self._apply_custom_config(video, analysis, custom_config)
            
            # Asigură-te că directorul de output există
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Salvează
            processed_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )
            
            # Cleanup
            video.close()
            processed_video.close()
            
            return {
                'success': True,
                'output_path': output_path,
                'custom_config_applied': custom_config
            }
            
        except Exception as e:
            logger.error(f"❌ Eroare în edit personalizat: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _apply_custom_config(self, video: VideoFileClip, analysis: Dict, 
                           custom_config: Dict) -> VideoFileClip:
        """Aplică configurația personalizată"""
        processed = video
        
        # Aplică efectele selectate de utilizator
        selected_effects = custom_config.get('effects', [])
        
        for effect_name in selected_effects:
            if effect_name in self.effects.effects_library:
                effect_function = self.effects.effects_library[effect_name]
                try:
                    processed = self.effects._apply_single_effect(
                        processed, effect_function, effect_name, 
                        analysis, custom_config
                    )
                except Exception as e:
                    logger.warning(f"Nu s-a putut aplica efectul {effect_name}: {str(e)}")
        
        return processed
    
    def get_video_info(self, video_path: str) -> Dict:
        """Obține informații de bază despre videoclip"""
        try:
            with VideoFileClip(video_path) as video:
                return {
                    'duration': video.duration,
                    'fps': video.fps,
                    'size': video.size,
                    'has_audio': video.audio is not None,
                    'file_size': os.path.getsize(video_path)
                }
        except Exception as e:
            logger.error(f"Eroare la obținerea info video: {str(e)}")
            return {
                'duration': 0,
                'fps': 30,
                'size': (1080, 1920),
                'has_audio': False,
                'file_size': 0
            }
    
    def extract_highlights(self, video_path: str, output_dir: str, 
                         highlight_duration: int = 15) -> List[str]:
        """
        Extrage highlight-uri automate din videoclip
        
        Args:
            video_path: Calea către videoclip
            output_dir: Directorul pentru highlight-uri
            highlight_duration: Durata fiecărui highlight în secunde
            
        Returns:
            Lista cu căile către highlight-uri
        """
        try:
            logger.info(f"🎬 Extrag highlight-uri din: {video_path}")
            
            # Analizează videoclipul
            analysis = self.analyzer.analyze_video(video_path)
            
            # Găsește momentele cele mai interesante
            emotional_peaks = analysis['emotional_peaks']
            
            if not emotional_peaks:
                logger.warning("Nu s-au găsit momente emoționale pentru highlight-uri")
                return []
            
            highlights = []
            video = VideoFileClip(video_path)
            
            # Asigură-te că directorul există
            os.makedirs(output_dir, exist_ok=True)
            
            for i, peak in enumerate(emotional_peaks[:3]):  # Maxim 3 highlight-uri
                start_time = max(0, peak['time'] - highlight_duration / 2)
                end_time = min(video.duration, start_time + highlight_duration)
                
                # Extrage segmentul
                highlight_clip = video.subclip(start_time, end_time)
                
                # Aplică efecte pentru highlight
                genre = analysis['genre']['primary_genre']
                highlight_processed = self.effects.apply_genre_effects(
                    highlight_clip, genre, analysis
                )
                
                # Salvează highlight-ul
                highlight_path = os.path.join(output_dir, f"highlight_{i+1}.mp4")
                highlight_processed.write_videofile(
                    highlight_path,
                    codec='libx264',
                    audio_codec='aac',
                    verbose=False,
                    logger=None
                )
                
                highlights.append(highlight_path)
                
                # Cleanup
                highlight_clip.close()
                highlight_processed.close()
            
            video.close()
            
            logger.info(f"✅ {len(highlights)} highlight-uri create cu succes")
            return highlights
            
        except Exception as e:
            logger.error(f"❌ Eroare la extragerea highlight-urilor: {str(e)}")
            return []
