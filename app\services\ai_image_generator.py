"""
AI Image Generator for Enhanced Shitpost Generation
Supports both local Stable Diffusion and cloud APIs with fallback
"""

import os
import base64
import hashlib
import asyncio
import aiohttp
import requests
from typing import Optional, Dict, Any, List
from PIL import Image, ImageEnhance, ImageFilter
from io import BytesIO
import logging
import time
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class GenerationConfig:
    """Configuration for AI image generation"""
    width: int = 512
    height: int = 512
    steps: int = 20
    cfg_scale: float = 7.0
    negative_prompt: str = "blurry, low quality, text, watermark"
    style: str = "meme"

class RateLimiter:
    """Simple rate limiter for API calls"""
    def __init__(self, max_calls: int, period: int):
        self.max_calls = max_calls
        self.period = period
        self.calls = []
    
    async def acquire(self):
        now = time.time()
        # Remove old calls
        self.calls = [call_time for call_time in self.calls if now - call_time < self.period]
        
        if len(self.calls) >= self.max_calls:
            sleep_time = self.period - (now - self.calls[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
        
        self.calls.append(now)

class StableDiffusionAPI:
    """Local Stable Diffusion WebUI API client"""

    def __init__(self, base_url: str = "http://localhost:7860"):
        self.base_url = base_url
        self.available = False
        # Check if Stable Diffusion is enabled in config before checking availability
        from app.config import config
        stable_diffusion_enabled = config.app.get("stable_diffusion_enabled", True)
        if stable_diffusion_enabled:
            self._check_availability()
        else:
            logger.info("🚫 Stable Diffusion disabled in configuration, skipping availability check")

    def _check_availability(self):
        """Check if local SD is available"""
        try:
            response = requests.get(f"{self.base_url}/sdapi/v1/options", timeout=5)
            self.available = response.status_code == 200
            logger.info(f"Stable Diffusion WebUI {'available' if self.available else 'not available'}")
        except Exception as e:
            self.available = False
            logger.warning(f"Stable Diffusion WebUI not available: {e}")
    
    async def generate_image(self, prompt: str, config: GenerationConfig) -> Optional[bytes]:
        """Generate image using local Stable Diffusion"""
        if not self.available:
            return None
        
        try:
            payload = {
                "prompt": prompt,
                "negative_prompt": config.negative_prompt,
                "width": config.width,
                "height": config.height,
                "steps": config.steps,
                "cfg_scale": config.cfg_scale,
                "sampler_name": "DPM++ 2M Karras",
                "batch_size": 1,
                "n_iter": 1,
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/sdapi/v1/txt2img",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get('images'):
                            image_data = base64.b64decode(result['images'][0])
                            logger.info(f"Generated image with SD: {len(image_data)} bytes")
                            return image_data
        except Exception as e:
            logger.error(f"Stable Diffusion generation failed: {e}")
        
        return None

class OpenAIImageGenerator:
    """OpenAI DALL-E image generator with cost tracking"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.rate_limiter = RateLimiter(max_calls=50, period=3600)  # 50 per hour
        self.daily_budget = float(os.getenv('OPENAI_DAILY_BUDGET', '10.0'))
        self.current_spend = 0.0
        self.cost_per_image = 0.04  # DALL-E 3 cost
    
    async def generate_image(self, prompt: str, config: GenerationConfig) -> Optional[bytes]:
        """Generate image using OpenAI DALL-E"""
        if not self.api_key:
            logger.warning("OpenAI API key not available")
            return None
        
        # Check budget
        if self.current_spend + self.cost_per_image > self.daily_budget:
            logger.warning("Daily budget exceeded for OpenAI image generation")
            return None
        
        try:
            await self.rate_limiter.acquire()
            
            # Import OpenAI here to avoid dependency issues
            from openai import AsyncOpenAI
            client = AsyncOpenAI(api_key=self.api_key)
            
            response = await client.images.generate(
                model="dall-e-3",
                prompt=prompt,
                size=f"{config.width}x{config.height}",
                quality="standard",
                n=1,
            )
            
            if response.data:
                image_url = response.data[0].url
                async with aiohttp.ClientSession() as session:
                    async with session.get(image_url) as img_response:
                        if img_response.status == 200:
                            image_data = await img_response.read()
                            self.current_spend += self.cost_per_image
                            logger.info(f"Generated image with DALL-E: {len(image_data)} bytes, cost: ${self.cost_per_image}")
                            return image_data
        
        except Exception as e:
            logger.error(f"OpenAI image generation failed: {e}")
        
        return None

class ImageCache:
    """Simple file-based cache for generated images"""
    
    def __init__(self, cache_dir: str = "storage/image_cache"):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        self.cache_ttl = 86400  # 24 hours
    
    def _get_cache_key(self, prompt: str, config: GenerationConfig) -> str:
        """Generate cache key from prompt and config"""
        cache_string = f"{prompt}_{config.width}_{config.height}_{config.style}"
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def get_cached_image(self, prompt: str, config: GenerationConfig) -> Optional[bytes]:
        """Get cached image if available and not expired"""
        cache_key = self._get_cache_key(prompt, config)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.jpg")
        
        if os.path.exists(cache_file):
            # Check if cache is still valid
            if time.time() - os.path.getmtime(cache_file) < self.cache_ttl:
                try:
                    with open(cache_file, 'rb') as f:
                        logger.info(f"Using cached image for prompt: {prompt[:50]}...")
                        return f.read()
                except Exception as e:
                    logger.error(f"Error reading cached image: {e}")
        
        return None
    
    def cache_image(self, prompt: str, config: GenerationConfig, image_data: bytes):
        """Cache generated image"""
        try:
            cache_key = self._get_cache_key(prompt, config)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.jpg")
            
            with open(cache_file, 'wb') as f:
                f.write(image_data)
            
            logger.info(f"Cached image for prompt: {prompt[:50]}...")
        except Exception as e:
            logger.error(f"Error caching image: {e}")

class AIImageGenerator:
    """Main AI image generator with multiple backends and caching"""

    def __init__(self):
        self.sd_api = StableDiffusionAPI()
        self.openai_api = OpenAIImageGenerator()
        self.cache = ImageCache()

        # Import free AI services
        try:
            from .free_ai_services import free_ai_generator
            self.free_ai = free_ai_generator
            self.free_ai_available = True
        except ImportError:
            self.free_ai = None
            self.free_ai_available = False
            logger.warning("Free AI services not available")

        self.meme_styles = {
            'absurd': "surreal, absurd, chaotic, deep fried meme, oversaturated",
            'classic': "classic meme format, clean, simple",
            'romanian': "romanian culture, balkan meme, eastern european",
            'gaming': "gaming meme, gamer culture, video game reference",
            'brain': "big brain meme, galaxy brain, intellectual",
            'chaos': "chaotic energy, random, nonsensical, fever dream"
        }
    
    def _enhance_prompt(self, base_prompt: str, style: str, chaos_level: int) -> str:
        """Enhance prompt based on style and chaos level"""
        style_modifier = self.meme_styles.get(style, "")
        
        # Add chaos-based modifiers
        if chaos_level >= 8:
            chaos_modifier = "extremely chaotic, fever dream, surreal, deep fried"
        elif chaos_level >= 6:
            chaos_modifier = "chaotic, absurd, oversaturated"
        elif chaos_level >= 4:
            chaos_modifier = "moderately chaotic, funny"
        else:
            chaos_modifier = "clean, simple"
        
        enhanced_prompt = f"{base_prompt}, {style_modifier}, {chaos_modifier}, meme style, high quality"
        return enhanced_prompt
    
    async def generate_meme_image(self, prompt: str, style: str = "absurd",
                                 chaos_level: int = 5, config: Optional[GenerationConfig] = None) -> Optional[bytes]:
        """Generate meme image with fallback strategy including free AI services"""
        if config is None:
            config = GenerationConfig()

        # Enhance prompt based on style and chaos
        enhanced_prompt = self._enhance_prompt(prompt, style, chaos_level)

        # Check cache first
        cached_image = self.cache.get_cached_image(enhanced_prompt, config)
        if cached_image:
            return cached_image

        # Try free AI services first (completely free)
        if self.free_ai_available:
            logger.info("Trying free AI services...")
            image_data = await self.free_ai.generate_meme_image(
                enhanced_prompt,
                width=config.width,
                height=config.height,
                style=style,
                chaos_level=chaos_level
            )
            if image_data:
                # Apply additional meme effects
                image_data = self.apply_meme_effects(image_data, chaos_level)
                self.cache.cache_image(enhanced_prompt, config, image_data)
                return image_data

        # Try local Stable Diffusion (free but requires setup)
        image_data = await self.sd_api.generate_image(enhanced_prompt, config)

        # Fallback to OpenAI if local fails (paid service)
        if not image_data:
            logger.info("Local SD failed, trying OpenAI...")
            image_data = await self.openai_api.generate_image(enhanced_prompt, config)

        # Cache successful generation
        if image_data:
            self.cache.cache_image(enhanced_prompt, config, image_data)
            return image_data

        logger.error("All image generation methods failed")
        return None
    
    def apply_meme_effects(self, image_data: bytes, chaos_level: int) -> bytes:
        """Apply meme-style effects to generated image"""
        try:
            image = Image.open(BytesIO(image_data))
            
            # Apply effects based on chaos level
            if chaos_level >= 7:
                # Deep fried effect
                image = ImageEnhance.Contrast(image).enhance(2.0)
                image = ImageEnhance.Color(image).enhance(2.0)
                image = image.filter(ImageFilter.SHARPEN)
            elif chaos_level >= 4:
                # Moderate enhancement
                image = ImageEnhance.Contrast(image).enhance(1.5)
                image = ImageEnhance.Color(image).enhance(1.3)
            
            # Convert back to bytes
            output = BytesIO()
            image.save(output, format='JPEG', quality=85)
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error applying meme effects: {e}")
            return image_data
