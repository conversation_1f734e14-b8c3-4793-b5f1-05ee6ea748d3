@echo off
echo ========================================
echo MoneyPrinterTurbo - Romanian Setup
echo ========================================
echo.

echo Configuring MoneyPrinterTurbo for Romanian language...
echo.

echo Setting default language to Romanian...
powershell -Command "(Get-Content config.toml) -replace 'language = \"\"en\"\"', 'language = \"\"ro\"\"' | Set-Content config.toml"

echo Configuring default Romanian voice...
powershell -Command "(Get-Content config.toml) -replace 'voice_name = \"\".*\"\"', 'voice_name = \"\"ro-RO-AlinaNeural-Female\"\"' | Set-Content config.toml"

echo.
echo Romanian configuration completed successfully!
echo.
echo To get started:
echo    1. Run: start_webui.bat
echo    2. Open: http://localhost:8501
echo    3. Interface will be in Romanian
echo.
echo Available guides:
echo    - ROMANIAN_GUIDE.md (Complete Romanian guide)
echo    - SETUP_GUIDE.md (General setup guide)
echo.
echo Romanian voices available:
echo    - ro-RO-AlinaNeural (Female)
echo    - ro-RO-<PERSON>Neural (Male)
echo.
echo Don't forget to configure:
echo    - Pexels API key (for video materials)
echo    - LLM API key (for script generation)
echo.
pause
