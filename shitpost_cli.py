#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Shitpost Video Generator CLI
Command-line interface for generating viral shitpost videos with AI integration
"""

import os
import sys
import asyncio
import argparse
import json
import random
from typing import Dict, List, Optional
from pathlib import Path

# Add the project root to the path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

try:
    from app.services.shitpost_generator import ShitpostGenerator
    from app.services.gpt4free_service import gpt4free_service
    from app.services.free_ai_services import free_ai_generator
    from app.config import config
    SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"❌ Error importing services: {e}")
    print("Make sure you're running from the project root directory")
    SERVICES_AVAILABLE = False

class ShitpostCLI:
    """Command-line interface for shitpost generation"""
    
    def __init__(self):
        self.generator = None
        if SERVICES_AVAILABLE:
            self.generator = ShitpostGenerator()
    
    def create_parser(self) -> argparse.ArgumentParser:
        """Create command-line argument parser"""
        parser = argparse.ArgumentParser(
            description="🤖 Shitpost Video Generator - Create viral meme videos with AI",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  %(prog)s generate --theme romanian --chaos 8 --duration 15
  %(prog)s batch --count 5 --theme gaming --output ./batch_output/
  %(prog)s ai-generate --prompt "confused romanian guy" --style absurd
  %(prog)s config --show
            """
        )
        
        subparsers = parser.add_subparsers(dest='command', help='Available commands')
        
        # Generate command
        gen_parser = subparsers.add_parser('generate', help='Generate a single shitpost video')
        gen_parser.add_argument('--theme', choices=['romanian', 'gaming', 'philosophical', 'chaos', 'random'], 
                               default='random', help='Shitpost theme')
        gen_parser.add_argument('--chaos', type=int, choices=range(1, 11), default=7,
                               help='Chaos level (1-10)')
        gen_parser.add_argument('--duration', type=int, default=15,
                               help='Video duration in seconds')
        gen_parser.add_argument('--text', type=str, help='Custom text (optional)')
        gen_parser.add_argument('--output', type=str, help='Output file path')
        gen_parser.add_argument('--ai', action='store_true', help='Use AI-powered generation')
        
        # Batch generation command
        batch_parser = subparsers.add_parser('batch', help='Generate multiple shitpost videos')
        batch_parser.add_argument('--count', type=int, default=3, help='Number of videos to generate')
        batch_parser.add_argument('--theme', choices=['romanian', 'gaming', 'philosophical', 'chaos', 'random'], 
                                 default='random', help='Shitpost theme')
        batch_parser.add_argument('--chaos-range', nargs=2, type=int, default=[5, 8],
                                 help='Chaos level range (min max)')
        batch_parser.add_argument('--duration-range', nargs=2, type=int, default=[10, 20],
                                 help='Duration range in seconds (min max)')
        batch_parser.add_argument('--output-dir', type=str, default='./batch_output',
                                 help='Output directory for batch generation')
        batch_parser.add_argument('--ai', action='store_true', help='Use AI-powered generation')
        
        # AI-specific generation
        ai_parser = subparsers.add_parser('ai-generate', help='Generate using AI services')
        ai_parser.add_argument('--prompt', type=str, required=True, help='AI prompt for content generation')
        ai_parser.add_argument('--style', choices=['absurd', 'classic', 'romanian', 'gaming', 'chaos'], 
                              default='absurd', help='AI generation style')
        ai_parser.add_argument('--image-prompt', type=str, help='Specific prompt for image generation')
        ai_parser.add_argument('--chaos', type=int, choices=range(1, 11), default=6,
                              help='Chaos level (1-10)')
        ai_parser.add_argument('--duration', type=int, default=12, help='Video duration in seconds')
        ai_parser.add_argument('--ai-only', action='store_true',
                              help='Use AI-only mode (complete video from AI-generated images)')
        ai_parser.add_argument('--output', type=str, help='Output file path')
        
        # Configuration commands
        config_parser = subparsers.add_parser('config', help='Configuration management')
        config_parser.add_argument('--show', action='store_true', help='Show current configuration')
        config_parser.add_argument('--test-services', action='store_true', help='Test AI services availability')
        config_parser.add_argument('--set-gpt4free', type=str, help='Set GPT4Free model')
        
        # Status command
        status_parser = subparsers.add_parser('status', help='Show system status')
        status_parser.add_argument('--verbose', action='store_true', help='Verbose output')
        
        return parser
    
    async def generate_single(self, args) -> bool:
        """Generate a single shitpost video"""
        if not self.generator:
            print("❌ Shitpost generator not available")
            return False
        
        print(f"🎬 Generating shitpost video...")
        print(f"   Theme: {args.theme}")
        print(f"   Chaos Level: {args.chaos}")
        print(f"   Duration: {args.duration}s")
        
        try:
            if args.ai and hasattr(self.generator, 'generate_ai_powered_shitpost'):
                result = await self.generator.generate_ai_powered_shitpost(
                    theme=args.theme,
                    duration=args.duration,
                    chaos_level=args.chaos,
                    custom_text=args.text,
                    use_ai_images=True
                )
            else:
                result = self.generator.generate_random_shitpost(
                    theme=args.theme,
                    duration=args.duration,
                    chaos_level=args.chaos,
                    custom_text=args.text
                )
            
            if result['success']:
                output_path = result['output_path']
                if args.output:
                    # Move to specified output path
                    import shutil
                    shutil.move(output_path, args.output)
                    output_path = args.output
                
                print(f"✅ Video generated successfully!")
                print(f"   Output: {output_path}")
                print(f"   Text: {result['text_generated']}")
                print(f"   File size: {result.get('file_size', 0) / (1024*1024):.1f} MB")
                return True
            else:
                print(f"❌ Generation failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            print(f"❌ Error during generation: {e}")
            return False
    
    async def generate_batch(self, args) -> bool:
        """Generate multiple shitpost videos"""
        if not self.generator:
            print("❌ Shitpost generator not available")
            return False
        
        # Create output directory
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎬 Generating {args.count} shitpost videos...")
        print(f"   Theme: {args.theme}")
        print(f"   Chaos Range: {args.chaos_range[0]}-{args.chaos_range[1]}")
        print(f"   Duration Range: {args.duration_range[0]}-{args.duration_range[1]}s")
        print(f"   Output Directory: {output_dir}")
        
        successful = 0
        failed = 0
        
        for i in range(args.count):
            # Randomize parameters within ranges
            chaos = random.randint(args.chaos_range[0], args.chaos_range[1])
            duration = random.randint(args.duration_range[0], args.duration_range[1])
            
            print(f"\n📹 Generating video {i+1}/{args.count} (Chaos: {chaos}, Duration: {duration}s)")
            
            try:
                if args.ai and hasattr(self.generator, 'generate_ai_powered_shitpost'):
                    result = await self.generator.generate_ai_powered_shitpost(
                        theme=args.theme,
                        duration=duration,
                        chaos_level=chaos,
                        use_ai_images=True
                    )
                else:
                    result = self.generator.generate_random_shitpost(
                        theme=args.theme,
                        duration=duration,
                        chaos_level=chaos
                    )
                
                if result['success']:
                    # Move to batch output directory
                    original_path = result['output_path']
                    filename = f"batch_{i+1:03d}_{Path(original_path).name}"
                    new_path = output_dir / filename
                    
                    import shutil
                    shutil.move(original_path, new_path)
                    
                    print(f"   ✅ Success: {filename}")
                    print(f"   Text: {result['text_generated'][:50]}...")
                    successful += 1
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                    failed += 1
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
                failed += 1
        
        print(f"\n🎉 Batch generation complete!")
        print(f"   Successful: {successful}")
        print(f"   Failed: {failed}")
        print(f"   Output directory: {output_dir}")
        
        return successful > 0
    
    async def ai_generate(self, args) -> bool:
        """Generate using AI services"""
        ai_mode = "AI-Only" if args.ai_only else "AI-Powered"
        print(f"🤖 {ai_mode} shitpost generation...")
        print(f"   Prompt: {args.prompt}")
        print(f"   Style: {args.style}")
        print(f"   AI-Only Mode: {args.ai_only}")

        # First, generate text content using GPT4Free
        if gpt4free_service.is_available():
            print("📝 Generating text content with GPT4Free...")
            text_content = await gpt4free_service.generate_shitpost_content(
                theme=args.style,
                chaos_level=args.chaos,
                style=args.style
            )

            if text_content:
                print(f"   Generated text: {text_content[:100]}...")
            else:
                print("   ⚠️ Text generation failed, using prompt as text")
                text_content = args.prompt
        else:
            print("   ⚠️ GPT4Free not available, using prompt as text")
            text_content = args.prompt
        
        # Generate image if image prompt provided
        image_data = None
        if args.image_prompt:
            print("🎨 Generating AI image...")
            image_data = await free_ai_generator.generate_meme_image(
                prompt=args.image_prompt,
                style=args.style,
                chaos_level=args.chaos
            )
            
            if image_data:
                print(f"   ✅ Image generated: {len(image_data)} bytes")
            else:
                print("   ⚠️ Image generation failed")
        
        # Generate video
        if not self.generator:
            print("❌ Shitpost generator not available")
            return False
        
        try:
            if hasattr(self.generator, 'generate_ai_powered_shitpost'):
                if args.ai_only:
                    print("🎬 Using AI-Only mode for complete video generation...")

                result = await self.generator.generate_ai_powered_shitpost(
                    theme=args.style,
                    duration=args.duration,
                    chaos_level=args.chaos,
                    custom_text=text_content,
                    use_ai_images=True,
                    ai_only_mode=args.ai_only
                )
            else:
                result = self.generator.generate_random_shitpost(
                    theme=args.style,
                    duration=args.duration,
                    chaos_level=args.chaos,
                    custom_text=text_content
                )
            
            if result['success']:
                output_path = result['output_path']
                if args.output:
                    import shutil
                    shutil.move(output_path, args.output)
                    output_path = args.output
                
                print(f"✅ AI shitpost generated successfully!")
                print(f"   Output: {output_path}")
                print(f"   Text: {result['text_generated']}")
                return True
            else:
                print(f"❌ Generation failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            print(f"❌ Error during AI generation: {e}")
            return False
    
    def show_config(self):
        """Show current configuration"""
        print("⚙️ Current Configuration:")
        print(f"   Project Root: {root_dir}")
        print(f"   Services Available: {SERVICES_AVAILABLE}")
        
        if SERVICES_AVAILABLE:
            print(f"   GPT4Free Available: {gpt4free_service.is_available()}")
            print(f"   Free AI Generator: Available")
            
            # Show GPT4Free status
            gpt4free_status = gpt4free_service.get_status()
            print(f"   GPT4Free Model: {gpt4free_status.get('default_model', 'N/A')}")
            
            # Show AI services status
            ai_status = free_ai_generator.get_service_status()
            print(f"   AI Services: {list(ai_status.keys())}")
    
    async def test_services(self):
        """Test AI services availability"""
        print("🧪 Testing AI services...")
        
        if not SERVICES_AVAILABLE:
            print("❌ Services not available")
            return
        
        # Test GPT4Free
        print("\n📝 Testing GPT4Free...")
        if gpt4free_service.is_available():
            test_text = await gpt4free_service.generate_text("Test prompt for shitpost generation")
            if test_text:
                print(f"   ✅ GPT4Free working: {test_text[:50]}...")
            else:
                print("   ❌ GPT4Free test failed")
        else:
            print("   ❌ GPT4Free not available")
        
        # Test Free AI Image Generator
        print("\n🎨 Testing Free AI Image Generator...")
        test_image = await free_ai_generator.generate_meme_image(
            "test meme image", style="meme", chaos_level=3
        )
        if test_image:
            print(f"   ✅ AI Image Generator working: {len(test_image)} bytes")
        else:
            print("   ❌ AI Image Generator test failed")
    
    def show_status(self, verbose: bool = False):
        """Show system status"""
        print("📊 System Status:")
        print(f"   Python: {sys.version.split()[0]}")
        print(f"   Platform: {sys.platform}")
        print(f"   Working Directory: {os.getcwd()}")
        print(f"   Services Available: {SERVICES_AVAILABLE}")
        
        if verbose and SERVICES_AVAILABLE:
            print("\n🔧 Detailed Service Status:")
            
            # GPT4Free status
            gpt4free_status = gpt4free_service.get_status()
            print(f"   GPT4Free:")
            for key, value in gpt4free_status.items():
                print(f"     {key}: {value}")
            
            # AI services status
            ai_status = free_ai_generator.get_service_status()
            print(f"   AI Services:")
            for service, status in ai_status.items():
                if isinstance(status, dict):
                    print(f"     {service}:")
                    for k, v in status.items():
                        print(f"       {k}: {v}")
                else:
                    print(f"     {service}: {status}")

async def main():
    """Main CLI entry point"""
    cli = ShitpostCLI()
    parser = cli.create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print("🤖 Shitpost Video Generator CLI")
    print("=" * 40)
    
    try:
        if args.command == 'generate':
            success = await cli.generate_single(args)
        elif args.command == 'batch':
            success = await cli.generate_batch(args)
        elif args.command == 'ai-generate':
            success = await cli.ai_generate(args)
        elif args.command == 'config':
            if args.show:
                cli.show_config()
            elif args.test_services:
                await cli.test_services()
            success = True
        elif args.command == 'status':
            cli.show_status(args.verbose)
            success = True
        else:
            parser.print_help()
            success = False
        
        if success:
            print("\n✅ Command completed successfully!")
        else:
            print("\n❌ Command failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
