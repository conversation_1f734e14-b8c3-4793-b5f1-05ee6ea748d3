"""
Video Compression Service
Serviciu pentru compresia videoclipurilor mari pentru procesare optimizată
"""

import os
import tempfile
import subprocess
from typing import Optional, Tuple, Dict, Any
from pathlib import Path
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class CompressionConfig:
    """Configurație pentru compresia video"""
    resolution: str = "720p"  # 720p, 480p, 360p
    quality: str = "medium"   # high, medium, low
    fps: int = 30
    audio_bitrate: str = "128k"
    video_codec: str = "libx264"
    audio_codec: str = "aac"
    preset: str = "fast"      # ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow

class VideoCompressionService:
    """Serviciu pentru compresia videoclipurilor"""
    
    def __init__(self):
        self.temp_dir = None
        self._check_ffmpeg()
    
    def _check_ffmpeg(self):
        """Verifică dacă FFmpeg este disponibil"""
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("✅ FFmpeg este disponibil")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("⚠️ FFmpeg nu este disponibil - compresia va fi limitată")
            return False
    
    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """Obține informații despre videoclip"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', 
                '-show_format', '-show_streams', video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                
                # Extrage informații video
                video_stream = None
                audio_stream = None
                
                for stream in data.get('streams', []):
                    if stream.get('codec_type') == 'video' and not video_stream:
                        video_stream = stream
                    elif stream.get('codec_type') == 'audio' and not audio_stream:
                        audio_stream = stream
                
                format_info = data.get('format', {})
                
                info = {
                    'duration': float(format_info.get('duration', 0)),
                    'size': int(format_info.get('size', 0)),
                    'bitrate': int(format_info.get('bit_rate', 0)),
                    'format': format_info.get('format_name', 'unknown')
                }
                
                if video_stream:
                    info.update({
                        'width': int(video_stream.get('width', 0)),
                        'height': int(video_stream.get('height', 0)),
                        'fps': eval(video_stream.get('r_frame_rate', '30/1')),
                        'video_codec': video_stream.get('codec_name', 'unknown'),
                        'video_bitrate': int(video_stream.get('bit_rate', 0))
                    })
                
                if audio_stream:
                    info.update({
                        'audio_codec': audio_stream.get('codec_name', 'unknown'),
                        'audio_bitrate': int(audio_stream.get('bit_rate', 0)),
                        'sample_rate': int(audio_stream.get('sample_rate', 0))
                    })
                
                return info
                
        except Exception as e:
            logger.error(f"Eroare la obținerea informațiilor video: {e}")
        
        return {}
    
    def estimate_compressed_size(self, original_size: int, config: CompressionConfig) -> int:
        """Estimează dimensiunea după compresie"""
        
        # Factori de compresie aproximativi
        resolution_factors = {
            "720p": 0.6,   # 60% din dimensiunea originală
            "480p": 0.35,  # 35% din dimensiunea originală  
            "360p": 0.2    # 20% din dimensiunea originală
        }
        
        quality_factors = {
            "high": 1.0,
            "medium": 0.7,
            "low": 0.5
        }
        
        resolution_factor = resolution_factors.get(config.resolution, 0.6)
        quality_factor = quality_factors.get(config.quality, 0.7)
        
        estimated_size = int(original_size * resolution_factor * quality_factor)
        return estimated_size
    
    def compress_video(
        self, 
        input_path: str, 
        output_path: Optional[str] = None,
        compression_level: str = "Ușoară (720p)",
        progress_callback=None
    ) -> Optional[str]:
        """
        Comprimă un videoclip
        
        Args:
            input_path: Calea către videoclipul original
            output_path: Calea pentru videoclipul comprimat (opțional)
            compression_level: Nivelul de compresie
            progress_callback: Funcție pentru raportarea progresului
            
        Returns:
            Calea către videoclipul comprimat sau None dacă a eșuat
        """
        
        try:
            if progress_callback:
                progress_callback("🔍 Analizez videoclipul original...", 0.1)
            
            # Obține informații despre videoclipul original
            video_info = self.get_video_info(input_path)
            
            if not video_info:
                logger.error("Nu s-au putut obține informații despre videoclip")
                return None
            
            # Configurează compresia bazată pe nivelul selectat
            config = self._get_compression_config(compression_level)
            
            # Generează calea de ieșire dacă nu este specificată
            if not output_path:
                input_path_obj = Path(input_path)
                output_path = str(input_path_obj.parent / f"{input_path_obj.stem}_compressed{input_path_obj.suffix}")
            
            if progress_callback:
                progress_callback("🗜️ Configurez parametrii de compresie...", 0.2)
            
            # Construiește comanda FFmpeg
            cmd = self._build_ffmpeg_command(input_path, output_path, config, video_info)
            
            if progress_callback:
                progress_callback("⚙️ Începe compresia video...", 0.3)
            
            # Execută compresia
            success = self._execute_compression(cmd, video_info.get('duration', 0), progress_callback)
            
            if success and os.path.exists(output_path):
                if progress_callback:
                    progress_callback("✅ Compresie completă!", 1.0)
                
                # Afișează statistici
                original_size = os.path.getsize(input_path)
                compressed_size = os.path.getsize(output_path)
                compression_ratio = (1 - compressed_size / original_size) * 100
                
                logger.info(f"Compresie completă:")
                logger.info(f"  Original: {original_size / (1024*1024):.1f} MB")
                logger.info(f"  Comprimat: {compressed_size / (1024*1024):.1f} MB")
                logger.info(f"  Reducere: {compression_ratio:.1f}%")
                
                return output_path
            else:
                logger.error("Compresia a eșuat")
                return None
                
        except Exception as e:
            logger.error(f"Eroare în compresia video: {e}")
            return None
    
    def _get_compression_config(self, compression_level: str) -> CompressionConfig:
        """Obține configurația de compresie bazată pe nivel"""
        
        if "720p" in compression_level:
            return CompressionConfig(
                resolution="720p",
                quality="medium",
                fps=30,
                audio_bitrate="128k",
                preset="fast"
            )
        elif "480p" in compression_level:
            return CompressionConfig(
                resolution="480p", 
                quality="medium",
                fps=30,
                audio_bitrate="96k",
                preset="fast"
            )
        elif "360p" in compression_level:
            return CompressionConfig(
                resolution="360p",
                quality="low", 
                fps=24,
                audio_bitrate="64k",
                preset="faster"
            )
        else:
            # Default la 720p
            return CompressionConfig()
    
    def _build_ffmpeg_command(
        self, 
        input_path: str, 
        output_path: str, 
        config: CompressionConfig,
        video_info: Dict[str, Any]
    ) -> list:
        """Construiește comanda FFmpeg pentru compresie"""
        
        cmd = ['ffmpeg', '-i', input_path]
        
        # Setări video
        if config.resolution == "720p":
            cmd.extend(['-vf', 'scale=-2:720'])
        elif config.resolution == "480p":
            cmd.extend(['-vf', 'scale=-2:480'])
        elif config.resolution == "360p":
            cmd.extend(['-vf', 'scale=-2:360'])
        
        # Codec și calitate video
        cmd.extend(['-c:v', config.video_codec])
        cmd.extend(['-preset', config.preset])
        
        # Setări calitate
        if config.quality == "high":
            cmd.extend(['-crf', '20'])
        elif config.quality == "medium":
            cmd.extend(['-crf', '25'])
        else:  # low
            cmd.extend(['-crf', '30'])
        
        # Frame rate
        cmd.extend(['-r', str(config.fps)])
        
        # Setări audio
        cmd.extend(['-c:a', config.audio_codec])
        cmd.extend(['-b:a', config.audio_bitrate])
        
        # Optimizări
        cmd.extend(['-movflags', '+faststart'])  # Pentru streaming
        cmd.extend(['-y'])  # Suprascrie fișierul de ieșire
        
        cmd.append(output_path)
        
        return cmd
    
    def _execute_compression(
        self, 
        cmd: list, 
        duration: float, 
        progress_callback=None
    ) -> bool:
        """Execută comanda de compresie cu monitorizare progres"""
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # Monitorizează progresul prin stderr de la FFmpeg
            while True:
                output = process.stderr.readline()
                if output == '' and process.poll() is not None:
                    break
                
                if output and progress_callback and duration > 0:
                    # Extrage timpul curent din output-ul FFmpeg
                    if 'time=' in output:
                        try:
                            time_str = output.split('time=')[1].split()[0]
                            current_time = self._parse_time(time_str)
                            progress = min(0.3 + (current_time / duration) * 0.7, 1.0)
                            progress_callback(f"🗜️ Comprim video... {current_time:.1f}s/{duration:.1f}s", progress)
                        except:
                            pass
            
            return_code = process.poll()
            return return_code == 0
            
        except Exception as e:
            logger.error(f"Eroare în execuția FFmpeg: {e}")
            return False
    
    def _parse_time(self, time_str: str) -> float:
        """Parsează string-ul de timp de la FFmpeg în secunde"""
        try:
            parts = time_str.split(':')
            if len(parts) == 3:
                hours = float(parts[0])
                minutes = float(parts[1]) 
                seconds = float(parts[2])
                return hours * 3600 + minutes * 60 + seconds
        except:
            pass
        return 0.0
