#!/usr/bin/env python3
"""
Test Subtitle Rendering Fix

This script tests the subtitle rendering system with PIL-based fallback
to ensure subtitles work without ImageMagick dependency.
"""

import sys
import os
from pathlib import Path

# Add the project root to the path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

def test_moviepy_textclip():
    """Test MoviePy TextClip functionality"""
    print("🎬 Testing MoviePy TextClip...")
    
    try:
        from moviepy.editor import TextClip
        
        # Test basic TextClip
        try:
            clip = TextClip(
                txt="Test subtitle",
                fontsize=50,
                color="white"
            )
            print("  ✅ Basic TextClip creation - OK")
            return True
        except Exception as e:
            if "ImageMagick" in str(e):
                print("  ❌ TextClip requires ImageMagick (expected)")
                return False
            else:
                print(f"  ❌ TextClip failed: {e}")
                return False
                
    except ImportError as e:
        print(f"  ❌ MoviePy import failed: {e}")
        return False


def test_pil_text_rendering():
    """Test PIL-based text rendering"""
    print("\n🖼️ Testing PIL Text Rendering...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # Test basic PIL text rendering
        img = Image.new('RGBA', (400, 100), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.load_default()
            draw.text((10, 10), "Test subtitle", font=font, fill=(255, 255, 255))
            print("  ✅ PIL text rendering - OK")
            
            # Test numpy conversion
            img_array = np.array(img)
            print("  ✅ PIL to numpy conversion - OK")
            
            return True
            
        except Exception as e:
            print(f"  ❌ PIL text rendering failed: {e}")
            return False
            
    except ImportError as e:
        print(f"  ❌ PIL import failed: {e}")
        return False


def test_subtitle_file_reading():
    """Test subtitle file reading functionality"""
    print("\n📄 Testing Subtitle File Reading...")
    
    try:
        # Create a test subtitle file
        test_srt_content = """1
00:00:00,000 --> 00:00:03,000
Test subtitle line 1

2
00:00:03,000 --> 00:00:06,000
Test subtitle line 2 with special chars: àáâãäå

3
00:00:06,000 --> 00:00:09,000
Test subtitle line 3
Multiple lines
"""
        
        test_file = "test_subtitle.srt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_srt_content)
        
        # Test reading the file
        from app.services import subtitle
        subtitle_items = subtitle.file_to_subtitles(test_file)
        
        if subtitle_items and len(subtitle_items) >= 3:
            print(f"  ✅ Subtitle file reading - OK ({len(subtitle_items)} items)")
            print(f"  📝 Sample: {subtitle_items[0]}")
        else:
            print(f"  ⚠️ Subtitle file reading - Limited ({len(subtitle_items) if subtitle_items else 0} items)")
        
        # Clean up
        if os.path.exists(test_file):
            os.remove(test_file)
            
        return True
        
    except Exception as e:
        print(f"  ❌ Subtitle file reading failed: {e}")
        return False


def test_integrated_subtitle_system():
    """Test the integrated subtitle system with fallback"""
    print("\n🔧 Testing Integrated Subtitle System...")

    try:
        # Test PIL-based text rendering directly
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np

        # Simulate the PIL text rendering logic
        text = "Test subtitle with\nmultiple lines"
        font_size = 50
        stroke_width = 2
        text_color = (255, 255, 255)  # White
        stroke_color = (0, 0, 0)      # Black

        # Load font
        try:
            font = ImageFont.load_default()
        except:
            print("  ⚠️ Could not load font, using fallback")
            font = None

        if font:
            # Calculate text dimensions
            lines = text.split('\n')
            line_heights = []
            line_widths = []

            for line in lines:
                bbox = font.getbbox(line)
                line_width = bbox[2] - bbox[0]
                line_height = bbox[3] - bbox[1]
                line_widths.append(line_width)
                line_heights.append(line_height)

            max_line_width = max(line_widths) if line_widths else 100
            total_height = sum(line_heights) + (len(lines) - 1) * int(font_size * 0.2)

            # Add padding
            padding = 20
            img_width = max_line_width + 2 * padding + 2 * stroke_width
            img_height = total_height + 2 * padding + 2 * stroke_width

            # Create image with transparent background
            img = Image.new('RGBA', (img_width, img_height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # Draw text with stroke
            y_offset = padding + stroke_width
            for i, line in enumerate(lines):
                line_width = line_widths[i]
                x_offset = (img_width - line_width) // 2  # Center align

                # Draw stroke
                if stroke_width > 0:
                    for dx in range(-stroke_width, stroke_width + 1):
                        for dy in range(-stroke_width, stroke_width + 1):
                            if dx*dx + dy*dy <= stroke_width*stroke_width:
                                draw.text((x_offset + dx, y_offset + dy), line,
                                        font=font, fill=stroke_color)

                # Draw main text
                draw.text((x_offset, y_offset), line, font=font, fill=text_color)
                y_offset += line_heights[i] + int(font_size * 0.2)

            # Convert PIL image to numpy array
            img_array = np.array(img)

            print("  ✅ PIL text clip creation - OK")
            print(f"  📏 Image size: {img_array.shape}")
            print(f"  📝 Text lines: {len(lines)}")
            return True
        else:
            print("  ❌ Font loading failed")
            return False

    except Exception as e:
        print(f"  ❌ Integrated subtitle system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_color_parsing():
    """Test color parsing functionality"""
    print("\n🎨 Testing Color Parsing...")
    
    try:
        def parse_color(color_str):
            if color_str.startswith('#'):
                color_str = color_str[1:]
                return tuple(int(color_str[i:i+2], 16) for i in (0, 2, 4))
            return (255, 255, 255)  # Default white
        
        # Test various color formats
        test_colors = [
            "#FFFFFF",  # White
            "#000000",  # Black
            "#FF0000",  # Red
            "#00FF00",  # Green
            "#0000FF",  # Blue
        ]
        
        for color in test_colors:
            rgb = parse_color(color)
            print(f"  🎨 {color} → {rgb}")
        
        print("  ✅ Color parsing - OK")
        return True
        
    except Exception as e:
        print(f"  ❌ Color parsing failed: {e}")
        return False


def main():
    """Run all subtitle rendering tests"""
    print("🚀 Subtitle Rendering Test Suite")
    print("=" * 50)
    
    try:
        # Test 1: MoviePy TextClip
        moviepy_works = test_moviepy_textclip()
        
        # Test 2: PIL text rendering
        pil_works = test_pil_text_rendering()
        
        # Test 3: Subtitle file reading
        file_reading_works = test_subtitle_file_reading()
        
        # Test 4: Color parsing
        color_parsing_works = test_color_parsing()
        
        # Test 5: Integrated system
        integrated_works = test_integrated_subtitle_system()
        
        print("\n" + "=" * 50)
        print("📋 TEST SUMMARY")
        print("=" * 50)
        
        results = {
            "MoviePy TextClip": moviepy_works,
            "PIL Text Rendering": pil_works,
            "Subtitle File Reading": file_reading_works,
            "Color Parsing": color_parsing_works,
            "Integrated System": integrated_works
        }
        
        for test_name, passed in results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"  {status} {test_name}")
        
        # Determine overall status
        critical_tests = ["PIL Text Rendering", "Subtitle File Reading", "Integrated System"]
        critical_passed = all(results[test] for test in critical_tests)
        
        if critical_passed:
            print(f"\n🎉 SUBTITLE SYSTEM READY!")
            print(f"\n✅ SUBTITLE RENDERING STATUS:")
            if moviepy_works:
                print(f"  🎬 Primary: MoviePy TextClip (ImageMagick available)")
            else:
                print(f"  🖼️ Fallback: PIL-based rendering (ImageMagick not available)")
            print(f"  📄 File Reading: Working")
            print(f"  🎨 Color Support: Working")
            print(f"  🔧 Integration: Working")
            
            print(f"\n🎯 READY FOR TESTING:")
            print(f"  - Generate a video with subtitles enabled")
            print(f"  - Subtitles should appear in the final video")
            print(f"  - No ImageMagick dependency required")
            
            return True
        else:
            print(f"\n❌ SUBTITLE SYSTEM HAS ISSUES")
            failed_tests = [test for test, passed in results.items() if not passed and test in critical_tests]
            print(f"  Critical failures: {', '.join(failed_tests)}")
            return False
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
