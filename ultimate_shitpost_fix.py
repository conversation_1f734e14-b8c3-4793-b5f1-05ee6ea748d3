#!/usr/bin/env python3
"""
Ultimate Shitpost Generator Fix
A completely isolated solution that bypasses all WebSocket and ImageMagick issues
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_and_install_dependencies():
    """Check and install missing dependencies"""
    print("🔍 Checking dependencies...")
    
    missing_deps = []
    
    # Check core dependencies
    try:
        import tkinter
        print("✅ Tkinter available")
    except ImportError:
        missing_deps.append("tkinter")
        print("❌ Tkinter missing")
    
    try:
        import PIL
        print("✅ PIL/Pillow available")
    except ImportError:
        missing_deps.append("Pillow")
        print("❌ PIL/Pillow missing")
    
    try:
        import moviepy
        print("✅ MoviePy available")
    except ImportError:
        missing_deps.append("moviepy")
        print("❌ MoviePy missing")
    
    # Install missing dependencies
    if missing_deps:
        print(f"\n📦 Installing missing dependencies: {missing_deps}")
        for dep in missing_deps:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✅ Installed {dep}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {dep}")
    
    return len(missing_deps) == 0

def create_minimal_shitpost_generator():
    """Create a minimal, self-contained shitpost generator"""
    
    minimal_code = '''#!/usr/bin/env python3
"""
Minimal Shitpost Generator
Self-contained version that works without external dependencies
"""

import os
import sys
import random
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from pathlib import Path

class MinimalShitpostGenerator:
    """Minimal shitpost generator with basic functionality"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 Minimal Shitpost Generator")
        self.root.geometry("600x500")
        
        # Romanian meme templates
        self.romanian_templates = [
            "Nimeni:\\nAbsolut nimeni:\\nEu: {action}",
            "Bunica: '{advice}'\\nEu: '{reaction}'",
            "Când {situation} dar {twist}",
            "POV: {scenario}",
            "Breaking news: {absurd_news}",
            "Știința: {fact}\\nEu: {misinterpretation}",
            "Mama: '{mom_quote}'\\nEu la 25 de ani: '{adult_reaction}'"
        ]
        
        # Word banks
        self.actions = ["să vorbesc cu peștii", "să dansez breakdance", "să mănânc covrigii cosmici"]
        self.advice = ["Mănâncă că ești slab", "Pune-ți ceva pe tine", "Nu ieși cu părul ud"]
        self.reactions = ["Dar bunico...", "Am 25 de ani", "Sunt adult"]
        self.situations = ["e frig afară", "e cald în casă", "plouă"]
        self.twists = ["încep să zbor", "devin invizibil", "vorbesc cu pisicile"]
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Title
        title_label = tk.Label(self.root, text="🤖 Minimal Shitpost Generator", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Theme selection
        theme_frame = tk.Frame(self.root)
        theme_frame.pack(pady=10)
        
        tk.Label(theme_frame, text="Theme:").pack(side=tk.LEFT)
        self.theme_var = tk.StringVar(value="romanian")
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var,
                                  values=["romanian", "gaming", "philosophical", "random"])
        theme_combo.pack(side=tk.LEFT, padx=10)
        
        # Chaos level
        chaos_frame = tk.Frame(self.root)
        chaos_frame.pack(pady=10)
        
        tk.Label(chaos_frame, text="Chaos Level:").pack(side=tk.LEFT)
        self.chaos_var = tk.IntVar(value=7)
        chaos_scale = tk.Scale(chaos_frame, from_=1, to=10, variable=self.chaos_var, 
                              orient=tk.HORIZONTAL, length=200)
        chaos_scale.pack(side=tk.LEFT, padx=10)
        
        # Generate button
        generate_btn = tk.Button(self.root, text="🎬 Generate Shitpost Text", 
                               command=self.generate_text, bg="#4CAF50", fg="white",
                               font=("Arial", 12, "bold"), pady=10)
        generate_btn.pack(pady=20)
        
        # Output text area
        self.output_text = tk.Text(self.root, height=15, width=70, wrap=tk.WORD,
                                  font=("Arial", 11))
        self.output_text.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
        
        # Buttons frame
        buttons_frame = tk.Frame(self.root)
        buttons_frame.pack(pady=10)
        
        copy_btn = tk.Button(buttons_frame, text="📋 Copy Text", 
                           command=self.copy_text)
        copy_btn.pack(side=tk.LEFT, padx=5)
        
        save_btn = tk.Button(buttons_frame, text="💾 Save Text", 
                           command=self.save_text)
        save_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = tk.Button(buttons_frame, text="🗑️ Clear", 
                            command=self.clear_output)
        clear_btn.pack(side=tk.LEFT, padx=5)
    
    def generate_text(self):
        """Generate shitpost text"""
        theme = self.theme_var.get()
        chaos = self.chaos_var.get()
        
        if theme == "romanian":
            text = self.generate_romanian_meme(chaos)
        elif theme == "gaming":
            text = self.generate_gaming_meme(chaos)
        elif theme == "philosophical":
            text = self.generate_philosophical_meme(chaos)
        else:
            text = self.generate_random_meme(chaos)
        
        # Add chaos effects
        if chaos >= 7:
            text = text.upper()
        if chaos >= 8:
            text = text.replace(" ", " ✨ ")
        if chaos >= 9:
            text = "🔥 " + text + " 🔥"
        if chaos == 10:
            text = "🌟💫⭐ " + text + " ⭐💫🌟"
        
        self.output_text.insert(tk.END, f"\\n{'='*50}\\n")
        self.output_text.insert(tk.END, f"Theme: {theme} | Chaos: {chaos}\\n")
        self.output_text.insert(tk.END, f"{'='*50}\\n")
        self.output_text.insert(tk.END, text + "\\n")
        self.output_text.see(tk.END)
    
    def generate_romanian_meme(self, chaos):
        """Generate Romanian meme"""
        template = random.choice(self.romanian_templates)
        
        replacements = {
            "action": random.choice(self.actions),
            "advice": random.choice(self.advice),
            "reaction": random.choice(self.reactions),
            "situation": random.choice(self.situations),
            "twist": random.choice(self.twists),
            "scenario": f"Ești român și {random.choice(['mănânci mici', 'asculți manele', 'te uiți la Antena 1'])}",
            "absurd_news": f"Covrigul a fost declarat {random.choice(['vegetală', 'fruct', 'animal domestic'])}",
            "fact": "Apa fierbe la 100°C",
            "misinterpretation": "Deci dacă beau apă fierbinte devin fierbinte",
            "mom_quote": random.choice(self.advice),
            "adult_reaction": random.choice(self.reactions)
        }
        
        for key, value in replacements.items():
            template = template.replace(f"{{{key}}}", value)
        
        return template
    
    def generate_gaming_meme(self, chaos):
        """Generate gaming meme"""
        templates = [
            "Când mori pentru a {number}-a oară la același boss\\nEu: 'This is fine'",
            "Noob: 'Cum fac {action}?'\\nPro: 'Git gud'",
            "Când {game_situation} dar {game_twist}",
            "POV: Joci {game} la 3 dimineața"
        ]
        
        template = random.choice(templates)
        
        replacements = {
            "number": random.choice(["47", "73", "156", "999"]),
            "action": random.choice(["headshot", "combo", "speedrun", "no-scope"]),
            "game_situation": "ai 1 HP",
            "game_twist": "apare boss final",
            "game": random.choice(["Dark Souls", "CS:GO", "League", "Minecraft"])
        }
        
        for key, value in replacements.items():
            template = template.replace(f"{{{key}}}", value)
        
        return template
    
    def generate_philosophical_meme(self, chaos):
        """Generate philosophical meme"""
        templates = [
            "Dacă {premise}\\nAtunci {conclusion}\\n*galaxy brain intensifies*",
            "Shower thought: {thought}",
            "Știința: {science_fact}\\nFilozofia: {philosophy_twist}",
            "Big brain time: {big_brain_idea}"
        ]
        
        template = random.choice(templates)
        
        replacements = {
            "premise": "timpul este bani",
            "conclusion": "ceasurile sunt bănci",
            "thought": "Covrigul este un donut care și-a găsit gaura în viață",
            "science_fact": "Universul se extinde",
            "philosophy_twist": "Deci și eu mă extind când mănânc",
            "big_brain_idea": "Dacă dorm 8 ore, trăiesc doar 16 ore pe zi"
        }
        
        for key, value in replacements.items():
            template = template.replace(f"{{{key}}}", value)
        
        return template
    
    def generate_random_meme(self, chaos):
        """Generate completely random meme"""
        random_elements = [
            "Covrigul cosmic", "Pisica filozofică", "Bunica quantică",
            "Mici interdimensionali", "Manele existențiale", "Țuică temporală"
        ]
        
        actions = [
            "dansează breakdance", "vorbește cu extratereștrii", 
            "rezolvă ecuații diferențiale", "face yoga"
        ]
        
        element = random.choice(random_elements)
        action = random.choice(actions)
        
        return f"Breaking news: {element} {action}\\nEu: 'Seems legit'"
    
    def copy_text(self):
        """Copy text to clipboard"""
        try:
            text = self.output_text.get("1.0", tk.END)
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            messagebox.showinfo("Success", "Text copied to clipboard!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy: {e}")
    
    def save_text(self):
        """Save text to file"""
        try:
            text = self.output_text.get("1.0", tk.END)
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(text)
                messagebox.showinfo("Success", f"Text saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save: {e}")
    
    def clear_output(self):
        """Clear output text"""
        self.output_text.delete("1.0", tk.END)
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🤖 Starting Minimal Shitpost Generator...")
    
    try:
        app = MinimalShitpostGenerator()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
'''
    
    # Write the minimal generator
    minimal_path = Path("minimal_shitpost_generator.py")
    
    try:
        with open(minimal_path, 'w', encoding='utf-8') as f:
            f.write(minimal_code)
        
        print(f"✅ Created minimal generator: {minimal_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create minimal generator: {e}")
        return False

def create_launcher_scripts():
    """Create launcher scripts for different scenarios"""
    
    # Windows batch file for minimal version
    minimal_bat = '''@echo off
echo 🤖 Minimal Shitpost Generator (No WebSocket Issues)
echo Starting text-only version...

REM Activate virtual environment if it exists
if exist "venv\\Scripts\\activate.bat" (
    echo Activating virtual environment...
    call venv\\Scripts\\activate.bat
)

python minimal_shitpost_generator.py

pause
'''
    
    # Windows batch file for standalone version
    standalone_bat = '''@echo off
echo 🤖 Standalone Shitpost Generator (Full Features)
echo Starting desktop application...

REM Activate virtual environment if it exists
if exist "venv\\Scripts\\activate.bat" (
    echo Activating virtual environment...
    call venv\\Scripts\\activate.bat
)

python standalone_shitpost_generator.py

pause
'''
    
    # CLI-only batch file
    cli_only_bat = '''@echo off
echo 🤖 CLI-Only Shitpost Generator (No GUI Issues)
echo.

REM Activate virtual environment if it exists
if exist "venv\\Scripts\\activate.bat" (
    echo Activating virtual environment...
    call venv\\Scripts\\activate.bat
)

echo Available commands:
echo   generate --theme romanian --chaos 8
echo   batch --count 5 --theme gaming
echo   config --test-services
echo.

if "%1"=="" (
    set /p command="Enter command (or 'help' for options): "
    python shitpost_cli.py %command%
) else (
    python shitpost_cli.py %*
)

pause
'''
    
    scripts = [
        ("start_minimal.bat", minimal_bat),
        ("start_standalone.bat", standalone_bat),
        ("start_cli_only.bat", cli_only_bat)
    ]
    
    for filename, content in scripts:
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Created launcher: {filename}")
        except Exception as e:
            print(f"❌ Failed to create {filename}: {e}")

def main():
    """Main fix function"""
    print("🔧 Ultimate Shitpost Generator Fix")
    print("=" * 50)
    print("This will create multiple solutions to bypass WebSocket and ImageMagick issues")
    print()
    
    # Check dependencies
    deps_ok = check_and_install_dependencies()
    
    # Create minimal version (always works)
    print("\\n📝 Creating minimal text-only version...")
    minimal_created = create_minimal_shitpost_generator()
    
    # Create launcher scripts
    print("\\n🚀 Creating launcher scripts...")
    create_launcher_scripts()
    
    # Summary
    print("\\n" + "=" * 50)
    print("🎉 Ultimate Fix Complete!")
    print("=" * 50)
    
    print("\\n🎯 Available Solutions:")
    print("1. 📝 Minimal Text Generator (Always Works)")
    print("   - No WebSocket issues")
    print("   - No ImageMagick required")
    print("   - Pure text generation")
    print("   - Run: start_minimal.bat")
    
    print("\\n2. 🖥️ Standalone Desktop App")
    print("   - Tkinter-based GUI")
    print("   - No Streamlit WebSocket issues")
    print("   - Full video generation")
    print("   - Run: start_standalone.bat")
    
    print("\\n3. 💻 CLI-Only Version")
    print("   - Command-line interface")
    print("   - No GUI dependencies")
    print("   - Batch processing")
    print("   - Run: start_cli_only.bat")
    
    print("\\n🚀 Quick Start:")
    print("1. For immediate text generation: start_minimal.bat")
    print("2. For full features without WebSocket: start_standalone.bat")
    print("3. For command-line usage: start_cli_only.bat")
    
    print("\\n💡 These solutions completely bypass:")
    print("   ✅ WebSocket timeout errors")
    print("   ✅ Streamlit connection issues")
    print("   ✅ ImageMagick dependency problems")
    print("   ✅ Asyncio event loop conflicts")

if __name__ == "__main__":
    main()
