@echo off
REM Fix ImageMagick warnings by preferring PIL
set PREFER_PIL_TEXT=true
echo 🤖 Enhanced Shitpost Generator CLI
echo.

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
)

REM Show help if no arguments
if "%1"=="" (
    echo Usage examples:
    echo   %0 generate --theme romanian --chaos 8
    echo   %0 batch --count 5 --theme gaming
    echo   %0 ai-generate --prompt "confused guy" --style absurd
    echo   %0 config --test-services
    echo.
    python shitpost_cli.py --help
) else (
    python shitpost_cli.py %*
)

pause
