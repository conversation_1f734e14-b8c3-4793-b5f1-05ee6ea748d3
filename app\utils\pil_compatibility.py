"""
PIL/Pillow Compatibility Module for MoneyPrinterTurbo

This module provides compatibility fixes for PIL.Image.ANTIALIAS deprecation
in Pillow 10.0.0+ when used with MoviePy 1.0.3.

The issue: MoviePy 1.0.3 uses PIL.Image.ANTIALIAS which was deprecated and
removed in Pillow 10.0.0+. This module patches PIL.Image to restore
compatibility.
"""

import logging
from typing import Optional

logger = logging.getLogger(__name__)

def apply_pil_compatibility_patch() -> bool:
    """
    Apply PIL compatibility patch for ANTIALIAS deprecation.
    
    Returns:
        bool: True if patch was applied successfully, False otherwise
    """
    try:
        import PIL.Image as Image
        
        # Check if ANTIALIAS already exists (older Pillow versions)
        if hasattr(Image, 'ANTIALIAS'):
            logger.info("✅ PIL.Image.ANTIALIAS already exists, no patch needed")
            return True
        
        # Check if we have the new Resampling enum
        if hasattr(Image, 'Resampling') and hasattr(Image.Resampling, 'LANCZOS'):
            # Patch: Add ANTIALIAS as alias to LANCZOS
            Image.ANTIALIAS = Image.Resampling.LANCZOS
            logger.info("✅ Applied PIL compatibility patch: ANTIALIAS -> Resampling.LANCZOS")
            return True
        
        # Fallback: Use LANCZOS directly if available
        elif hasattr(Image, 'LANCZOS'):
            Image.ANTIALIAS = Image.LANCZOS
            logger.info("✅ Applied PIL compatibility patch: ANTIALIAS -> LANCZOS")
            return True
        
        # Last resort: Use BICUBIC
        elif hasattr(Image, 'BICUBIC'):
            Image.ANTIALIAS = Image.BICUBIC
            logger.warning("⚠️ Applied PIL compatibility patch: ANTIALIAS -> BICUBIC (fallback)")
            return True
        
        else:
            logger.error("❌ Could not apply PIL compatibility patch: No suitable resampling method found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to apply PIL compatibility patch: {e}")
        return False

def verify_pil_compatibility() -> bool:
    """
    Verify that PIL compatibility is working correctly.
    
    Returns:
        bool: True if PIL.Image.ANTIALIAS is available, False otherwise
    """
    try:
        import PIL.Image as Image
        
        # Test if ANTIALIAS is available
        antialias_value = getattr(Image, 'ANTIALIAS', None)
        if antialias_value is not None:
            logger.info(f"✅ PIL.Image.ANTIALIAS is available: {antialias_value}")
            return True
        else:
            logger.error("❌ PIL.Image.ANTIALIAS is not available")
            return False
            
    except Exception as e:
        logger.error(f"❌ PIL compatibility verification failed: {e}")
        return False

def get_pil_info() -> dict:
    """
    Get information about PIL/Pillow installation.
    
    Returns:
        dict: Information about PIL/Pillow version and available resampling methods
    """
    try:
        import PIL
        import PIL.Image as Image
        
        info = {
            'pillow_version': getattr(PIL, '__version__', 'unknown'),
            'has_antialias': hasattr(Image, 'ANTIALIAS'),
            'has_resampling_enum': hasattr(Image, 'Resampling'),
            'has_lanczos': hasattr(Image, 'LANCZOS'),
            'available_resampling': []
        }
        
        # Get available resampling methods
        if hasattr(Image, 'Resampling'):
            info['available_resampling'] = [attr for attr in dir(Image.Resampling) 
                                          if not attr.startswith('_')]
        else:
            # Legacy methods
            legacy_methods = ['NEAREST', 'LANCZOS', 'BILINEAR', 'BICUBIC', 'ANTIALIAS']
            info['available_resampling'] = [method for method in legacy_methods 
                                          if hasattr(Image, method)]
        
        return info
        
    except Exception as e:
        logger.error(f"Failed to get PIL info: {e}")
        return {'error': str(e)}

def initialize_pil_compatibility() -> bool:
    """
    Initialize PIL compatibility for MoneyPrinterTurbo.
    
    This function should be called early in the application startup
    to ensure MoviePy works correctly with newer Pillow versions.
    
    Returns:
        bool: True if initialization was successful, False otherwise
    """
    logger.info("🔧 Initializing PIL compatibility for MoneyPrinterTurbo...")
    
    # Get PIL information
    pil_info = get_pil_info()
    logger.info(f"📦 Pillow version: {pil_info.get('pillow_version', 'unknown')}")
    logger.info(f"📋 Available resampling methods: {pil_info.get('available_resampling', [])}")
    
    # Apply compatibility patch if needed
    if not pil_info.get('has_antialias', False):
        logger.info("🔧 PIL.Image.ANTIALIAS not found, applying compatibility patch...")
        patch_success = apply_pil_compatibility_patch()
        
        if patch_success:
            # Verify the patch worked
            verify_success = verify_pil_compatibility()
            if verify_success:
                logger.info("✅ PIL compatibility initialization successful")
                return True
            else:
                logger.error("❌ PIL compatibility patch verification failed")
                return False
        else:
            logger.error("❌ PIL compatibility patch application failed")
            return False
    else:
        logger.info("✅ PIL.Image.ANTIALIAS already available, no patch needed")
        return True

# Auto-initialize when module is imported
if __name__ != "__main__":
    try:
        initialize_pil_compatibility()
    except Exception as e:
        logger.error(f"❌ Auto-initialization of PIL compatibility failed: {e}")
