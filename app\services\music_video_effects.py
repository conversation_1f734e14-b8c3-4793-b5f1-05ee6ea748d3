"""
Music Video Effects Service
Aplică efecte vizuale specifice genurilor muzicale
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from moviepy.editor import (
    VideoFileClip, TextClip, CompositeVideoClip, 
    concatenate_videoclips, ColorClip
)
import random

logger = logging.getLogger(__name__)

class MusicVideoEffects:
    """Generator de efecte vizuale pentru videoclipuri muzicale"""
    
    def __init__(self):
        self.effects_library = self._load_effects_library()
        self.genre_mappings = self._load_genre_mappings()
        
    def _load_effects_library(self):
        """Biblioteca completă de efecte disponibile"""
        return {
            # Text Effects
            'text_overlay_emotional': self.text_overlay_emotional,
            'text_overlay_rapid': self.text_overlay_rapid,
            'text_overlay_elegant': self.text_overlay_elegant,
            
            # Color Effects
            'color_warm_boost': self.color_warm_boost,
            'color_dark_dramatic': self.color_dark_dramatic,
            'color_neon_flash': self.color_neon_flash,
            
            # Motion Effects
            'zoom_dramatic': self.zoom_dramatic,
            'zoom_smooth': self.zoom_smooth,
            'shake_energy': self.shake_energy,
            
            # Transition Effects
            'fade_smooth': self.fade_smooth,
        }
    
    def _load_genre_mappings(self):
        """Maparea efectelor pentru fiecare gen"""
        return {
            'manele': {
                'primary_effects': [
                    'text_overlay_emotional',
                    'color_warm_boost', 
                    'zoom_dramatic',
                    'fade_smooth'
                ],
                'style_config': {
                    'font_family': 'Arial-Bold',
                    'color_palette': ['#FFD700', '#FF6B6B', '#FFA500'],
                    'animation_speed': 'slow',
                    'intensity': 'high'
                }
            },
            
            'rock': {
                'primary_effects': [
                    'color_dark_dramatic',
                    'zoom_dramatic',
                    'shake_energy',
                    'fade_smooth'
                ],
                'style_config': {
                    'font_family': 'Arial-Black',
                    'color_palette': ['#000000', '#FF0000', '#FFFFFF'],
                    'animation_speed': 'fast',
                    'intensity': 'extreme'
                }
            },
            
            'hip_hop': {
                'primary_effects': [
                    'text_overlay_rapid',
                    'color_dark_dramatic',
                    'shake_energy',
                    'fade_smooth'
                ],
                'style_config': {
                    'font_family': 'Impact',
                    'color_palette': ['#000000', '#FFFF00', '#00FF00'],
                    'animation_speed': 'very_fast',
                    'intensity': 'high'
                }
            },
            
            'edm': {
                'primary_effects': [
                    'color_neon_flash',
                    'zoom_smooth',
                    'text_overlay_rapid',
                    'fade_smooth'
                ],
                'style_config': {
                    'font_family': 'Arial-Bold',
                    'color_palette': ['#00FFFF', '#FF00FF', '#FFFF00'],
                    'animation_speed': 'very_fast',
                    'intensity': 'extreme'
                }
            },
            
            'pop': {
                'primary_effects': [
                    'text_overlay_elegant',
                    'color_warm_boost',
                    'zoom_smooth',
                    'fade_smooth'
                ],
                'style_config': {
                    'font_family': 'Arial',
                    'color_palette': ['#FF69B4', '#87CEEB', '#FFB6C1'],
                    'animation_speed': 'medium',
                    'intensity': 'medium'
                }
            }
        }
    
    def apply_genre_effects(self, clip: VideoFileClip, genre: str, analysis_data: Dict, 
                          user_preferences: Optional[Dict] = None) -> VideoFileClip:
        """
        Aplică efectele specifice genului
        
        Args:
            clip: Videoclipul original
            genre: Genul muzical detectat
            analysis_data: Datele din analiză
            user_preferences: Preferințele utilizatorului
            
        Returns:
            Videoclip procesat cu efecte
        """
        logger.info(f"🎨 Aplicând efecte pentru genul: {genre}")
        
        # Obține configurația pentru gen
        genre_config = self.genre_mappings.get(genre, self.genre_mappings['pop'])
        
        # Începe cu clip-ul original
        processed_clip = clip
        
        try:
            # Aplică efectele primare (doar primele 2 pentru stabilitate)
            effects_to_apply = genre_config['primary_effects'][:2]
            
            for effect_name in effects_to_apply:
                if effect_name in self.effects_library:
                    effect_function = self.effects_library[effect_name]
                    processed_clip = self._apply_single_effect(
                        processed_clip, effect_function, effect_name, 
                        analysis_data, genre_config
                    )
            
            logger.info(f"✅ Efecte aplicate cu succes pentru {genre}")
            return processed_clip
            
        except Exception as e:
            logger.error(f"❌ Eroare la aplicarea efectelor: {str(e)}")
            return clip  # Returnează clip-ul original în caz de eroare
    
    def _apply_single_effect(self, clip: VideoFileClip, effect_function, effect_name: str,
                           analysis_data: Dict, genre_config: Dict) -> VideoFileClip:
        """Aplică un singur efect cu gestionarea erorilor"""
        try:
            if effect_name.startswith('text_overlay'):
                return effect_function(
                    clip, 
                    analysis_data.get('lyrics', {}), 
                    analysis_data.get('audio', {}),
                    genre_config['style_config']
                )
            elif effect_name.startswith('color'):
                if 'neon_flash' in effect_name:
                    return effect_function(
                        clip, 
                        analysis_data.get('audio', {}).get('beat_times', [])
                    )
                else:
                    return effect_function(clip)
            elif effect_name.startswith('zoom'):
                return effect_function(
                    clip, 
                    analysis_data.get('emotional_peaks', [])
                )
            elif effect_name.startswith('shake'):
                return effect_function(
                    clip, 
                    analysis_data.get('audio', {}).get('energy_timeline', [])
                )
            else:
                return effect_function(clip)
                
        except Exception as e:
            logger.warning(f"⚠️ Nu s-a putut aplica efectul {effect_name}: {str(e)}")
            return clip
    
    # === TEXT OVERLAY EFFECTS ===
    
    def text_overlay_emotional(self, clip: VideoFileClip, lyrics: Dict, 
                             audio: Dict, style_config: Dict) -> VideoFileClip:
        """Text overlay pentru manele/pop emoțional"""
        try:
            # Text simplu pentru demonstrație
            text = "🎵 Music Video Edit 🎵"
            
            txt_clip = TextClip(
                text,
                fontsize=60,
                font='Arial-Bold',
                color='gold',
                stroke_color='black',
                stroke_width=2
            ).set_position('center').set_duration(min(3, clip.duration)).set_start(1)
            
            # Animație fade in/out
            txt_clip = txt_clip.crossfadein(0.5).crossfadeout(0.5)
            
            return CompositeVideoClip([clip, txt_clip])
            
        except Exception as e:
            logger.warning(f"Nu s-a putut crea text overlay emoțional: {str(e)}")
            return clip
    
    def text_overlay_rapid(self, clip: VideoFileClip, lyrics: Dict, 
                         audio: Dict, style_config: Dict) -> VideoFileClip:
        """Text overlay rapid pentru hip-hop"""
        try:
            # Text rapid pentru hip-hop
            words = ['BEAT', 'DROP', 'FLOW', 'FIRE']
            text_clips = []
            
            for i, word in enumerate(words):
                start_time = i * 0.8
                if start_time >= clip.duration:
                    break
                    
                txt_clip = TextClip(
                    word,
                    fontsize=80,
                    font='Arial-Black',
                    color='yellow',
                    stroke_color='black',
                    stroke_width=3
                ).set_position('center').set_duration(0.6).set_start(start_time)
                
                text_clips.append(txt_clip)
            
            if text_clips:
                return CompositeVideoClip([clip] + text_clips)
            else:
                return clip
                
        except Exception as e:
            logger.warning(f"Nu s-a putut crea text overlay rapid: {str(e)}")
            return clip
    
    def text_overlay_elegant(self, clip: VideoFileClip, lyrics: Dict, 
                           audio: Dict, style_config: Dict) -> VideoFileClip:
        """Text overlay elegant pentru pop"""
        try:
            text = "✨ Pop Vibes ✨"
            
            txt_clip = TextClip(
                text,
                fontsize=50,
                font='Arial',
                color='pink',
                stroke_color='white',
                stroke_width=1
            ).set_position('center').set_duration(min(4, clip.duration)).set_start(2)
            
            txt_clip = txt_clip.crossfadein(1).crossfadeout(1)
            
            return CompositeVideoClip([clip, txt_clip])
            
        except Exception as e:
            logger.warning(f"Nu s-a putut crea text overlay elegant: {str(e)}")
            return clip
    
    # === COLOR EFFECTS ===
    
    def color_warm_boost(self, clip: VideoFileClip, intensity: float = 1.0) -> VideoFileClip:
        """Boost cald pentru manele/pop"""
        try:
            def warm_filter(get_frame, t):
                frame = get_frame(t)
                frame = frame.astype(np.float32)
                # Boost red și yellow channels
                frame[:,:,0] = np.clip(frame[:,:,0] * 1.2, 0, 255)  # Red
                frame[:,:,1] = np.clip(frame[:,:,1] * 1.1, 0, 255)  # Green
                return frame.astype(np.uint8)
            
            return clip.fl(warm_filter)
        except Exception as e:
            logger.warning(f"Nu s-a putut aplica warm boost: {str(e)}")
            return clip
    
    def color_dark_dramatic(self, clip: VideoFileClip, intensity: float = 1.0) -> VideoFileClip:
        """Color grading dramatic pentru rock"""
        try:
            def dark_filter(get_frame, t):
                frame = get_frame(t)
                frame = frame.astype(np.float32)
                # Reduce brightness, increase contrast
                frame = frame * 0.8  # Darker
                frame = np.clip((frame - 128) * 1.3 + 128, 0, 255)
                return frame.astype(np.uint8)
            
            return clip.fl(dark_filter)
        except Exception as e:
            logger.warning(f"Nu s-a putut aplica dark dramatic: {str(e)}")
            return clip
    
    def color_neon_flash(self, clip: VideoFileClip, beat_times: List[float]) -> VideoFileClip:
        """Flash-uri neon pentru EDM"""
        try:
            def neon_filter(get_frame, t):
                frame = get_frame(t)
                
                # Check dacă suntem pe un beat
                on_beat = any(abs(t - beat_time) < 0.2 for beat_time in beat_times[:10])
                
                if on_beat:
                    # Neon flash effect
                    frame = frame.astype(np.float32)
                    # Adaugă cyan tint
                    frame[:,:,1] = np.clip(frame[:,:,1] + 50, 0, 255)  # Green
                    frame[:,:,2] = np.clip(frame[:,:,2] + 50, 0, 255)  # Blue
                
                return frame.astype(np.uint8)
            
            return clip.fl(neon_filter)
        except Exception as e:
            logger.warning(f"Nu s-a putut aplica neon flash: {str(e)}")
            return clip
    
    # === MOTION EFFECTS ===
    
    def zoom_dramatic(self, clip: VideoFileClip, emotional_peaks: List[Dict]) -> VideoFileClip:
        """Zoom dramatic pentru momente emoționale"""
        try:
            if not emotional_peaks:
                return clip
                
            def zoom_effect(t):
                base_zoom = 1.0
                for peak in emotional_peaks[:3]:  # Maxim 3 peak-uri
                    peak_time = peak.get('time', 0)
                    if abs(t - peak_time) < 2.0:  # În raza de 2 secunde
                        distance = abs(t - peak_time)
                        zoom_factor = 1.0 + (0.3 * (2.0 - distance) / 2.0)
                        base_zoom = max(base_zoom, zoom_factor)
                
                return base_zoom
            
            return clip.resize(zoom_effect)
        except Exception as e:
            logger.warning(f"Nu s-a putut aplica zoom dramatic: {str(e)}")
            return clip
    
    def zoom_smooth(self, clip: VideoFileClip, emotional_peaks: List[Dict]) -> VideoFileClip:
        """Zoom smooth pentru pop"""
        try:
            def smooth_zoom(t):
                # Zoom ușor progresiv
                progress = t / clip.duration
                return 1.0 + 0.1 * progress
            
            return clip.resize(smooth_zoom)
        except Exception as e:
            logger.warning(f"Nu s-a putut aplica zoom smooth: {str(e)}")
            return clip
    
    def shake_energy(self, clip: VideoFileClip, energy_timeline: List[float]) -> VideoFileClip:
        """Shake pentru momente de energie"""
        try:
            if not energy_timeline:
                return clip
                
            def shake_effect(t):
                # Calculează energia la momentul t
                timeline_pos = int((t / clip.duration) * len(energy_timeline))
                if 0 <= timeline_pos < len(energy_timeline):
                    energy = energy_timeline[timeline_pos]
                    if energy > 0.7:  # High energy
                        shake_x = random.randint(-3, 3)
                        shake_y = random.randint(-2, 2)
                        return (shake_x, shake_y)
                
                return (0, 0)
            
            return clip.set_position(shake_effect)
        except Exception as e:
            logger.warning(f"Nu s-a putut aplica shake energy: {str(e)}")
            return clip
    
    # === TRANSITION EFFECTS ===
    
    def fade_smooth(self, clip: VideoFileClip) -> VideoFileClip:
        """Fade smooth"""
        try:
            fade_duration = min(1.0, clip.duration / 4)
            return clip.crossfadein(fade_duration).crossfadeout(fade_duration)
        except Exception as e:
            logger.warning(f"Nu s-a putut aplica fade smooth: {str(e)}")
            return clip
