"""
AI Video Source Manager
Unified manager for AI-generated images as video sources with multiple providers
"""

import asyncio
import logging
import os
import time
from typing import Optional, List, Dict, Any, Union
from dataclasses import dataclass
from enum import Enum
import json
from PIL import Image
from io import BytesIO

# Import AI generators
try:
    import aiohttp
    import requests
    PERCHANCE_AVAILABLE = True
except ImportError:
    PERCHANCE_AVAILABLE = False
    logging.warning("HTTP libraries not available for Perchance")

try:
    from .ai_image_generator import AIImageGenerator, GenerationConfig
    AI_IMAGE_GENERATOR_AVAILABLE = True
except ImportError:
    AI_IMAGE_GENERATOR_AVAILABLE = False
    logging.warning("AI Image Generator not available")

logger = logging.getLogger(__name__)

class AIProvider(str, Enum):
    """Available AI image providers"""
    PERCHANCE = "perchance"
    OPENAI = "openai"
    STABLE_DIFFUSION = "stable_diffusion"
    LOCAL_SD = "local_sd"  # Local Stable Diffusion (Automatic1111/Fooocus)
    AUTO = "auto"  # Automatic selection based on availability

@dataclass
class AIImageConfig:
    """Configuration for AI image generation"""
    width: int = 1080
    height: int = 1920  # Default to portrait for video
    style: str = "realistic"
    quality: str = "standard"
    provider: AIProvider = AIProvider.AUTO
    negative_prompt: str = ""
    steps: int = 20
    guidance_scale: float = 7.5
    
    def to_video_aspect_resolution(self, aspect: str = "portrait"):
        """Convert to video aspect ratio resolution"""
        if aspect == "landscape":
            self.width, self.height = 1920, 1080
        elif aspect == "square":
            self.width, self.height = 1080, 1080
        else:  # portrait
            self.width, self.height = 1080, 1920

@dataclass
class AIImageResult:
    """Result from AI image generation"""
    success: bool
    image_data: Optional[bytes] = None
    provider_used: Optional[str] = None
    generation_time: float = 0.0
    error_message: Optional[str] = None
    cached: bool = False
    cost: float = 0.0

class ContextualPromptGenerator:
    """Generates contextual prompts based on video content"""
    
    def __init__(self):
        self.style_templates = {
            "realistic": {
                "landscape": "realistic photograph of {subject}, natural lighting, high resolution, professional photography",
                "portrait": "realistic portrait of {subject}, professional lighting, detailed, high quality",
                "urban": "realistic urban scene with {subject}, modern cityscape, natural lighting",
                "nature": "realistic nature scene featuring {subject}, natural environment, detailed",
                "interior": "realistic interior scene with {subject}, natural lighting, detailed",
                "action": "realistic action scene with {subject}, dynamic movement, sharp focus",
                "abstract": "realistic image of {subject}, clean composition, professional quality"
            },
            "cinematic": {
                "landscape": "cinematic shot of {subject}, dramatic lighting, film grain, wide angle lens",
                "portrait": "cinematic portrait of {subject}, dramatic lighting, shallow depth of field",
                "urban": "cinematic urban scene with {subject}, moody atmosphere, film noir style",
                "nature": "cinematic nature scene with {subject}, golden hour lighting, epic composition",
                "interior": "cinematic interior shot with {subject}, dramatic lighting, atmospheric",
                "action": "cinematic action sequence with {subject}, dynamic camera angle, motion blur",
                "abstract": "cinematic image of {subject}, dramatic lighting, artistic composition"
            },
            "artistic": {
                "landscape": "artistic interpretation of {subject}, painterly style, vibrant colors",
                "portrait": "artistic portrait of {subject}, expressive style, creative composition",
                "urban": "artistic urban scene with {subject}, stylized, creative perspective",
                "nature": "artistic nature scene with {subject}, impressionistic style, beautiful colors",
                "interior": "artistic interior with {subject}, creative lighting, stylized",
                "action": "artistic action scene with {subject}, dynamic composition, expressive",
                "abstract": "artistic representation of {subject}, creative style, expressive"
            },
            "fantasy": {
                "landscape": "fantasy landscape with {subject}, magical atmosphere, mystical lighting",
                "portrait": "fantasy character {subject}, magical elements, ethereal lighting",
                "urban": "fantasy city scene with {subject}, magical architecture, mystical atmosphere",
                "nature": "enchanted forest with {subject}, magical creatures, fantasy elements",
                "interior": "fantasy interior with {subject}, magical elements, mystical lighting",
                "action": "fantasy battle scene with {subject}, magical effects, epic composition",
                "abstract": "fantasy scene featuring {subject}, magical elements, mystical atmosphere"
            },
            "cyberpunk": {
                "landscape": "cyberpunk cityscape with {subject}, neon lights, futuristic atmosphere",
                "portrait": "cyberpunk character {subject}, neon lighting, futuristic elements",
                "urban": "cyberpunk street scene with {subject}, neon signs, dark atmosphere",
                "nature": "cyberpunk nature scene with {subject}, technological elements, neon accents",
                "interior": "cyberpunk interior with {subject}, neon lighting, high-tech elements",
                "action": "cyberpunk action scene with {subject}, neon effects, futuristic weapons",
                "abstract": "cyberpunk scene with {subject}, neon aesthetics, futuristic elements"
            },
            "vintage": {
                "landscape": "vintage photograph of {subject}, sepia tones, classic composition",
                "portrait": "vintage portrait of {subject}, classic lighting, retro style",
                "urban": "vintage city scene with {subject}, classic architecture, nostalgic atmosphere",
                "nature": "vintage nature scene with {subject}, classic photography style",
                "interior": "vintage interior with {subject}, classic decor, warm lighting",
                "action": "vintage action scene with {subject}, classic cinematography",
                "abstract": "vintage image of {subject}, classic style, nostalgic feel"
            }
        }

        self.emotional_modifiers = {
            "dramatic": "dramatic lighting, intense atmosphere, high contrast",
            "peaceful": "soft lighting, calm atmosphere, serene mood",
            "mysterious": "dark shadows, mysterious atmosphere, moody lighting",
            "romantic": "warm lighting, romantic atmosphere, soft focus",
            "action": "dynamic lighting, energetic atmosphere, motion effects",
            "melancholic": "muted colors, melancholic mood, soft shadows",
            "neutral": "balanced lighting, natural atmosphere"
        }

        self.quality_enhancers = [
            "high quality", "detailed", "professional", "masterpiece",
            "8k resolution", "sharp focus", "perfect composition",
            "award winning", "trending on artstation", "photorealistic"
        ]

        self.theme_keywords = {
            "business": ["corporate", "professional", "office", "meeting", "success"],
            "technology": ["innovation", "digital", "future", "AI", "computer"],
            "nature": ["landscape", "forest", "ocean", "mountain", "wildlife"],
            "lifestyle": ["people", "family", "home", "happiness", "daily life"],
            "education": ["learning", "school", "books", "knowledge", "study"],
            "health": ["wellness", "fitness", "medical", "healthy", "care"],
            "travel": ["destination", "adventure", "culture", "exploration", "journey"]
        }
    
    def generate_prompt_from_script(self, script: str, style: str = "realistic", emotional_tone: str = "neutral", scene_type: str = "abstract") -> str:
        """Generate enhanced AI prompt from video script with emotional and scene context"""
        # Extract key concepts from script
        key_concepts = self._extract_key_concepts(script)

        # Determine theme if not provided
        theme = self._determine_theme(script, key_concepts)

        # Use scene_type if provided, otherwise use theme
        scene_category = scene_type if scene_type != "abstract" else theme

        # Generate contextual prompt
        if key_concepts:
            main_subject = key_concepts[0]
            template = self.style_templates.get(style, self.style_templates["realistic"])
            category_template = template.get(scene_category, template["abstract"])

            prompt = category_template.format(subject=main_subject)

            # Add additional context from key concepts
            if len(key_concepts) > 1:
                additional_context = ", ".join(key_concepts[1:3])  # Add 2 more concepts
                prompt += f", {additional_context}"

            # Add emotional modifiers
            if emotional_tone in self.emotional_modifiers:
                prompt += f", {self.emotional_modifiers[emotional_tone]}"

            # Add quality enhancers
            quality_enhancer = self.quality_enhancers[hash(script) % len(self.quality_enhancers)]
            prompt += f", {quality_enhancer}"

            return prompt

        # Fallback generic prompt with style and emotion
        base_prompt = f"{style} image, high quality, professional"
        if emotional_tone in self.emotional_modifiers:
            base_prompt += f", {self.emotional_modifiers[emotional_tone]}"

        return base_prompt
    
    def generate_prompt_from_topic(self, topic: str, style: str = "realistic") -> str:
        """Generate AI prompt from video topic/subject"""
        # Clean and process topic
        clean_topic = topic.strip().lower()
        
        # Determine theme from topic
        theme = self._determine_theme_from_topic(clean_topic)
        
        # Get template
        template = self.style_templates.get(style, self.style_templates["realistic"])
        category_template = template.get(theme, template["abstract"])
        
        # Generate prompt
        prompt = category_template.format(subject=topic)
        
        # Add style-specific enhancements
        if style == "cinematic":
            prompt += ", dramatic composition, professional cinematography"
        elif style == "artistic":
            prompt += ", creative composition, artistic vision"
        elif style == "realistic":
            prompt += ", photorealistic, sharp focus, detailed"
        
        return prompt

    def generate_prompt_from_segment(self, segment, style: str = "realistic") -> str:
        """Generate AI prompt from script segment with full context"""
        try:
            # Import here to avoid circular imports
            from .script_analyzer import ScriptSegment

            if isinstance(segment, ScriptSegment):
                # Use segment's visual concepts as main subject
                main_subject = ", ".join(segment.visual_concepts[:2]) if segment.visual_concepts else "abstract concept"

                # Get template based on style and scene type
                template = self.style_templates.get(style, self.style_templates["realistic"])
                category_template = template.get(segment.scene_type, template["abstract"])

                # Generate base prompt
                prompt = category_template.format(subject=main_subject)

                # Add additional visual concepts
                if len(segment.visual_concepts) > 2:
                    additional_concepts = ", ".join(segment.visual_concepts[2:4])
                    prompt += f", {additional_concepts}"

                # Add emotional modifiers
                if segment.emotional_tone in self.emotional_modifiers:
                    prompt += f", {self.emotional_modifiers[segment.emotional_tone]}"

                # Add quality enhancer based on priority score
                quality_index = int(segment.priority_score * len(self.quality_enhancers))
                quality_enhancer = self.quality_enhancers[min(quality_index, len(self.quality_enhancers) - 1)]
                prompt += f", {quality_enhancer}"

                return prompt
            else:
                # Fallback for non-segment input
                return self.generate_prompt_from_script(str(segment), style)

        except Exception as e:
            logger.warning(f"Error generating prompt from segment: {e}")
            return self.generate_prompt_from_script(str(segment), style)

    def _extract_key_concepts(self, text: str) -> List[str]:
        """Extract key concepts from text"""
        # Simple keyword extraction (could be enhanced with NLP)
        words = text.lower().split()
        
        # Filter out common words
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should", "may", "might", "can", "this", "that", "these", "those"}
        
        key_words = [word for word in words if len(word) > 3 and word not in stop_words]
        
        # Return top 5 most relevant words
        return key_words[:5]
    
    def _determine_theme(self, text: str, concepts: List[str]) -> str:
        """Determine theme from text and concepts"""
        text_lower = text.lower()
        
        # Check for theme keywords
        for theme, keywords in self.theme_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return theme
        
        # Check concepts
        for concept in concepts:
            for theme, keywords in self.theme_keywords.items():
                if concept in keywords:
                    return theme
        
        return "abstract"  # Default theme
    
    def _determine_theme_from_topic(self, topic: str) -> str:
        """Determine theme from topic"""
        for theme, keywords in self.theme_keywords.items():
            if any(keyword in topic for keyword in keywords):
                return theme
        return "abstract"

class AIVideoSourceManager:
    """Main manager for AI-generated video sources"""
    
    def __init__(self):
        self.providers = {}
        self.prompt_generator = ContextualPromptGenerator()
        self.cache_dir = "storage/ai_images"
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Initialize available providers
        self._initialize_providers()
        
        # Provider priority (first available will be used for AUTO)
        self.provider_priority = [
            AIProvider.PERCHANCE,  # Free and unlimited - prioritized for stability
            AIProvider.LOCAL_SD,  # Local Stable Diffusion - best quality and speed
            AIProvider.STABLE_DIFFUSION,  # Legacy local support
            AIProvider.OPENAI  # Paid but high quality
        ]
    
    def _initialize_providers(self):
        """Initialize available AI providers"""
        # Check if Stable Diffusion is enabled in config
        from app.config import config
        stable_diffusion_enabled = config.app.get("stable_diffusion_enabled", True)

        # Initialize Local Stable Diffusion (highest priority) - only if enabled
        if stable_diffusion_enabled:
            try:
                from .local_stable_diffusion import LocalStableDiffusionManager
                local_sd = LocalStableDiffusionManager()
                self.providers[AIProvider.LOCAL_SD] = local_sd
                logger.info("✅ Local Stable Diffusion provider initialized (availability will be checked on first use)")
            except Exception as e:
                logger.warning(f"Local Stable Diffusion not available: {e}")
        else:
            logger.info("🚫 Local Stable Diffusion disabled in configuration")

        # Initialize Perchance with Hybrid AI Generator
        if PERCHANCE_AVAILABLE:
            try:
                # Try hybrid generator first (Free AI + Enhanced Placeholders)
                from .free_ai_image_generator import HybridAIImageGenerator
                self.providers[AIProvider.PERCHANCE] = HybridAIImageGenerator()
                logger.info("✅ Hybrid AI provider initialized (Free AI + Enhanced Placeholders)")
            except Exception as e:
                logger.warning(f"Hybrid AI not available: {e}")
                # Fallback to original Perchance
                try:
                    from .perchance_ai import PerchanceImageGenerator
                    self.providers[AIProvider.PERCHANCE] = PerchanceImageGenerator()
                    logger.info("✅ Perchance AI provider initialized (fallback)")
                except Exception as e2:
                    logger.error(f"Failed to initialize any Perchance provider: {e2}")

        # Initialize existing AI Image Generator (OpenAI + Stable Diffusion)
        if AI_IMAGE_GENERATOR_AVAILABLE:
            try:
                self.providers[AIProvider.OPENAI] = AIImageGenerator()
                self.providers[AIProvider.STABLE_DIFFUSION] = AIImageGenerator()
                logger.info("OpenAI and Stable Diffusion providers initialized")
            except Exception as e:
                logger.error(f"Failed to initialize AI Image Generator: {e}")
    
    async def generate_image_from_script(self, script: str, config: Optional[AIImageConfig] = None) -> AIImageResult:
        """Generate AI image based on video script"""
        if config is None:
            config = AIImageConfig()
        
        # Generate contextual prompt
        prompt = self.prompt_generator.generate_prompt_from_script(script, config.style)
        
        return await self.generate_image(prompt, config)
    
    async def generate_image_from_topic(self, topic: str, config: Optional[AIImageConfig] = None) -> AIImageResult:
        """Generate AI image based on video topic"""
        if config is None:
            config = AIImageConfig()
        
        # Generate contextual prompt
        prompt = self.prompt_generator.generate_prompt_from_topic(topic, config.style)
        
        return await self.generate_image(prompt, config)
    
    async def generate_image(self, prompt: str, config: Optional[AIImageConfig] = None) -> AIImageResult:
        """Generate AI image with fallback providers"""
        if config is None:
            config = AIImageConfig()
        
        start_time = time.time()
        
        # Check cache first
        cached_result = self._get_cached_image(prompt, config)
        if cached_result:
            return cached_result
        
        # Determine provider to use
        providers_to_try = self._get_providers_to_try(config.provider)
        
        last_error = None
        for provider in providers_to_try:
            try:
                logger.info(f"Trying {provider} for prompt: {prompt[:50]}...")
                
                result = await self._generate_with_provider(provider, prompt, config)
                
                if result.success:
                    result.generation_time = time.time() - start_time
                    result.provider_used = provider
                    
                    # Cache successful result
                    self._cache_image(prompt, config, result.image_data)
                    
                    logger.info(f"Successfully generated image with {provider} in {result.generation_time:.2f}s")
                    return result
                else:
                    last_error = result.error_message
                    logger.warning(f"{provider} failed: {result.error_message}")
            
            except Exception as e:
                last_error = str(e)
                logger.error(f"Error with {provider}: {e}")
        
        # All providers failed, try fallback generator
        logger.warning("All AI providers failed, using fallback generator")
        try:
            from .fallback_image_generator import FallbackImageGenerator

            fallback_generator = FallbackImageGenerator()

            # Extract scene type and emotional tone from prompt
            scene_type = "abstract"
            emotional_tone = "neutral"

            # Simple keyword detection for scene type
            prompt_lower = prompt.lower()
            if any(word in prompt_lower for word in ["battle", "fight", "war", "combat"]):
                scene_type = "battle"
            elif any(word in prompt_lower for word in ["dialogue", "speaking", "conversation", "talk"]):
                scene_type = "dialogue"
            elif any(word in prompt_lower for word in ["landscape", "scenery", "mountain", "forest", "field"]):
                scene_type = "landscape"
            elif any(word in prompt_lower for word in ["action", "running", "jumping", "moving"]):
                scene_type = "action"

            # Simple keyword detection for emotional tone
            if any(word in prompt_lower for word in ["dramatic", "intense", "powerful"]):
                emotional_tone = "dramatic"
            elif any(word in prompt_lower for word in ["peaceful", "calm", "serene"]):
                emotional_tone = "peaceful"
            elif any(word in prompt_lower for word in ["epic", "heroic", "grand"]):
                emotional_tone = "epic"

            fallback_image = await fallback_generator.generate_contextual_image(
                prompt, config.width, config.height, scene_type, emotional_tone
            )

            if fallback_image:
                logger.info(f"Generated fallback image: {len(fallback_image)} bytes")
                return AIImageResult(
                    success=True,
                    image_data=fallback_image,
                    cost=0.0,
                    provider_used="fallback",
                    generation_time=time.time() - start_time
                )
        except Exception as e:
            logger.error(f"Fallback generator also failed: {e}")

        # If even fallback failed, return error
        return AIImageResult(
            success=False,
            error_message=f"All providers failed. Last error: {last_error}",
            generation_time=time.time() - start_time
        )
    
    def _get_providers_to_try(self, requested_provider: AIProvider) -> List[AIProvider]:
        """Get list of providers to try based on request and availability"""
        if requested_provider == AIProvider.AUTO:
            # Use priority order, but only available providers
            return [p for p in self.provider_priority if p in self.providers]
        elif requested_provider in self.providers:
            return [requested_provider]
        else:
            logger.warning(f"Requested provider {requested_provider} not available, falling back to AUTO")
            return [p for p in self.provider_priority if p in self.providers]
    
    async def _generate_with_provider(self, provider: AIProvider, prompt: str, config: AIImageConfig) -> AIImageResult:
        """Generate image with specific provider"""
        try:
            if provider == AIProvider.LOCAL_SD:
                return await self._generate_with_local_sd(prompt, config)
            elif provider == AIProvider.PERCHANCE:
                return await self._generate_with_perchance(prompt, config)
            elif provider in [AIProvider.OPENAI, AIProvider.STABLE_DIFFUSION]:
                return await self._generate_with_ai_generator(provider, prompt, config)
            else:
                return AIImageResult(
                    success=False,
                    error_message=f"Unknown provider: {provider}"
                )
        except Exception as e:
            return AIImageResult(
                success=False,
                error_message=f"Provider {provider} error: {str(e)}"
            )

    async def _generate_with_local_sd(self, prompt: str, config: AIImageConfig) -> AIImageResult:
        """Generate image using Local Stable Diffusion"""
        try:
            generator = self.providers[AIProvider.LOCAL_SD]

            # Initialize if not already done
            if not hasattr(generator, '_initialized'):
                is_available = await generator.initialize()
                generator._initialized = True
                if not is_available:
                    # Remove from providers list so fallback works
                    if AIProvider.LOCAL_SD in self.providers:
                        del self.providers[AIProvider.LOCAL_SD]
                        logger.info("🔄 Local SD not available, removed from providers list for fallback")
                    return AIImageResult(
                        success=False,
                        error_message="No local Stable Diffusion installations found. Please install Automatic1111 WebUI or Fooocus."
                    )

            # Generate image
            image_data = await generator.generate_image(prompt, config.width, config.height)

            if image_data:
                return AIImageResult(
                    success=True,
                    image_data=image_data,
                    cost=0.0  # Local generation is free
                )
            else:
                return AIImageResult(
                    success=False,
                    error_message="No image data returned from Local Stable Diffusion"
                )

        except Exception as e:
            return AIImageResult(
                success=False,
                error_message=f"Local SD generation failed: {str(e)}"
            )

    async def _generate_with_perchance(self, prompt: str, config: AIImageConfig) -> AIImageResult:
        """Generate image using Perchance"""
        try:
            generator = self.providers[AIProvider.PERCHANCE]

            # Generate image
            image_data = await generator.generate_image(prompt, config.width, config.height)

            if image_data:
                return AIImageResult(
                    success=True,
                    image_data=image_data,
                    cost=0.0  # Perchance is free
                )
            else:
                return AIImageResult(
                    success=False,
                    error_message="No image data returned from Perchance"
                )

        except Exception as e:
            return AIImageResult(
                success=False,
                error_message=f"Perchance generation failed: {str(e)}"
            )
    
    async def _generate_with_ai_generator(self, provider: AIProvider, prompt: str, config: AIImageConfig) -> AIImageResult:
        """Generate image using existing AI Image Generator"""
        try:
            generator = self.providers[provider]
            
            # Convert config
            gen_config = GenerationConfig(
                width=config.width,
                height=config.height,
                negative_prompt=config.negative_prompt,
                steps=config.steps,
                cfg_scale=config.guidance_scale
            )
            
            # Generate image
            image_data = await generator.generate_meme_image(prompt, config.style, 5, gen_config)
            
            if image_data:
                return AIImageResult(
                    success=True,
                    image_data=image_data,
                    cost=generator.openai_api.cost_per_image if provider == AIProvider.OPENAI else 0.0
                )
            else:
                return AIImageResult(
                    success=False,
                    error_message="No image data returned"
                )
        
        except Exception as e:
            return AIImageResult(
                success=False,
                error_message=f"AI Generator failed: {str(e)}"
            )
    
    def _resize_image(self, image_data: bytes, target_width: int, target_height: int) -> bytes:
        """Resize image to target dimensions"""
        try:
            image = Image.open(BytesIO(image_data))
            
            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize
            resized = image.resize((target_width, target_height), Image.Resampling.LANCZOS)
            
            # Convert back to bytes
            output = BytesIO()
            resized.save(output, format='JPEG', quality=90)
            return output.getvalue()
        
        except Exception as e:
            logger.error(f"Error resizing image: {e}")
            return image_data  # Return original if resize fails
    
    def _get_cache_key(self, prompt: str, config: AIImageConfig) -> str:
        """Generate cache key for prompt and config"""
        import hashlib
        
        cache_data = f"{prompt}_{config.width}x{config.height}_{config.style}_{config.provider}"
        return hashlib.md5(cache_data.encode()).hexdigest()
    
    def _get_cached_image(self, prompt: str, config: AIImageConfig) -> Optional[AIImageResult]:
        """Get cached image if available"""
        try:
            cache_key = self._get_cache_key(prompt, config)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.jpg")
            
            if os.path.exists(cache_file):
                # Check if cache is still valid (24 hours)
                if time.time() - os.path.getmtime(cache_file) < 86400:
                    with open(cache_file, 'rb') as f:
                        image_data = f.read()
                    
                    logger.info(f"Using cached image for prompt: {prompt[:50]}...")
                    return AIImageResult(
                        success=True,
                        image_data=image_data,
                        cached=True,
                        generation_time=0.0
                    )
        except Exception as e:
            logger.error(f"Error reading cached image: {e}")
        
        return None
    
    def _cache_image(self, prompt: str, config: AIImageConfig, image_data: bytes):
        """Cache generated image"""
        try:
            cache_key = self._get_cache_key(prompt, config)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.jpg")
            
            with open(cache_file, 'wb') as f:
                f.write(image_data)
            
            logger.info(f"Cached image for prompt: {prompt[:50]}...")
        except Exception as e:
            logger.error(f"Error caching image: {e}")
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return list(self.providers.keys())
    
    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all providers"""
        status = {}
        
        for provider in AIProvider:
            if provider == AIProvider.AUTO:
                continue
                
            available = provider in self.providers
            status[provider] = {
                "available": available,
                "cost": "Free" if provider == AIProvider.PERCHANCE else "Paid" if provider == AIProvider.OPENAI else "Free (Local)",
                "quality": "Good" if provider == AIProvider.PERCHANCE else "Excellent" if provider == AIProvider.OPENAI else "Very Good"
            }
        
        return status
    
    async def cleanup(self):
        """Cleanup resources"""
        for provider_name, provider in self.providers.items():
            try:
                if hasattr(provider, 'close'):
                    await provider.close()
            except Exception as e:
                logger.error(f"Error closing {provider_name}: {e}")
