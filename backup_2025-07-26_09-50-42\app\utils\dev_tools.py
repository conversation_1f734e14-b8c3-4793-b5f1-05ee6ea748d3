"""
Instrumente pentru dezvoltatori și mentenanță aplicație.
Include funcții pentru curățare cache, monitorizare disk și gestionare fișiere.
"""

import os
import shutil
import time
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
from loguru import logger
import psutil


@dataclass
class DiskUsageInfo:
    """Informații despre utilizarea spațiului pe disk."""
    path: str
    total_gb: float
    used_gb: float
    free_gb: float
    usage_percent: float


@dataclass
class DirectoryInfo:
    """Informații despre un director."""
    path: str
    size_mb: float
    file_count: int
    last_modified: datetime


class DevToolsManager:
    """Manager pentru instrumentele de dezvoltare și mentenanță."""
    
    def __init__(self):
        self.storage_dir = Path("storage")
        self.temp_dir = self.storage_dir / "temp"
        self.cache_dir = self.storage_dir / "cache_videos"
        self.videos_dir = self.storage_dir / "videos"
        self.tasks_dir = self.storage_dir / "tasks"
        
        # Creează directoarele dacă nu există
        for directory in [self.temp_dir, self.cache_dir, self.videos_dir, self.tasks_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_disk_usage(self, path: str = None) -> DiskUsageInfo:
        """
        Obține informații despre utilizarea spațiului pe disk.
        
        Args:
            path: Calea pentru care să se verifice spațiul (implicit: directorul curent)
            
        Returns:
            DiskUsageInfo: Informații despre utilizarea diskului
        """
        if path is None:
            path = os.getcwd()
        
        try:
            usage = shutil.disk_usage(path)
            total_gb = usage.total / (1024**3)
            free_gb = usage.free / (1024**3)
            used_gb = total_gb - free_gb
            usage_percent = (used_gb / total_gb) * 100
            
            return DiskUsageInfo(
                path=path,
                total_gb=round(total_gb, 2),
                used_gb=round(used_gb, 2),
                free_gb=round(free_gb, 2),
                usage_percent=round(usage_percent, 2)
            )
        except Exception as e:
            logger.error(f"❌ Eroare la obținerea informațiilor disk: {e}")
            return DiskUsageInfo(path, 0, 0, 0, 0)
    
    def get_directory_info(self, directory: Path) -> DirectoryInfo:
        """
        Obține informații despre un director.
        
        Args:
            directory: Calea către director
            
        Returns:
            DirectoryInfo: Informații despre director
        """
        try:
            if not directory.exists():
                return DirectoryInfo(str(directory), 0, 0, datetime.now())
            
            total_size = 0
            file_count = 0
            last_modified = datetime.fromtimestamp(0)
            
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    try:
                        file_size = file_path.stat().st_size
                        total_size += file_size
                        file_count += 1
                        
                        file_modified = datetime.fromtimestamp(file_path.stat().st_mtime)
                        if file_modified > last_modified:
                            last_modified = file_modified
                    except (OSError, PermissionError):
                        continue
            
            size_mb = total_size / (1024**2)
            
            return DirectoryInfo(
                path=str(directory),
                size_mb=round(size_mb, 2),
                file_count=file_count,
                last_modified=last_modified
            )
            
        except Exception as e:
            logger.error(f"❌ Eroare la obținerea informațiilor director {directory}: {e}")
            return DirectoryInfo(str(directory), 0, 0, datetime.now())
    
    def clear_temp_files(self) -> Tuple[bool, str, Dict[str, int]]:
        """
        Șterge fișierele temporare.
        
        Returns:
            Tuple[bool, str, Dict]: (success, message, stats)
        """
        try:
            stats = {"files_deleted": 0, "size_freed_mb": 0}
            
            if not self.temp_dir.exists():
                return True, "Directorul temp nu există", stats
            
            # Calculează dimensiunea înainte de ștergere
            temp_info = self.get_directory_info(self.temp_dir)
            initial_size = temp_info.size_mb
            
            # Șterge toate fișierele din temp
            for item in self.temp_dir.iterdir():
                try:
                    if item.is_file():
                        item.unlink()
                        stats["files_deleted"] += 1
                    elif item.is_dir():
                        shutil.rmtree(item)
                        stats["files_deleted"] += 1
                except (OSError, PermissionError) as e:
                    logger.warning(f"⚠️ Nu s-a putut șterge {item}: {e}")
            
            stats["size_freed_mb"] = round(initial_size, 2)
            
            message = f"✅ Șterse {stats['files_deleted']} elemente, eliberat {stats['size_freed_mb']} MB"
            logger.info(message)
            
            return True, message, stats
            
        except Exception as e:
            error_msg = f"❌ Eroare la ștergerea fișierelor temporare: {e}"
            logger.error(error_msg)
            return False, error_msg, {"files_deleted": 0, "size_freed_mb": 0}
    
    def clear_cache_videos(self) -> Tuple[bool, str, Dict[str, int]]:
        """
        Șterge cache-ul de videoclipuri.
        
        Returns:
            Tuple[bool, str, Dict]: (success, message, stats)
        """
        try:
            stats = {"files_deleted": 0, "size_freed_mb": 0}
            
            if not self.cache_dir.exists():
                return True, "Directorul cache nu există", stats
            
            # Calculează dimensiunea înainte de ștergere
            cache_info = self.get_directory_info(self.cache_dir)
            initial_size = cache_info.size_mb
            
            # Șterge toate fișierele din cache
            for item in self.cache_dir.iterdir():
                try:
                    if item.is_file():
                        item.unlink()
                        stats["files_deleted"] += 1
                    elif item.is_dir():
                        shutil.rmtree(item)
                        stats["files_deleted"] += 1
                except (OSError, PermissionError) as e:
                    logger.warning(f"⚠️ Nu s-a putut șterge {item}: {e}")
            
            stats["size_freed_mb"] = round(initial_size, 2)
            
            message = f"✅ Șterse {stats['files_deleted']} elemente din cache, eliberat {stats['size_freed_mb']} MB"
            logger.info(message)
            
            return True, message, stats
            
        except Exception as e:
            error_msg = f"❌ Eroare la ștergerea cache-ului: {e}"
            logger.error(error_msg)
            return False, error_msg, {"files_deleted": 0, "size_freed_mb": 0}
    
    def cleanup_old_videos(self, keep_last_n: int = 10) -> Tuple[bool, str, Dict[str, int]]:
        """
        Șterge videoclipurile vechi, păstrând ultimele N.
        
        Args:
            keep_last_n: Numărul de videoclipuri de păstrat
            
        Returns:
            Tuple[bool, str, Dict]: (success, message, stats)
        """
        try:
            stats = {"files_deleted": 0, "size_freed_mb": 0}
            
            if not self.videos_dir.exists():
                return True, "Directorul videos nu există", stats
            
            # Obține toate fișierele video sortate după data modificării
            video_files = []
            for item in self.videos_dir.iterdir():
                if item.is_file() and item.suffix.lower() in ['.mp4', '.avi', '.mov', '.mkv']:
                    try:
                        mtime = item.stat().st_mtime
                        size = item.stat().st_size
                        video_files.append((item, mtime, size))
                    except (OSError, PermissionError):
                        continue
            
            # Sortează după data modificării (cele mai noi primul)
            video_files.sort(key=lambda x: x[1], reverse=True)
            
            # Șterge videoclipurile vechi
            files_to_delete = video_files[keep_last_n:]
            
            for file_path, _, size in files_to_delete:
                try:
                    file_path.unlink()
                    stats["files_deleted"] += 1
                    stats["size_freed_mb"] += size / (1024**2)
                except (OSError, PermissionError) as e:
                    logger.warning(f"⚠️ Nu s-a putut șterge {file_path}: {e}")
            
            stats["size_freed_mb"] = round(stats["size_freed_mb"], 2)
            
            message = f"✅ Șterse {stats['files_deleted']} videoclipuri vechi, eliberat {stats['size_freed_mb']} MB"
            logger.info(message)
            
            return True, message, stats
            
        except Exception as e:
            error_msg = f"❌ Eroare la curățarea videoclipurilor vechi: {e}"
            logger.error(error_msg)
            return False, error_msg, {"files_deleted": 0, "size_freed_mb": 0}
    
    def get_storage_summary(self) -> Dict[str, DirectoryInfo]:
        """
        Obține un rezumat al utilizării spațiului în directoarele de storage.
        
        Returns:
            Dict[str, DirectoryInfo]: Informații despre fiecare director
        """
        directories = {
            "temp": self.temp_dir,
            "cache": self.cache_dir,
            "videos": self.videos_dir,
            "tasks": self.tasks_dir
        }
        
        summary = {}
        for name, path in directories.items():
            summary[name] = self.get_directory_info(path)
        
        return summary
    
    def get_system_info(self) -> Dict[str, str]:
        """
        Obține informații despre sistem.
        
        Returns:
            Dict[str, str]: Informații despre sistem
        """
        try:
            # Informații CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Informații memorie
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_total_gb = memory.total / (1024**3)
            memory_used_gb = memory.used / (1024**3)
            
            # Informații disk
            disk_info = self.get_disk_usage()
            
            return {
                "CPU Usage": f"{cpu_percent}%",
                "CPU Cores": str(cpu_count),
                "Memory Usage": f"{memory_percent}% ({memory_used_gb:.1f}GB / {memory_total_gb:.1f}GB)",
                "Disk Usage": f"{disk_info.usage_percent}% ({disk_info.used_gb:.1f}GB / {disk_info.total_gb:.1f}GB)",
                "Free Disk Space": f"{disk_info.free_gb:.1f}GB"
            }
            
        except Exception as e:
            logger.error(f"❌ Eroare la obținerea informațiilor sistem: {e}")
            return {"Error": str(e)}
    
    def cleanup_all(self, keep_videos: int = 10) -> Tuple[bool, str, Dict[str, Dict[str, int]]]:
        """
        Efectuează o curățare completă a tuturor fișierelor temporare.
        
        Args:
            keep_videos: Numărul de videoclipuri de păstrat
            
        Returns:
            Tuple[bool, str, Dict]: (success, message, detailed_stats)
        """
        try:
            all_stats = {}
            messages = []
            overall_success = True
            
            # Curăță fișierele temporare
            success, msg, stats = self.clear_temp_files()
            all_stats["temp"] = stats
            messages.append(f"Temp: {msg}")
            if not success:
                overall_success = False
            
            # Curăță cache-ul
            success, msg, stats = self.clear_cache_videos()
            all_stats["cache"] = stats
            messages.append(f"Cache: {msg}")
            if not success:
                overall_success = False
            
            # Curăță videoclipurile vechi
            success, msg, stats = self.cleanup_old_videos(keep_videos)
            all_stats["videos"] = stats
            messages.append(f"Videos: {msg}")
            if not success:
                overall_success = False
            
            # Calculează totalurile
            total_files = sum(stats.get("files_deleted", 0) for stats in all_stats.values())
            total_size = sum(stats.get("size_freed_mb", 0) for stats in all_stats.values())
            
            summary_msg = f"🧹 Curățare completă: {total_files} fișiere șterse, {total_size:.2f} MB eliberat"
            messages.insert(0, summary_msg)
            
            final_message = "\n".join(messages)
            logger.info(summary_msg)
            
            return overall_success, final_message, all_stats
            
        except Exception as e:
            error_msg = f"❌ Eroare la curățarea completă: {e}"
            logger.error(error_msg)
            return False, error_msg, {}

    def cleanup_locked_files(self) -> Tuple[bool, str, Dict[str, int]]:
        """
        Curăță fișierele blocate și forțează eliberarea memoriei.

        Returns:
            Tuple[bool, str, Dict]: (success, message, stats)
        """
        try:
            import gc
            import time

            logger.info("🔓 Curățare fișiere blocate...")

            stats = {"locked_files": 0, "files_deleted": 0}

            # Forțează garbage collection
            gc.collect()

            # Găsește fișierele temporare blocate în tasks
            locked_files = []
            tasks_dir = self.storage_path / "tasks"

            if tasks_dir.exists():
                for task_dir in tasks_dir.iterdir():
                    if task_dir.is_dir():
                        for file_path in task_dir.glob("temp-*.mp4"):
                            if file_path.exists():
                                try:
                                    # Încearcă să deschidă fișierul pentru a verifica dacă este blocat
                                    with open(file_path, 'r+b') as f:
                                        pass
                                except (PermissionError, OSError):
                                    locked_files.append(file_path)

            stats["locked_files"] = len(locked_files)

            # Încearcă să șteargă fișierele blocate cu retry logic
            for file_path in locked_files:
                for attempt in range(3):
                    try:
                        time.sleep(1)  # Așteaptă între încercări
                        gc.collect()   # Forțează garbage collection
                        file_path.unlink()
                        stats["files_deleted"] += 1
                        logger.info(f"✅ Șters fișier blocat: {file_path.name}")
                        break
                    except Exception as e:
                        if attempt == 2:  # Ultima încercare
                            logger.warning(f"⚠️ Nu s-a putut șterge fișierul blocat {file_path}: {e}")

            message = f"✅ Procesate {stats['locked_files']} fișiere blocate, șterse {stats['files_deleted']}"
            logger.info(message)
            return True, message, stats

        except Exception as e:
            error_msg = f"❌ Eroare la curățarea fișierelor blocate: {e}"
            logger.error(error_msg)
            return False, error_msg, {"locked_files": 0, "files_deleted": 0}


# Instanță globală pentru instrumentele de dezvoltare
_dev_tools_manager = None

def get_dev_tools_manager() -> DevToolsManager:
    """Returnează instanța globală a managerului de instrumente dezvoltare."""
    global _dev_tools_manager
    if _dev_tools_manager is None:
        _dev_tools_manager = DevToolsManager()
    return _dev_tools_manager
