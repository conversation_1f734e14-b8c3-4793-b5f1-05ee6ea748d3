"""
MoneyPrinterTurbo Application Package

This package initializes compatibility fixes and core components
for the MoneyPrinterTurbo video generation application.
"""

# Apply PIL compatibility patch early to fix MoviePy + Pillow 10+ issues
try:
    from .utils.pil_compatibility import initialize_pil_compatibility
    initialize_pil_compatibility()
except Exception as e:
    import logging
    logging.getLogger(__name__).warning(f"PIL compatibility initialization failed: {e}")

__version__ = "1.2.6"