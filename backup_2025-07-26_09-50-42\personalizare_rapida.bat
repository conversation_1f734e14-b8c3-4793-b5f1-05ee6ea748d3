@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: ========================================
:: 🎨 Script de Personalizare Rapidă
:: Transformă MoneyPrinterTurbo în aplicația voastră
:: ========================================

title Personalizare Rapidă - Aparat de Scos Masele la Fraieri

:: Culori pentru output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "RESET=[0m"

echo.
echo %CYAN%========================================%RESET%
echo %CYAN%🎨 Personalizare Rapidă%RESET%
echo %CYAN%========================================%RESET%
echo %GREEN%Transformați MoneyPrinterTurbo în aplicația voastră!%RESET%
echo.

:: Verifică dacă suntem în directorul corect
if not exist "webui\Main.py" (
    echo %RED%❌ EROARE: Nu sunt în directorul corect!%RESET%
    echo %YELLOW%Vă rugăm să rulați acest script din directorul MoneyPrinterTurbo%RESET%
    pause
    exit /b 1
)

:: Meniu principal
:main_menu
cls
echo %CYAN%========================================%RESET%
echo %CYAN%🎨 Meniu Personalizare%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %YELLOW%1.%RESET% 🏷️ Schimbă numele aplicației
echo %YELLOW%2.%RESET% 🎨 Personalizează interfața
echo %YELLOW%3.%RESET% 🌐 Configurează limba română
echo %YELLOW%4.%RESET% 📝 Creează documentația personalizată
echo %YELLOW%5.%RESET% 🔧 Setup mediu de dezvoltare
echo %YELLOW%6.%RESET% 📦 Creează pachet de distribuție
echo %YELLOW%7.%RESET% 🚀 Personalizare completă (toate opțiunile)
echo %YELLOW%0.%RESET% ❌ Ieșire
echo.

set /p "choice=Alegeți opțiunea (1-7, 0 pentru ieșire): "

if "%choice%"=="1" goto :change_name
if "%choice%"=="2" goto :customize_ui
if "%choice%"=="3" goto :setup_romanian
if "%choice%"=="4" goto :create_docs
if "%choice%"=="5" goto :setup_dev
if "%choice%"=="6" goto :create_package
if "%choice%"=="7" goto :full_customization
if "%choice%"=="0" goto :exit
goto :invalid_choice

:change_name
echo.
echo %GREEN%🏷️ Schimbarea Numelui Aplicației%RESET%
echo %BLUE%========================================%RESET%
echo.

set /p "app_name=Introduceți numele noii aplicații: "
if "%app_name%"=="" (
    echo %RED%❌ Numele nu poate fi gol!%RESET%
    pause
    goto :main_menu
)

set /p "app_icon=Introduceți iconița aplicației (ex: 💰, 🎬, 🚀): "
if "%app_icon%"=="" set "app_icon=🎬"

echo.
echo %BLUE%🔄 Aplicarea schimbărilor...%RESET%

:: Schimbă titlul în Main.py
powershell -Command "(Get-Content 'webui\Main.py') -replace 'st\.title\(\".*\"\)', 'st.title(\"%app_icon% %app_name%\")' | Set-Content 'webui\Main.py'"

:: Schimbă page_title
powershell -Command "(Get-Content 'webui\Main.py') -replace 'page_title=\".*\"', 'page_title=\"%app_name%\"' | Set-Content 'webui\Main.py'"

:: Schimbă page_icon
powershell -Command "(Get-Content 'webui\Main.py') -replace 'page_icon=\".*\"', 'page_icon=\"%app_icon%\"' | Set-Content 'webui\Main.py'"

echo %GREEN%✅ Numele aplicației a fost schimbat în: %app_icon% %app_name%%RESET%
echo.
pause
goto :main_menu

:customize_ui
echo.
echo %GREEN%🎨 Personalizarea Interfeței%RESET%
echo %BLUE%========================================%RESET%
echo.

echo %YELLOW%Selectați tema de culori:%RESET%
echo %BLUE%1.%RESET% 🔵 Albastru profesional
echo %GREEN%2.%RESET% 🟢 Verde natural
echo %RED%3.%RESET% 🔴 Roșu energic
echo %YELLOW%4.%RESET% 🟡 Galben vibrant
echo %CYAN%5.%RESET% 🔷 Cyan modern

set /p "color_choice=Alegeți tema (1-5): "

:: Creează fișierul de stil personalizat
echo /* Stil personalizat generat automat */ > webui\custom_style.css

if "%color_choice%"=="1" (
    echo .main-header { background: linear-gradient(90deg, #1f4e79, #2980b9^); } >> webui\custom_style.css
    set "theme_name=Albastru Profesional"
)
if "%color_choice%"=="2" (
    echo .main-header { background: linear-gradient(90deg, #27ae60, #2ecc71^); } >> webui\custom_style.css
    set "theme_name=Verde Natural"
)
if "%color_choice%"=="3" (
    echo .main-header { background: linear-gradient(90deg, #c0392b, #e74c3c^); } >> webui\custom_style.css
    set "theme_name=Roșu Energic"
)
if "%color_choice%"=="4" (
    echo .main-header { background: linear-gradient(90deg, #f39c12, #f1c40f^); } >> webui\custom_style.css
    set "theme_name=Galben Vibrant"
)
if "%color_choice%"=="5" (
    echo .main-header { background: linear-gradient(90deg, #16a085, #1abc9c^); } >> webui\custom_style.css
    set "theme_name=Cyan Modern"
)

echo .custom-button { border-radius: 10px; padding: 10px 20px; } >> webui\custom_style.css

echo %GREEN%✅ Tema "%theme_name%" a fost aplicată%RESET%
echo.
pause
goto :main_menu

:setup_romanian
echo.
echo %GREEN%🌐 Configurarea Limbii Române%RESET%
echo %BLUE%========================================%RESET%
echo.

echo %BLUE%🔄 Configurez limba română ca implicită...%RESET%

:: Setează limba română în config
powershell -Command "(Get-Content 'config.toml') -replace 'language = \".*\"', 'language = \"ro\"' | Set-Content 'config.toml'"

:: Setează vocea română
powershell -Command "(Get-Content 'config.toml') -replace 'voice_name = \".*\"', 'voice_name = \"ro-RO-AlinaNeural-Female\"' | Set-Content 'config.toml'"

echo %GREEN%✅ Limba română configurată ca implicită%RESET%
echo %GREEN%✅ Vocea românească Alina setată%RESET%
echo.
pause
goto :main_menu

:create_docs
echo.
echo %GREEN%📝 Crearea Documentației Personalizate%RESET%
echo %BLUE%========================================%RESET%
echo.

set /p "project_name=Introduceți numele proiectului: "
set /p "author_name=Introduceți numele autorului: "
set /p "project_description=Introduceți descrierea proiectului: "

echo # %project_name% > README_PERSONALIZAT.md
echo. >> README_PERSONALIZAT.md
echo **Autor**: %author_name% >> README_PERSONALIZAT.md
echo **Descriere**: %project_description% >> README_PERSONALIZAT.md
echo. >> README_PERSONALIZAT.md
echo ## Caracteristici >> README_PERSONALIZAT.md
echo. >> README_PERSONALIZAT.md
echo - 🎬 Generare automată de videoclipuri >> README_PERSONALIZAT.md
echo - 🇷🇴 Suport complet pentru limba română >> README_PERSONALIZAT.md
echo - 🎤 Voci românești naturale >> README_PERSONALIZAT.md
echo - 📱 Interfață prietenoasă >> README_PERSONALIZAT.md
echo. >> README_PERSONALIZAT.md
echo ## Instalare >> README_PERSONALIZAT.md
echo. >> README_PERSONALIZAT.md
echo 1. Rulați `lansare_automata.bat` >> README_PERSONALIZAT.md
echo 2. Configurați cheile API în Basic Settings >> README_PERSONALIZAT.md
echo 3. Începeți să creați videoclipuri! >> README_PERSONALIZAT.md
echo. >> README_PERSONALIZAT.md
echo ------- >> README_PERSONALIZAT.md
echo Bazat pe MoneyPrinterTurbo >> README_PERSONALIZAT.md

echo %GREEN%✅ Documentația personalizată a fost creată: README_PERSONALIZAT.md%RESET%
echo.
pause
goto :main_menu

:setup_dev
echo.
echo %GREEN%🔧 Setup Mediu de Dezvoltare%RESET%
echo %BLUE%========================================%RESET%
echo.

echo %BLUE%🔄 Configurez mediul de dezvoltare...%RESET%

:: Creează directoare pentru dezvoltare
if not exist "custom" mkdir custom
if not exist "custom\plugins" mkdir custom\plugins
if not exist "custom\templates" mkdir custom\templates
if not exist "custom\themes" mkdir custom\themes

:: Creează fișiere de exemplu
echo # Plugin personalizat > custom\plugins\exemplu_plugin.py
echo class ExempluPlugin: >> custom\plugins\exemplu_plugin.py
echo     def __init__(self): >> custom\plugins\exemplu_plugin.py
echo         self.name = "Exemplu Plugin" >> custom\plugins\exemplu_plugin.py

echo # Template personalizat > custom\templates\template_romanesc.py
echo TEMPLATE_ROMANESC = { >> custom\templates\template_romanesc.py
echo     "name": "Conținut Viral Românesc", >> custom\templates\template_romanesc.py
echo     "prompt": "Generează conținut viral despre România" >> custom\templates\template_romanesc.py
echo } >> custom\templates\template_romanesc.py

:: Creează .gitignore personalizat
echo # Fișiere personalizate > .gitignore.custom
echo /custom/secrets/ >> .gitignore.custom
echo /custom/api_keys/ >> .gitignore.custom
echo *.local >> .gitignore.custom

echo %GREEN%✅ Mediul de dezvoltare a fost configurat%RESET%
echo %GREEN%📁 Directoare create: custom/plugins, custom/templates, custom/themes%RESET%
echo.
pause
goto :main_menu

:create_package
echo.
echo %GREEN%📦 Crearea Pachetului de Distribuție%RESET%
echo %BLUE%========================================%RESET%
echo.

set /p "package_name=Introduceți numele pachetului (fără spații): "
if "%package_name%"=="" set "package_name=ApparatMasele"

echo %BLUE%🔄 Creez pachetul de distribuție...%RESET%

:: Creează directorul de distribuție
if not exist "dist" mkdir dist
if not exist "dist\%package_name%" mkdir "dist\%package_name%"

:: Copiază fișierele esențiale
xcopy "webui" "dist\%package_name%\webui" /E /I /Q
xcopy "app" "dist\%package_name%\app" /E /I /Q
xcopy "resource" "dist\%package_name%\resource" /E /I /Q
copy "requirements.txt" "dist\%package_name%\"
copy "config.example.toml" "dist\%package_name%\"
copy "lansare_automata.bat" "dist\%package_name%\"

:: Creează README pentru distribuție
echo # %package_name% > "dist\%package_name%\README.md"
echo. >> "dist\%package_name%\README.md"
echo Generator automat de videoclipuri cu suport pentru limba română. >> "dist\%package_name%\README.md"
echo. >> "dist\%package_name%\README.md"
echo ## Instalare Rapidă >> "dist\%package_name%\README.md"
echo. >> "dist\%package_name%\README.md"
echo 1. Rulați `lansare_automata.bat` >> "dist\%package_name%\README.md"
echo 2. Urmați instrucțiunile de pe ecran >> "dist\%package_name%\README.md"

echo %GREEN%✅ Pachetul de distribuție a fost creat în: dist\%package_name%%RESET%
echo.
pause
goto :main_menu

:full_customization
echo.
echo %GREEN%🚀 Personalizare Completă%RESET%
echo %BLUE%========================================%RESET%
echo.

echo %YELLOW%Această opțiune va aplica toate personalizările.%RESET%
echo %YELLOW%Continuați? (Y/N)%RESET%
set /p "confirm=: "

if /i not "%confirm%"=="Y" goto :main_menu

echo.
echo %BLUE%🔄 Aplicarea tuturor personalizărilor...%RESET%

:: Aplică toate personalizările
call :change_name_auto
call :setup_romanian
call :setup_dev
call :create_docs

echo.
echo %GREEN%🎉 Personalizarea completă a fost finalizată!%RESET%
echo %GREEN%✅ Numele aplicației: Aparat de Scos Masele la Fraieri%RESET%
echo %GREEN%✅ Limba română configurată%RESET%
echo %GREEN%✅ Mediul de dezvoltare pregătit%RESET%
echo %GREEN%✅ Documentația creată%RESET%
echo.
pause
goto :main_menu

:change_name_auto
:: Schimbă automat numele la "Aparat de Scos Masele la Fraieri"
powershell -Command "(Get-Content 'webui\Main.py') -replace 'st\.title\(\".*\"\)', 'st.title(\"💰 Aparat de Scos Masele la Fraieri\")' | Set-Content 'webui\Main.py'"
powershell -Command "(Get-Content 'webui\Main.py') -replace 'page_title=\".*\"', 'page_title=\"Aparat de Scos Masele la Fraieri\"' | Set-Content 'webui\Main.py'"
powershell -Command "(Get-Content 'webui\Main.py') -replace 'page_icon=\".*\"', 'page_icon=\"💰\"' | Set-Content 'webui\Main.py'"
goto :eof

:invalid_choice
echo.
echo %RED%❌ Opțiune invalidă! Vă rugăm să alegeți 1-7 sau 0.%RESET%
timeout /t 2 /nobreak >nul
goto :main_menu

:exit
echo.
echo %GREEN%👋 Personalizarea s-a încheiat!%RESET%
echo %YELLOW%Aplicația voastră personalizată este gata de utilizare.%RESET%
echo %CYAN%Rulați 'lansare_automata.bat' pentru a o porni.%RESET%
echo.
pause
exit /b 0
