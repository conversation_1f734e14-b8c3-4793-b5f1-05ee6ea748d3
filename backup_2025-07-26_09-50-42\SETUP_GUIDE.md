# MoneyPrinterTurbo Setup Guide

## 🎯 What is MoneyPrinterTurbo?

MoneyPrinterTurbo is an AI-powered video generation tool that automatically creates high-definition short videos from simple topics or keywords. It generates:

- Video copy/script
- Video materials from stock footage
- Video subtitles
- Background music
- Complete synthesized videos

## 🚀 Quick Start

### 1. Initial Setup
Run the setup script to install all dependencies:
```bash
setup.bat
```

### 2. Start the Application

**Option A: Web Interface (Recommended for beginners)**
```bash
start_webui.bat
```
- Opens at: http://localhost:8501
- User-friendly interface
- No coding required

**Option B: API Service (For developers)**
```bash
start_api.bat
```
- API docs at: http://127.0.0.1:8080/docs
- RESTful API for integration

## ⚙️ Configuration

### Required API Keys
Before generating videos, you need to configure API keys in `config.toml`:

1. **Pexels API Key** (for video materials):
   - Register at: https://www.pexels.com/api/
   - Add your key to `pexels_api_keys = ["YOUR_API_KEY"]`

2. **LLM Provider** (for script generation):
   - Choose from: OpenAI, Moonshot, Azure, Qwen, DeepSeek, Gemini, etc.
   - Set `llm_provider = "openai"` (or your preferred provider)
   - Add corresponding API key (e.g., `openai_api_key = "YOUR_KEY"`)

### Optional Configuration
- **Video Source**: Choose between "pexels" or "pixabay"
- **Subtitle Provider**: "edge" (faster) or "whisper" (more accurate)
- **Voice Synthesis**: Multiple providers available

## 🎬 Features

### Video Formats
- **Portrait**: 9:16 (1080x1920) - Perfect for TikTok, Instagram Stories
- **Landscape**: 16:9 (1920x1080) - Perfect for YouTube, Facebook

### Supported Languages
- English
- Chinese (Simplified)
- Romanian (Română) - Full support with native voices
- German (Deutsch)
- Portuguese (Português)
- Vietnamese (Tiếng Việt)
- Multiple voice synthesis options

### AI Providers Supported
- OpenAI (GPT-4, GPT-3.5)
- Moonshot (月之暗面)
- Azure OpenAI
- Qwen (通义千问)
- DeepSeek
- Google Gemini
- Ollama (local models)
- G4F (free GPT)
- OneAPI
- Cloudflare
- ERNIE (文心一言)

## 📁 Project Structure
```
MoneyPrinterTurbo/
├── app/                 # Main application code
├── webui/              # Streamlit web interface
├── resource/           # Fonts, music, assets
├── config.toml         # Configuration file
├── setup.bat           # Setup script
├── start_webui.bat     # Start web interface
├── start_api.bat       # Start API service
└── requirements.txt    # Python dependencies
```

## 🔧 Troubleshooting

### Common Issues

1. **ImageMagick Error**:
   - Download from: https://imagemagick.org/script/download.php
   - Choose static library version
   - Update `imagemagick_path` in config.toml

2. **FFmpeg Error**:
   - Usually auto-downloaded
   - If issues persist, download manually and set `ffmpeg_path`

3. **Whisper Model Download**:
   - Requires good internet connection
   - Model is ~3GB in size
   - Alternative: Use "edge" subtitle provider

## 🌐 Web Interface Features

- **Topic Input**: Simply enter your video topic
- **Batch Generation**: Create multiple videos at once
- **Voice Preview**: Test different voices before generation
- **Subtitle Customization**: Adjust font, position, color, size
- **Background Music**: Random or custom music files
- **Material Duration**: Control video clip switching frequency

## 📚 API Documentation

When running the API service, visit http://127.0.0.1:8080/docs for:
- Interactive API documentation
- Request/response examples
- Authentication details
- Endpoint testing interface

## 🎵 Customization

### Adding Custom Fonts
Place font files in `resource/fonts/` directory

### Adding Custom Music
Place music files in `resource/songs/` directory

### Custom Video Materials
Configure `material_directory` in config.toml for custom video storage

## 📞 Support

- **GitHub Issues**: https://github.com/harry0703/MoneyPrinterTurbo/issues
- **Documentation**: Check the docs/ folder for additional resources
- **Voice List**: See docs/voice-list.txt for available voices

## 🔄 Updates

To update to the latest version:
1. Pull latest changes: `git pull`
2. Update dependencies: `pip install -r requirements.txt`
3. Restart the application

---

**Happy Video Creating! 🎬✨**
