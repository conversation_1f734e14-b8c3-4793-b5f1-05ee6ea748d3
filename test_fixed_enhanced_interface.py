#!/usr/bin/env python3
"""
Test Fixed Enhanced Viral Interface

Quick test to verify the attribute error fixes.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))


async def test_fixed_interface():
    """Test the fixed enhanced interface"""
    print("🔧 Testing Fixed Enhanced Viral Interface...")
    print("=" * 50)
    
    try:
        # Test the services are available
        from app.services.viral_content_generator import viral_content_generator
        from app.services.contextual_image_ai import contextual_image_ai
        from app.services.one_click_viral_generator import one_click_viral_generator, ViralVideoConfig
        
        print("✅ All services imported successfully")
        
        # Test configuration
        config = ViralVideoConfig(
            category="motivation",
            target_audience="tineri români 18-35",
            platform="tiktok",
            duration=60,
            use_contextual_images=True,
            max_contextual_images=5
        )
        
        print("✅ Configuration created")
        
        # Test that services are available
        print(f"📊 One-click generator available: {one_click_viral_generator.is_available()}")
        print(f"📊 Viral content generator available: {viral_content_generator.is_available()}")
        print(f"📊 Contextual image AI available: {contextual_image_ai.is_available()}")
        
        # Test a simple topic generation to verify the fix
        if viral_content_generator.is_available():
            print("\n🧪 Testing topic generation...")
            topic = await viral_content_generator.generate_viral_topic(
                category=config.category,
                target_audience=config.target_audience,
                platform=config.platform
            )
            
            if topic:
                print(f"✅ Topic generated: {topic.title}")
                print(f"📊 Viral potential: {topic.viral_potential:.1f}/10")
                print(f"🇷🇴 Romanian relevance: {topic.romanian_relevance:.1f}/10")
            else:
                print("❌ Topic generation failed")
        
        print("\n🎉 Fixed interface test completed successfully!")
        print("🚀 The enhanced interface should now work without attribute errors!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    try:
        success = asyncio.run(test_fixed_interface())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
