"""
AI Sequence Generator
Generates sequences of AI images for fully AI-powered video creation
"""

import os
import asyncio
import random
import time
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

@dataclass
class AISequenceConfig:
    """Configuration for AI sequence generation"""
    duration: float = 15.0
    images_per_second: float = 0.5  # How many images per second (0.5 = 1 image every 2 seconds)
    width: int = 720
    height: int = 1280  # Vertical format for social media
    chaos_level: int = 5
    theme: str = "romanian"
    style: str = "meme"
    transition_duration: float = 0.5  # Seconds for transitions between images
    
    @property
    def total_images(self) -> int:
        """Calculate total number of images needed"""
        return max(3, int(self.duration * self.images_per_second))
    
    @property
    def image_duration(self) -> float:
        """Duration each image should be displayed"""
        return self.duration / self.total_images

@dataclass
class AIImageSegment:
    """Represents a single AI-generated image segment"""
    prompt: str
    start_time: float
    end_time: float
    image_data: Optional[bytes] = None
    enhanced_prompt: str = ""
    generation_time: float = 0.0
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time

class AISequenceGenerator:
    """Generates sequences of AI images for video creation"""
    
    def __init__(self):
        self.cache_dir = Path("storage/ai_sequences")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Import AI services
        try:
            from .free_ai_services import free_ai_generator
            self.ai_generator = free_ai_generator
            self.ai_available = True
        except ImportError:
            logger.warning("Free AI services not available")
            self.ai_generator = None
            self.ai_available = False
        
        # Theme-specific prompt templates
        self.theme_prompts = {
            "romanian": [
                "confused romanian grandmother looking at smartphone",
                "traditional romanian kitchen with modern chaos",
                "romanian man eating mici with philosophical expression",
                "balkan humor scene with absurd elements",
                "romanian village with surreal modern elements",
                "bunica cooking with cosmic energy",
                "romanian street with meme-worthy situation"
            ],
            "gaming": [
                "gamer rage moment with exaggerated expression",
                "gaming setup with chaotic RGB lighting",
                "retro gaming console in surreal environment",
                "esports tournament with absurd elements",
                "gaming character in real world situation",
                "controller floating in cosmic space",
                "gaming meme come to life"
            ],
            "philosophical": [
                "thinking person with galaxy brain effect",
                "ancient philosopher with modern technology",
                "cosmic consciousness visualization",
                "deep thoughts manifesting visually",
                "existential crisis in visual form",
                "wisdom meeting absurdity",
                "philosophical concept as surreal scene"
            ],
            "chaos": [
                "reality breaking apart into fragments",
                "impossible geometric shapes in real space",
                "time and space distortion visualization",
                "cosmic chaos with random elements",
                "surreal landscape with floating objects",
                "interdimensional portal opening",
                "maximum entropy visualization"
            ]
        }
        
        # Chaos level modifiers
        self.chaos_modifiers = {
            1: "clean, simple, minimal",
            2: "slightly exaggerated, mild distortion",
            3: "moderately surreal, some impossible elements",
            4: "clearly absurd, reality-bending",
            5: "chaotic energy, multiple impossible elements",
            6: "very surreal, deep fried aesthetic",
            7: "extremely chaotic, oversaturated colors",
            8: "fever dream quality, maximum distortion",
            9: "reality completely broken, impossible physics",
            10: "interdimensional chaos, pure absurdity"
        }
    
    async def generate_sequence(self, config: AISequenceConfig, base_text: str = "") -> List[AIImageSegment]:
        """Generate a sequence of AI images for video creation"""
        logger.info(f"🎬 Generating AI sequence: {config.total_images} images for {config.duration}s")
        
        # Generate prompts for each segment
        segments = self._create_image_segments(config, base_text)
        
        # Generate images for each segment
        if self.ai_available:
            segments = await self._generate_images_for_segments(segments, config)
        else:
            logger.warning("AI services not available, using placeholder segments")
        
        logger.info(f"✅ Generated {len(segments)} AI image segments")
        return segments
    
    def _create_image_segments(self, config: AISequenceConfig, base_text: str) -> List[AIImageSegment]:
        """Create image segments with prompts"""
        segments = []
        
        # Get theme-specific prompts
        theme_prompts = self.theme_prompts.get(config.theme, self.theme_prompts["chaos"])
        
        # Calculate timing for each segment
        for i in range(config.total_images):
            start_time = i * config.image_duration
            end_time = min((i + 1) * config.image_duration, config.duration)
            
            # Select and enhance prompt
            base_prompt = random.choice(theme_prompts)
            enhanced_prompt = self._enhance_prompt_for_sequence(
                base_prompt, config, i, base_text
            )
            
            segment = AIImageSegment(
                prompt=base_prompt,
                enhanced_prompt=enhanced_prompt,
                start_time=start_time,
                end_time=end_time
            )
            
            segments.append(segment)
            logger.debug(f"Segment {i+1}: {start_time:.1f}s-{end_time:.1f}s | {enhanced_prompt[:50]}...")
        
        return segments
    
    def _enhance_prompt_for_sequence(self, base_prompt: str, config: AISequenceConfig, 
                                   index: int, base_text: str) -> str:
        """Enhance prompt for specific sequence position and chaos level"""
        
        # Add chaos modifier
        chaos_modifier = self.chaos_modifiers.get(config.chaos_level, "moderate chaos")
        
        # Add style modifiers
        style_modifiers = {
            "meme": "internet meme style, viral content, social media",
            "absurd": "surreal, absurd, chaotic, deep fried meme aesthetic",
            "classic": "classic meme format, clean, simple, recognizable",
            "romanian": "romanian culture, balkan humor, eastern european vibes",
            "gaming": "gaming meme, gamer culture, video game aesthetic",
            "philosophical": "big brain meme, galaxy brain, intellectual humor"
        }
        
        style_modifier = style_modifiers.get(config.style, "meme style")
        
        # Add progression for sequence continuity
        progression_modifiers = {
            0: "establishing shot, setting the scene",
            1: "building tension, something happening",
            2: "climax moment, peak chaos",
            3: "resolution, aftermath",
            4: "final punchline, maximum impact"
        }
        
        # Use modulo to cycle through progression for longer sequences
        progression = progression_modifiers.get(index % len(progression_modifiers), "continuing action")
        
        # Combine all elements
        enhanced = f"{base_prompt}, {style_modifier}, {chaos_modifier}, {progression}"
        
        # Add text context if available
        if base_text and len(base_text) > 10:
            # Extract key words from text for visual context
            key_words = self._extract_visual_keywords(base_text)
            if key_words:
                enhanced += f", visual elements: {', '.join(key_words[:3])}"
        
        # Add technical specifications
        enhanced += f", high quality, clear, vibrant colors, {config.width}x{config.height} aspect ratio"
        
        # Add chaos-specific effects
        if config.chaos_level >= 7:
            enhanced += ", oversaturated, deep fried effect, maximum chaos"
        if config.chaos_level >= 9:
            enhanced += ", reality breaking, impossible physics, interdimensional"
        
        return enhanced
    
    def _extract_visual_keywords(self, text: str) -> List[str]:
        """Extract visual keywords from text for image generation context"""
        # Romanian-specific visual keywords
        romanian_keywords = {
            "bunica": "grandmother",
            "mici": "grilled sausages",
            "mâncare": "food",
            "casă": "house",
            "pisică": "cat",
            "câine": "dog",
            "școală": "school",
            "muncă": "work"
        }
        
        # General visual keywords
        visual_keywords = [
            "face", "expression", "eyes", "hands", "food", "house", "car", "phone",
            "computer", "game", "book", "money", "time", "space", "cosmic", "brain"
        ]
        
        text_lower = text.lower()
        found_keywords = []
        
        # Check for Romanian keywords first
        for ro_word, en_word in romanian_keywords.items():
            if ro_word in text_lower:
                found_keywords.append(en_word)
        
        # Check for general visual keywords
        for keyword in visual_keywords:
            if keyword in text_lower:
                found_keywords.append(keyword)
        
        return found_keywords[:5]  # Limit to 5 keywords
    
    async def _generate_images_for_segments(self, segments: List[AIImageSegment], 
                                          config: AISequenceConfig) -> List[AIImageSegment]:
        """Generate AI images for all segments"""
        if not self.ai_generator:
            logger.warning("AI generator not available")
            return segments
        
        successful_generations = 0
        
        for i, segment in enumerate(segments):
            try:
                logger.info(f"🎨 Generating image {i+1}/{len(segments)}: {segment.enhanced_prompt[:50]}...")
                
                start_time = time.time()
                
                # Generate image using free AI services
                image_data = await self.ai_generator.generate_meme_image(
                    prompt=segment.enhanced_prompt,
                    width=config.width,
                    height=config.height,
                    style=config.style,
                    chaos_level=config.chaos_level
                )
                
                generation_time = time.time() - start_time
                segment.generation_time = generation_time
                
                if image_data:
                    segment.image_data = image_data
                    successful_generations += 1
                    logger.info(f"✅ Generated image {i+1}: {len(image_data)} bytes in {generation_time:.1f}s")
                else:
                    logger.warning(f"⚠️ Failed to generate image {i+1}")
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"❌ Error generating image {i+1}: {e}")
        
        logger.info(f"📊 Successfully generated {successful_generations}/{len(segments)} images")
        return segments
    
    def create_sequence_metadata(self, segments: List[AIImageSegment], config: AISequenceConfig) -> Dict:
        """Create metadata for the generated sequence"""
        total_generation_time = sum(s.generation_time for s in segments)
        successful_images = len([s for s in segments if s.image_data])
        
        return {
            "total_segments": len(segments),
            "successful_images": successful_images,
            "success_rate": successful_images / len(segments) if segments else 0,
            "total_generation_time": total_generation_time,
            "average_generation_time": total_generation_time / len(segments) if segments else 0,
            "config": {
                "duration": config.duration,
                "total_images": config.total_images,
                "images_per_second": config.images_per_second,
                "chaos_level": config.chaos_level,
                "theme": config.theme,
                "style": config.style,
                "resolution": f"{config.width}x{config.height}"
            },
            "segments": [
                {
                    "index": i,
                    "start_time": s.start_time,
                    "end_time": s.end_time,
                    "duration": s.duration,
                    "prompt": s.prompt,
                    "enhanced_prompt": s.enhanced_prompt,
                    "has_image": s.image_data is not None,
                    "generation_time": s.generation_time
                }
                for i, s in enumerate(segments)
            ]
        }

# Global instance
ai_sequence_generator = AISequenceGenerator()
