"""
Advanced Script Analyzer for AI Image Generation
Analyzes video scripts to extract visual concepts and generate contextual prompts
"""

import re
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from collections import Counter
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
from nltk.tag import pos_tag

logger = logging.getLogger(__name__)

@dataclass
class ScriptSegment:
    """Represents a segment of the script with visual concepts"""
    text: str
    start_time: float
    end_time: float
    duration: float
    visual_concepts: List[str]
    emotional_tone: str
    scene_type: str
    priority_score: float

@dataclass
class VisualConcept:
    """Represents a visual concept extracted from script"""
    concept: str
    category: str  # person, place, object, action, emotion, etc.
    importance: float
    context: str
    related_concepts: List[str]

class ScriptAnalyzer:
    """Advanced analyzer for video scripts to extract visual concepts"""
    
    def __init__(self):
        self.visual_keywords = {
            'person': ['person', 'man', 'woman', 'child', 'people', 'character', 'hero', 'villain', 'protagonist',
                      '<PERSON><PERSON><PERSON>', 'cel <PERSON>', 'soltăr', 'arca<PERSON>i', 'cavaleri', 'oaste', 'TikToker', 'rege'],
            'place': ['mountain', 'forest', 'city', 'ocean', 'desert', 'castle', 'house', 'building', 'landscape', 'scenery',
                     'Moldova', 'Neamț', 'Războieni', 'Podul Înalt', 'cort', 'câmpie', 'munte', 'fântână', 'canalizare'],
            'object': ['car', 'sword', 'book', 'tree', 'flower', 'stone', 'bridge', 'tower', 'ship', 'weapon',
                      'sabie', 'topoare', 'săgeți', 'coroană', 'diamante', 'borș', 'oală', 'toaletă', 'wc', 'capac'],
            'action': ['running', 'flying', 'fighting', 'dancing', 'walking', 'jumping', 'swimming', 'climbing',
                      'bătălie', 'luptă', 'atacă', 'apără', 'fuge', 'sare', 'lovește', 'trântește', 'zbierând'],
            'emotion': ['happy', 'sad', 'angry', 'peaceful', 'dramatic', 'mysterious', 'romantic', 'epic',
                       'speriat', 'tresări', 'răcni', 'pierdut', 'decisiv', 'neabătut', 'triumfător'],
            'weather': ['sunny', 'rainy', 'stormy', 'cloudy', 'foggy', 'snowy', 'windy'],
            'time': ['morning', 'afternoon', 'evening', 'night', 'dawn', 'dusk', 'sunset', 'sunrise',
                    'anul 1476', 'medieval', 'istoric', 'noaptea', 'dimineața'],
            'style': ['cinematic', 'realistic', 'fantasy', 'sci-fi', 'vintage', 'modern', 'artistic']
        }
        
        self.emotional_indicators = {
            'dramatic': ['dramatic', 'intense', 'powerful', 'epic', 'climax', 'tension'],
            'peaceful': ['peaceful', 'calm', 'serene', 'tranquil', 'gentle', 'quiet'],
            'mysterious': ['mysterious', 'dark', 'hidden', 'secret', 'unknown', 'shadow'],
            'romantic': ['romantic', 'love', 'beautiful', 'elegant', 'graceful', 'tender'],
            'action': ['action', 'fast', 'dynamic', 'explosive', 'energetic', 'intense'],
            'melancholic': ['sad', 'melancholic', 'lonely', 'nostalgic', 'somber', 'reflective']
        }
        
        self.scene_types = {
            'landscape': ['landscape', 'scenery', 'view', 'panorama', 'vista', 'horizon'],
            'portrait': ['person', 'character', 'face', 'portrait', 'close-up'],
            'action': ['action', 'movement', 'dynamic', 'chase', 'fight', 'race'],
            'interior': ['inside', 'room', 'house', 'building', 'indoor'],
            'exterior': ['outside', 'outdoor', 'landscape', 'street', 'field']
        }
        
        # Initialize NLTK components
        self._init_nltk()
    
    def _init_nltk(self):
        """Initialize NLTK components with error handling"""
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            try:
                nltk.download('punkt', quiet=True)
            except:
                logger.warning("Could not download NLTK punkt tokenizer")
        
        try:
            nltk.data.find('corpora/stopwords')
        except LookupError:
            try:
                nltk.download('stopwords', quiet=True)
            except:
                logger.warning("Could not download NLTK stopwords")
        
        try:
            nltk.data.find('taggers/averaged_perceptron_tagger')
        except LookupError:
            try:
                nltk.download('averaged_perceptron_tagger', quiet=True)
            except:
                logger.warning("Could not download NLTK POS tagger")
    
    def analyze_script(self, script: str, audio_duration: float = 30.0) -> List[ScriptSegment]:
        """Analyze script and create segments with visual concepts"""
        try:
            # Clean and prepare script
            clean_script = self._clean_script(script)
            
            # Split into sentences
            sentences = self._split_into_sentences(clean_script)
            
            # Create segments based on audio duration
            segments = self._create_segments(sentences, audio_duration)
            
            # Analyze each segment
            analyzed_segments = []
            for segment in segments:
                visual_concepts = self._extract_visual_concepts(segment['text'])
                emotional_tone = self._determine_emotional_tone(segment['text'])
                scene_type = self._determine_scene_type(segment['text'])
                priority_score = self._calculate_priority_score(segment['text'], visual_concepts)
                
                analyzed_segment = ScriptSegment(
                    text=segment['text'],
                    start_time=segment['start_time'],
                    end_time=segment['end_time'],
                    duration=segment['duration'],
                    visual_concepts=visual_concepts,
                    emotional_tone=emotional_tone,
                    scene_type=scene_type,
                    priority_score=priority_score
                )
                analyzed_segments.append(analyzed_segment)
            
            logger.info(f"Analyzed script into {len(analyzed_segments)} segments")
            return analyzed_segments
            
        except Exception as e:
            logger.error(f"Script analysis failed: {e}")
            # Return fallback segment
            return [ScriptSegment(
                text=script,
                start_time=0.0,
                end_time=audio_duration,
                duration=audio_duration,
                visual_concepts=['abstract', 'concept'],
                emotional_tone='neutral',
                scene_type='abstract',
                priority_score=0.5
            )]
    
    def _clean_script(self, script: str) -> str:
        """Clean script text for analysis"""
        # Remove extra whitespace
        script = re.sub(r'\s+', ' ', script.strip())
        
        # Remove special characters but keep punctuation
        script = re.sub(r'[^\w\s.,!?;:-]', '', script)
        
        return script
    
    def _split_into_sentences(self, script: str) -> List[str]:
        """Split script into sentences"""
        try:
            sentences = sent_tokenize(script)
            return [s.strip() for s in sentences if s.strip()]
        except:
            # Fallback to simple splitting
            sentences = re.split(r'[.!?]+', script)
            return [s.strip() for s in sentences if s.strip()]
    
    def _create_segments(self, sentences: List[str], audio_duration: float) -> List[Dict]:
        """Create time-based segments from sentences"""
        if not sentences:
            return []
        
        segments = []
        total_chars = sum(len(s) for s in sentences)
        current_time = 0.0
        
        for sentence in sentences:
            # Calculate duration based on character count
            char_ratio = len(sentence) / total_chars if total_chars > 0 else 1.0 / len(sentences)
            duration = audio_duration * char_ratio
            
            segments.append({
                'text': sentence,
                'start_time': current_time,
                'end_time': current_time + duration,
                'duration': duration
            })
            
            current_time += duration
        
        return segments
    
    def _extract_visual_concepts(self, text: str) -> List[str]:
        """Extract visual concepts from text"""
        concepts = []
        text_lower = text.lower()
        
        # Extract concepts by category
        for category, keywords in self.visual_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    concepts.append(keyword)
        
        # Extract nouns using POS tagging
        try:
            words = word_tokenize(text)
            pos_tags = pos_tag(words)
            nouns = [word.lower() for word, pos in pos_tags if pos.startswith('NN')]
            concepts.extend(nouns[:3])  # Add top 3 nouns
        except:
            # Fallback: extract potential nouns with simple rules
            words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
            concepts.extend(words[:5])
        
        # Remove duplicates and common words
        concepts = list(set(concepts))
        concepts = [c for c in concepts if len(c) > 2 and c not in ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use']]
        
        return concepts[:5]  # Return top 5 concepts
    
    def _determine_emotional_tone(self, text: str) -> str:
        """Determine emotional tone of text"""
        text_lower = text.lower()
        tone_scores = {}
        
        for tone, indicators in self.emotional_indicators.items():
            score = sum(1 for indicator in indicators if indicator in text_lower)
            if score > 0:
                tone_scores[tone] = score
        
        if tone_scores:
            return max(tone_scores, key=tone_scores.get)
        
        return 'neutral'
    
    def _determine_scene_type(self, text: str) -> str:
        """Determine scene type from text"""
        text_lower = text.lower()
        scene_scores = {}
        
        for scene_type, indicators in self.scene_types.items():
            score = sum(1 for indicator in indicators if indicator in text_lower)
            if score > 0:
                scene_scores[scene_type] = score
        
        if scene_scores:
            return max(scene_scores, key=scene_scores.get)
        
        return 'abstract'
    
    def _calculate_priority_score(self, text: str, visual_concepts: List[str]) -> float:
        """Calculate priority score for segment"""
        score = 0.0
        
        # Base score from text length
        score += min(len(text) / 100, 1.0) * 0.3
        
        # Score from visual concepts
        score += len(visual_concepts) / 10 * 0.4
        
        # Score from emotional indicators
        text_lower = text.lower()
        emotional_words = sum(1 for indicators in self.emotional_indicators.values() 
                            for indicator in indicators if indicator in text_lower)
        score += min(emotional_words / 5, 1.0) * 0.3
        
        return min(score, 1.0)
    
    def extract_key_themes(self, segments: List[ScriptSegment]) -> List[str]:
        """Extract key themes from all segments"""
        all_concepts = []
        for segment in segments:
            all_concepts.extend(segment.visual_concepts)
        
        # Count concept frequency
        concept_counts = Counter(all_concepts)
        
        # Return most common themes
        return [concept for concept, count in concept_counts.most_common(10)]
    
    def get_segment_for_time(self, segments: List[ScriptSegment], time: float) -> Optional[ScriptSegment]:
        """Get segment that contains the given time"""
        for segment in segments:
            if segment.start_time <= time <= segment.end_time:
                return segment
        return None

    def generate_contextual_image_prompt(self, segment: ScriptSegment, style: str = "realistic") -> str:
        """Generate AI image prompt based on script segment context with improved content analysis"""
        prompt_parts = []

        # Enhanced content analysis - extract key concepts from the actual text
        key_concepts = self._extract_key_concepts_from_text(segment.text)

        # Extract main subjects from visual concepts
        characters = [c for c in segment.visual_concepts if c in self.visual_keywords['person']]
        locations = [c for c in segment.visual_concepts if c in self.visual_keywords['place']]
        objects = [c for c in segment.visual_concepts if c in self.visual_keywords['object']]
        actions = [c for c in segment.visual_concepts if c in self.visual_keywords['action']]

        # Build contextual prompt based on actual content
        main_subject = self._determine_main_subject(segment.text, key_concepts, characters, locations, objects)

        if main_subject:
            prompt_parts.append(main_subject)

        # Add scene-specific context based on content analysis
        scene_context = self._generate_scene_context(segment, key_concepts, actions)
        if scene_context:
            prompt_parts.append(scene_context)

        # Add location context if available
        if locations:
            location_context = self._generate_location_context(locations[0], segment.text)
            prompt_parts.append(location_context)

        # Add emotional tone with content-aware modifiers
        tone_modifier = self._get_content_aware_tone_modifier(segment.emotional_tone, segment.text)
        if tone_modifier:
            prompt_parts.append(tone_modifier)

        # Add style modifiers
        style_modifiers = {
            'realistic': 'photorealistic, high detail, professional photography',
            'cinematic': 'cinematic composition, film lighting, movie scene',
            'artistic': 'artistic style, painterly, creative composition',
            'historical': 'historically accurate, period appropriate, authentic details',
            'fantasy': 'fantasy art style, magical atmosphere, enchanted',
            'cartoon': 'cartoon style, animated, colorful, stylized illustration'
        }

        if style in style_modifiers:
            prompt_parts.append(style_modifiers[style])

        # Combine into coherent prompt
        prompt = ", ".join(filter(None, prompt_parts))

        # Add quality enhancers
        prompt += ", high quality, detailed, professional composition"

        return prompt

    def _extract_key_concepts_from_text(self, text: str) -> List[str]:
        """Extract key concepts from the actual text content"""
        import re

        # Convert to lowercase for analysis
        text_lower = text.lower()

        # Extract nouns and important concepts
        key_concepts = []

        # Look for specific topics and subjects
        concept_patterns = {
            'education': ['school', 'learning', 'education', 'student', 'teacher', 'knowledge', 'study'],
            'technology': ['computer', 'internet', 'digital', 'technology', 'innovation', 'software'],
            'nature': ['nature', 'environment', 'forest', 'ocean', 'mountain', 'wildlife', 'ecosystem'],
            'science': ['science', 'research', 'experiment', 'discovery', 'laboratory', 'theory'],
            'history': ['history', 'ancient', 'medieval', 'past', 'civilization', 'empire', 'war'],
            'art': ['art', 'painting', 'sculpture', 'creative', 'artistic', 'design', 'beauty'],
            'business': ['business', 'company', 'market', 'economy', 'finance', 'industry'],
            'health': ['health', 'medical', 'doctor', 'medicine', 'wellness', 'fitness'],
            'travel': ['travel', 'journey', 'destination', 'tourism', 'adventure', 'exploration'],
            'food': ['food', 'cooking', 'recipe', 'cuisine', 'restaurant', 'meal', 'nutrition']
        }

        # Find matching concepts
        for category, keywords in concept_patterns.items():
            for keyword in keywords:
                if keyword in text_lower:
                    key_concepts.append(category)
                    break

        # Extract proper nouns (capitalized words)
        proper_nouns = re.findall(r'\b[A-Z][a-z]+\b', text)
        key_concepts.extend(proper_nouns[:3])  # Limit to first 3 proper nouns

        return list(set(key_concepts))  # Remove duplicates

    def _determine_main_subject(self, text: str, key_concepts: List[str],
                              characters: List[str], locations: List[str], objects: List[str]) -> str:
        """Determine the main subject for the image based on content analysis"""

        # Priority 1: Specific characters mentioned
        if characters:
            return characters[0]

        # Priority 2: Key concepts from text analysis
        if key_concepts:
            concept_subjects = {
                'education': 'students in classroom',
                'technology': 'modern technology setup',
                'nature': 'natural landscape',
                'science': 'scientific laboratory',
                'history': 'historical scene',
                'art': 'artistic creation',
                'business': 'professional business environment',
                'health': 'healthcare setting',
                'travel': 'travel destination',
                'food': 'culinary scene'
            }

            for concept in key_concepts:
                if concept in concept_subjects:
                    return concept_subjects[concept]

        # Priority 3: Locations
        if locations:
            return f"scene in {locations[0]}"

        # Priority 4: Objects
        if objects:
            return f"focus on {objects[0]}"

        # Fallback: Extract main topic from text
        words = text.lower().split()
        if len(words) > 5:
            # Use the most frequent meaningful words
            meaningful_words = [w for w in words if len(w) > 3 and w not in ['este', 'sunt', 'pentru', 'acest', 'această']]
            if meaningful_words:
                return f"concept of {meaningful_words[0]}"

        return "abstract concept illustration"

    def _generate_scene_context(self, segment: ScriptSegment, key_concepts: List[str], actions: List[str]) -> str:
        """Generate scene context based on content and scene type"""

        scene_contexts = {
            'dialogue': 'conversation scene, character interaction',
            'battle': 'conflict scene, dramatic action',
            'landscape': 'scenic environment, atmospheric view',
            'action': 'dynamic movement, energetic scene',
            'abstract': 'conceptual representation, symbolic imagery'
        }

        base_context = scene_contexts.get(segment.scene_type, 'general scene')

        # Add action context if available
        if actions:
            base_context += f", {actions[0]} activity"

        # Add concept-specific context
        if 'education' in key_concepts:
            base_context += ", educational environment"
        elif 'technology' in key_concepts:
            base_context += ", technological setting"
        elif 'nature' in key_concepts:
            base_context += ", natural environment"

        return base_context

    def _generate_location_context(self, location: str, text: str) -> str:
        """Generate location-specific context"""

        # Analyze text for location descriptors
        text_lower = text.lower()

        location_descriptors = {
            'indoor': ['room', 'house', 'building', 'office', 'classroom'],
            'outdoor': ['outside', 'park', 'street', 'garden', 'field'],
            'urban': ['city', 'town', 'urban', 'metropolitan'],
            'rural': ['village', 'countryside', 'rural', 'farm'],
            'natural': ['forest', 'mountain', 'beach', 'lake', 'river']
        }

        for context_type, keywords in location_descriptors.items():
            if any(keyword in text_lower for keyword in keywords):
                return f"{context_type} {location} setting"

        return f"in {location}"

    def _get_content_aware_tone_modifier(self, emotional_tone: str, text: str) -> str:
        """Get tone modifier based on content analysis"""

        tone_modifiers = {
            'dramatic': 'dramatic atmosphere, intense mood',
            'epic': 'epic cinematic style, heroic composition',
            'mysterious': 'mysterious atmosphere, moody lighting',
            'peaceful': 'peaceful scene, soft lighting',
            'action': 'dynamic action scene, motion blur',
            'romantic': 'romantic mood, warm lighting',
            'educational': 'clear, informative, well-lit',
            'professional': 'professional setting, clean composition'
        }

        # Analyze text for additional tone indicators
        text_lower = text.lower()

        if any(word in text_lower for word in ['important', 'crucial', 'significant']):
            return tone_modifiers.get(emotional_tone, '') + ', important subject matter'
        elif any(word in text_lower for word in ['beautiful', 'amazing', 'wonderful']):
            return tone_modifiers.get(emotional_tone, '') + ', beautiful composition'
        elif any(word in text_lower for word in ['complex', 'detailed', 'intricate']):
            return tone_modifiers.get(emotional_tone, '') + ', detailed complexity'

        return tone_modifiers.get(emotional_tone, 'balanced lighting, clear composition')

    def get_high_priority_segments(self, segments: List[ScriptSegment],
                                 max_segments: int = 10) -> List[ScriptSegment]:
        """Get segments with highest priority for image generation"""
        # Sort by priority score
        sorted_segments = sorted(segments, key=lambda x: x.priority_score, reverse=True)

        # Filter out segments that are too close together (minimum 5 seconds apart)
        filtered_segments = []
        last_time = -10.0

        for segment in sorted_segments:
            if segment.start_time - last_time >= 5.0:  # 5 second minimum gap
                filtered_segments.append(segment)
                last_time = segment.start_time

                if len(filtered_segments) >= max_segments:
                    break

        return filtered_segments

    def analyze_for_image_generation(self, script: str, audio_duration: float = 30.0,
                                   style: str = "realistic") -> List[Dict[str, Any]]:
        """Analyze script specifically for contextual image generation"""
        # Get script segments
        segments = self.analyze_script(script, audio_duration)

        # Get high priority segments for image generation
        image_segments = self.get_high_priority_segments(segments)

        # Generate image prompts for each segment
        image_data = []
        for segment in image_segments:
            prompt = self.generate_contextual_image_prompt(segment, style)

            image_data.append({
                'start_time': segment.start_time,
                'end_time': segment.end_time,
                'duration': segment.duration,
                'text': segment.text,
                'prompt': prompt,
                'priority': segment.priority_score,
                'scene_type': segment.scene_type,
                'emotional_tone': segment.emotional_tone,
                'visual_concepts': segment.visual_concepts
            })

        logger.info(f"Generated {len(image_data)} contextual image prompts")
        return image_data
