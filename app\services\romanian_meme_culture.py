"""
Romanian Meme Culture Enhancement System
Advanced text generation with cultural references and slang
"""

import random
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json
import os

logger = logging.getLogger(__name__)

@dataclass
class MemeTemplate:
    """Romanian meme template structure"""
    name: str
    format_string: str
    variables: List[str]
    context: str
    popularity: int  # 1-10 scale

class RomanianSlangGenerator:
    """Generates authentic Romanian slang and expressions"""
    
    def __init__(self):
        self.greetings = [
            "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>mule"
        ]
        
        self.expressions = [
            "ce faci mă?", "ce mai zici?", "cum merge?", "ce mai faci?",
            "salut băi", "hai noroc", "pa pa", "ne vedem"
        ]
        
        self.reactions = [
            "nu înțeleg nimic", "ce dracu'?", "serios?", "nu pot să cred",
            "asta e tare", "ce nebunie", "incredibil", "wow frate",
            "nu se poate", "asta e România", "clasic românesc"
        ]
        
        self.intensifiers = [
            "foarte", "super", "mega", "ultra", "extrem de", "incredibil de",
            "nebun de", "groaznic de", "teribil de", "fantastic de"
        ]
        
        self.situations = [
            "când ești la muncă", "când ești acasă", "când ești în trafic",
            "când ești la școală", "când ești cu prietenii", "când ești singur",
            "când ești la magazin", "când ești în vacanță", "când ești obosit",
            "când ești fericit", "când ești trist", "când ești nervos"
        ]
    
    def get_random_greeting(self) -> str:
        return random.choice(self.greetings)
    
    def get_random_expression(self) -> str:
        return random.choice(self.expressions)
    
    def get_random_reaction(self) -> str:
        return random.choice(self.reactions)
    
    def get_random_intensifier(self) -> str:
        return random.choice(self.intensifiers)
    
    def get_random_situation(self) -> str:
        return random.choice(self.situations)

class RomanianMemeTemplates:
    """Collection of Romanian meme templates and formats"""
    
    def __init__(self):
        self.templates = [
            MemeTemplate(
                name="confusion_classic",
                format_string="{greeting}, da' {situation}... {reaction}",
                variables=["greeting", "situation", "reaction"],
                context="expressing confusion about a situation",
                popularity=9
            ),
            MemeTemplate(
                name="realization_moment",
                format_string="Când realizezi că {realization}: '{reaction}'",
                variables=["realization", "reaction"],
                context="moment of realization",
                popularity=8
            ),
            MemeTemplate(
                name="mama_reaction",
                format_string="Mama când {situation}: 'Ăsta e copilul meu?'",
                variables=["situation"],
                context="mother's reaction to something",
                popularity=9
            ),
            MemeTemplate(
                name="romanian_success",
                format_string="Când reușești să {achievement} în România: '{reaction}'",
                variables=["achievement", "reaction"],
                context="achieving something difficult in Romania",
                popularity=7
            ),
            MemeTemplate(
                name="friend_betrayal",
                format_string="{greeting} când prietenul tău {betrayal}: '{reaction}'",
                variables=["greeting", "betrayal", "reaction"],
                context="friend doing something unexpected",
                popularity=8
            ),
            MemeTemplate(
                name="school_struggle",
                format_string="Profesorul: '{question}'\nEu: '{confused_reaction}'",
                variables=["question", "confused_reaction"],
                context="school/education struggles",
                popularity=8
            ),
            MemeTemplate(
                name="work_life",
                format_string="Șeful când {work_situation}: '{boss_reaction}'\nEu: '{employee_reaction}'",
                variables=["work_situation", "boss_reaction", "employee_reaction"],
                context="workplace situations",
                popularity=7
            ),
            MemeTemplate(
                name="technology_struggle",
                format_string="Când {tech_problem}: '{frustrated_reaction}'",
                variables=["tech_problem", "frustrated_reaction"],
                context="technology problems",
                popularity=6
            ),
            MemeTemplate(
                name="food_reaction",
                format_string="Când mama face {food}: '{happy_reaction}'",
                variables=["food", "happy_reaction"],
                context="food and family",
                popularity=8
            ),
            MemeTemplate(
                name="weather_complaint",
                format_string="Vremea în România: {weather_description}\nNoi: '{weather_reaction}'",
                variables=["weather_description", "weather_reaction"],
                context="complaining about weather",
                popularity=7
            )
        ]
        
        self.cultural_references = {
            "achievements": [
                "găsești parcare în centru", "prinzi autobuzul", "nu stai la coadă",
                "găsești bilet la concert", "nu pici la examen", "găsești job bun",
                "nu te îmbolnăvești iarna", "găsești apartament ieftin"
            ],
            "betrayals": [
                "nu-ți dă restul", "îți ia ultima felie de pizza", "nu te invită la petrecere",
                "îți spune spoilere", "nu-ți răspunde la mesaje", "îți împrumută bani și uită"
            ],
            "work_situations": [
                "lucrezi overtime fără plată", "vin controalele", "se strică calculatorul",
                "ai deadline în 5 minute", "șeful e în concediu", "nu merge internetul"
            ],
            "tech_problems": [
                "nu merge WiFi-ul", "se blochează calculatorul", "se descarcă telefonul",
                "nu merge aplicația", "se pierde conexiunea", "nu se salvează documentul"
            ],
            "foods": [
                "mici", "ciorbă de burtă", "papanași", "mămăligă", "sarmale",
                "cozonac", "plăcintă", "gogoși", "clătite", "salată de icre"
            ],
            "weather_descriptions": [
                "40 de grade vara, -20 iarna", "ploaie 6 luni pe an",
                "vânt ca în stepă", "zăpadă în aprilie", "caniculă în septembrie"
            ]
        }
    
    def get_random_template(self, popularity_threshold: int = 5) -> MemeTemplate:
        """Get random template above popularity threshold"""
        suitable_templates = [t for t in self.templates if t.popularity >= popularity_threshold]
        return random.choice(suitable_templates)
    
    def get_cultural_reference(self, category: str) -> str:
        """Get random cultural reference from category"""
        return random.choice(self.cultural_references.get(category, ["ceva românesc"]))

class RomanianMemeGenerator:
    """Advanced Romanian meme text generator"""
    
    def __init__(self):
        self.slang = RomanianSlangGenerator()
        self.templates = RomanianMemeTemplates()
        self.chaos_modifiers = {
            1: {"intensity": "calm", "style": "normal"},
            2: {"intensity": "mild", "style": "slightly confused"},
            3: {"intensity": "moderate", "style": "confused"},
            4: {"intensity": "elevated", "style": "very confused"},
            5: {"intensity": "high", "style": "chaotic"},
            6: {"intensity": "very high", "style": "very chaotic"},
            7: {"intensity": "extreme", "style": "extremely chaotic"},
            8: {"intensity": "maximum", "style": "completely chaotic"},
            9: {"intensity": "beyond maximum", "style": "fever dream"},
            10: {"intensity": "transcendent", "style": "incomprehensible"}
        }
    
    def generate_caption(self, context: str = "", chaos_level: int = 5, 
                        custom_text: str = "", theme: str = "general") -> str:
        """Generate Romanian meme caption"""
        try:
            # Use custom text if provided
            if custom_text:
                return self._enhance_custom_text(custom_text, chaos_level)
            
            # Select template based on theme and chaos level
            template = self._select_template(theme, chaos_level)
            
            # Fill template variables
            filled_template = self._fill_template(template, context, chaos_level)
            
            # Apply chaos modifications
            final_caption = self._apply_chaos_modifications(filled_template, chaos_level)
            
            return final_caption
            
        except Exception as e:
            logger.error(f"Error generating Romanian caption: {e}")
            return self._fallback_caption(chaos_level)
    
    def _enhance_custom_text(self, custom_text: str, chaos_level: int) -> str:
        """Enhance custom text with Romanian flair"""
        enhanced = custom_text
        
        # Add Romanian greeting
        if chaos_level >= 3:
            greeting = self.slang.get_random_greeting()
            enhanced = f"{greeting}, {enhanced}"
        
        # Add reaction based on chaos level
        if chaos_level >= 5:
            reaction = self.slang.get_random_reaction()
            enhanced = f"{enhanced}... {reaction}!"
        
        # Add intensifier for high chaos
        if chaos_level >= 7:
            intensifier = self.slang.get_random_intensifier()
            enhanced = f"{enhanced} ({intensifier} haotic!)"
        
        return enhanced
    
    def _select_template(self, theme: str, chaos_level: int) -> MemeTemplate:
        """Select appropriate template based on theme and chaos"""
        # For high chaos, prefer more popular/recognizable templates
        popularity_threshold = max(1, 8 - chaos_level)
        
        # Theme-specific template selection
        theme_preferences = {
            "school": ["school_struggle", "mama_reaction"],
            "work": ["work_life", "romanian_success"],
            "tech": ["technology_struggle", "confusion_classic"],
            "food": ["food_reaction", "mama_reaction"],
            "general": None  # No preference, use any
        }
        
        preferred_names = theme_preferences.get(theme)
        if preferred_names:
            preferred_templates = [t for t in self.templates.templates 
                                 if t.name in preferred_names and t.popularity >= popularity_threshold]
            if preferred_templates:
                return random.choice(preferred_templates)
        
        # Fallback to any suitable template
        return self.templates.get_random_template(popularity_threshold)
    
    def _fill_template(self, template: MemeTemplate, context: str, chaos_level: int) -> str:
        """Fill template with appropriate content"""
        variables = {}
        
        for var in template.variables:
            if var == "greeting":
                variables[var] = self.slang.get_random_greeting()
            elif var == "situation":
                variables[var] = context or self.slang.get_random_situation()
            elif var == "reaction":
                variables[var] = self.slang.get_random_reaction()
            elif var == "realization":
                variables[var] = context or "viața e grea în România"
            elif var == "achievement":
                variables[var] = self.templates.get_cultural_reference("achievements")
            elif var == "betrayal":
                variables[var] = self.templates.get_cultural_reference("betrayals")
            elif var == "work_situation":
                variables[var] = self.templates.get_cultural_reference("work_situations")
            elif var == "tech_problem":
                variables[var] = self.templates.get_cultural_reference("tech_problems")
            elif var == "food":
                variables[var] = self.templates.get_cultural_reference("foods")
            elif var == "weather_description":
                variables[var] = self.templates.get_cultural_reference("weather_descriptions")
            else:
                # Generic fallback
                variables[var] = self.slang.get_random_reaction()
        
        try:
            return template.format_string.format(**variables)
        except KeyError as e:
            logger.warning(f"Missing variable {e} in template, using fallback")
            return self._fallback_caption(chaos_level)
    
    def _apply_chaos_modifications(self, text: str, chaos_level: int) -> str:
        """Apply chaos-level modifications to text"""
        modified = text
        
        # Add repetition for high chaos
        if chaos_level >= 8:
            words = modified.split()
            if words:
                repeated_word = random.choice(words[-3:])  # Repeat one of last 3 words
                modified = f"{modified} {repeated_word} {repeated_word}!"
        
        # Add extra punctuation
        if chaos_level >= 6:
            modified = modified.replace("!", "!!!")
            modified = modified.replace("?", "???")
        
        # Add chaos indicators
        if chaos_level >= 9:
            chaos_indicators = ["🤯", "😵", "🔥", "💀", "🚀", "⚡"]
            indicator = random.choice(chaos_indicators)
            modified = f"{modified} {indicator}"
        
        return modified
    
    def _fallback_caption(self, chaos_level: int) -> str:
        """Fallback caption when generation fails"""
        greeting = self.slang.get_random_greeting()
        reaction = self.slang.get_random_reaction()
        
        if chaos_level >= 7:
            return f"{greeting}, {reaction}!!! 🤯"
        elif chaos_level >= 4:
            return f"{greeting}, {reaction}!"
        else:
            return f"{greeting}, {reaction}"
    
    def generate_batch_captions(self, count: int, theme: str = "general", 
                               chaos_level: int = 5) -> List[str]:
        """Generate multiple captions for variety"""
        captions = []
        for _ in range(count):
            caption = self.generate_caption(theme=theme, chaos_level=chaos_level)
            captions.append(caption)
        return captions
    
    def get_theme_suggestions(self) -> Dict[str, List[str]]:
        """Get suggestions for different themes"""
        return {
            "school": ["examen", "profesor", "notă", "vacanță"],
            "work": ["șef", "salariu", "overtime", "concediu"],
            "tech": ["internet", "calculator", "telefon", "aplicație"],
            "food": ["mâncare", "restaurant", "gătit", "foame"],
            "weather": ["vreme", "ploaie", "soare", "frig"],
            "general": ["viață", "prieteni", "familie", "România"]
        }
