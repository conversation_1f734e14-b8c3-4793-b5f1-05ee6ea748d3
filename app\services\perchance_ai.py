"""
Perchance AI Image Generator Integration
Provides AI image generation using the Perchance platform
"""

import asyncio
import aiohttp
import logging
import time
import hashlib
import json
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from io import BytesIO
import base64

logger = logging.getLogger(__name__)

@dataclass
class PerchanceConfig:
    """Configuration for Perchance AI generation"""
    width: int = 512
    height: int = 512
    style: str = "realistic"
    quality: str = "standard"  # standard, high
    negative_prompt: str = ""
    steps: int = 20
    guidance_scale: float = 7.5

class PerchanceRateLimiter:
    """Rate limiter for Perchance API calls"""
    
    def __init__(self, max_requests_per_minute: int = 30):
        self.max_requests = max_requests_per_minute
        self.requests = []
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire permission to make a request"""
        async with self.lock:
            now = time.time()
            # Remove requests older than 1 minute
            self.requests = [req_time for req_time in self.requests if now - req_time < 60]
            
            if len(self.requests) >= self.max_requests:
                # Calculate wait time
                oldest_request = min(self.requests)
                wait_time = 60 - (now - oldest_request)
                if wait_time > 0:
                    logger.info(f"Rate limit reached, waiting {wait_time:.1f} seconds")
                    await asyncio.sleep(wait_time)
            
            self.requests.append(now)

class PerchanceImageGenerator:
    """Perchance AI image generator using unofficial API"""
    
    def __init__(self):
        self.base_url = "https://perchance.org"
        self.rate_limiter = PerchanceRateLimiter()
        self.session = None
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=120)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={"User-Agent": self.user_agent}
            )
        return self.session
    
    async def _get_generator_page(self) -> Optional[str]:
        """Get the Perchance AI image generator page"""
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/ai-text-to-image-generator") as response:
                if response.status == 200:
                    return await response.text()
                else:
                    logger.error(f"Failed to get generator page: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error getting generator page: {e}")
            return None
    
    async def _extract_session_info(self, page_content: str) -> Dict[str, Any]:
        """Extract session information from the page"""
        try:
            # Look for session data in the page
            # This is a simplified approach - in practice, you'd need to parse the actual page structure
            session_info = {
                "csrf_token": None,
                "session_id": None,
                "generator_id": "ai-text-to-image-generator"
            }
            
            # Extract CSRF token if present
            if 'csrf' in page_content.lower():
                # Simple regex to find token patterns
                import re
                token_match = re.search(r'csrf["\']?\s*[:=]\s*["\']([^"\']+)["\']', page_content, re.IGNORECASE)
                if token_match:
                    session_info["csrf_token"] = token_match.group(1)
            
            return session_info
        except Exception as e:
            logger.error(f"Error extracting session info: {e}")
            return {}
    
    async def generate_image(self, prompt: str, width: int = 1024, height: int = 1024) -> Optional[bytes]:
        """Generate image using Perchance text-to-image plugin"""
        try:
            await self.rate_limiter.acquire()

            # Map dimensions to Perchance supported resolutions
            resolution = self._get_perchance_resolution(width, height)

            # Create a temporary Perchance generator page
            generator_html = self._create_generator_html(prompt, resolution)

            # Submit the generator and get the image
            image_data = await self._submit_generator_and_get_image(generator_html, prompt)

            if image_data:
                logger.info(f"Successfully generated image with Perchance: {len(image_data)} bytes")
                return image_data
            else:
                # Fallback to placeholder if real generation fails
                return await self._generate_placeholder_image(prompt, width, height)

        except Exception as e:
            logger.error(f"Perchance generation error: {e}")
            # Fallback to placeholder
            return await self._generate_placeholder_image(prompt, width, height)

    def _get_perchance_resolution(self, width: int, height: int) -> str:
        """Map requested dimensions to supported Perchance resolutions"""
        # Perchance supports: 512x512, 512x768, 768x512
        aspect_ratio = width / height

        if abs(aspect_ratio - 1.0) < 0.1:  # Square-ish
            return "512x512"
        elif aspect_ratio < 1.0:  # Portrait
            return "512x768"
        else:  # Landscape
            return "768x512"

    def _create_generator_html(self, prompt: str, resolution: str) -> str:
        """Create HTML for a Perchance generator with text-to-image plugin"""
        # Escape prompt for safe HTML inclusion
        safe_prompt = prompt.replace('"', '&quot;').replace('<', '&lt;').replace('>', '&gt;')

        html_template = f"""
<!DOCTYPE html>
<html>
<head>
    <title>AI Image Generator</title>
    <script src="https://perchance.org/perchance.js"></script>
</head>
<body>
    <div id="output"></div>

    <script>
        // Import the text-to-image plugin
        const image = perchance.importPlugin('text-to-image-plugin');

        // Configuration
        const promptData = {{
            prompt: "{safe_prompt}",
            resolution: "{resolution}",
            guidanceScale: 7.5,
            negativePrompt: "blurry, low quality, distorted, deformed"
        }};

        // Generate image when page loads
        window.addEventListener('load', async function() {{
            try {{
                const result = await image(promptData);
                document.getElementById('output').appendChild(result.canvas);

                // Signal completion
                window.imageGenerated = true;
                window.imageData = result.dataUrl;
            }} catch (error) {{
                console.error('Image generation failed:', error);
                window.imageGenerated = false;
                window.imageError = error.message;
            }}
        }});
    </script>
</body>
</html>
        """
        return html_template.strip()

    async def _submit_generator_and_get_image(self, html_content: str, prompt: str) -> Optional[bytes]:
        """Submit request to Perchance text-to-image API with multiple fallback methods"""

        # Try multiple approaches for Perchance integration
        methods = [
            self._try_direct_api,
            self._try_public_generator,
            self._try_alternative_endpoint
        ]

        for method in methods:
            try:
                result = await method(prompt)
                if result:
                    return result
            except Exception as e:
                logger.debug(f"Method {method.__name__} failed: {e}")
                continue

        logger.warning("All Perchance methods failed, using placeholder")
        return None

    async def _try_direct_api(self, prompt: str) -> Optional[bytes]:
        """Try the direct Perchance API endpoint"""
        session = await self._get_session()

        # Multiple possible API endpoints
        api_endpoints = [
            "https://image-generation.perchance.org/api/generate",
            "https://perchance.org/api/ai-text-to-image",
            "https://ai-text-to-image-generator.perchance.org/api/generate"
        ]

        request_data = {
            "prompt": prompt,
            "resolution": self._get_perchance_resolution(1024, 1024),
            "guidanceScale": 7.5,
            "negativePrompt": "blurry, low quality, distorted, deformed, ugly",
            "seed": -1
        }

        for api_url in api_endpoints:
            try:
                async with session.post(
                    api_url,
                    json=request_data,
                    headers={
                        "Content-Type": "application/json",
                        "Referer": "https://perchance.org/ai-text-to-image-generator",
                        "Origin": "https://perchance.org",
                        "User-Agent": self.user_agent
                    }
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return await self._extract_image_from_response(result)

            except Exception as e:
                logger.debug(f"API endpoint {api_url} failed: {e}")
                continue

        return None

    async def _try_public_generator(self, prompt: str) -> Optional[bytes]:
        """Try using a public Perchance generator"""
        try:
            session = await self._get_session()

            # Use the public AI text-to-image generator
            generator_url = "https://perchance.org/ai-text-to-image-generator"

            # First, get the generator page to extract any necessary tokens
            async with session.get(generator_url) as response:
                if response.status == 200:
                    page_content = await response.text()

                    # Look for any API endpoints or tokens in the page
                    # This is a simplified approach
                    api_match = re.search(r'api["\']?\s*:\s*["\']([^"\']+)["\']', page_content)
                    if api_match:
                        api_endpoint = api_match.group(1)
                        return await self._call_extracted_api(api_endpoint, prompt)

            return None

        except Exception as e:
            logger.debug(f"Public generator method failed: {e}")
            return None

    async def _try_alternative_endpoint(self, prompt: str) -> Optional[bytes]:
        """Try alternative endpoints or methods"""
        try:
            # This could include other free AI image generation services
            # For now, return None to fall back to placeholder
            return None

        except Exception as e:
            logger.debug(f"Alternative endpoint failed: {e}")
            return None

    async def _extract_image_from_response(self, response_data: dict) -> Optional[bytes]:
        """Extract image data from API response"""
        try:
            # Handle different response formats
            if "imageUrl" in response_data:
                return await self._download_image(response_data["imageUrl"])
            elif "image" in response_data:
                image_data = response_data["image"]
                if image_data.startswith("data:image"):
                    base64_data = image_data.split(",")[1]
                    return base64.b64decode(base64_data)
                else:
                    return base64.b64decode(image_data)
            elif "data" in response_data:
                return base64.b64decode(response_data["data"])

            return None

        except Exception as e:
            logger.error(f"Error extracting image from response: {e}")
            return None

    async def _call_extracted_api(self, api_endpoint: str, prompt: str) -> Optional[bytes]:
        """Call an API endpoint extracted from the page"""
        try:
            session = await self._get_session()

            request_data = {
                "prompt": prompt,
                "resolution": "512x512"
            }

            async with session.post(
                api_endpoint,
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return await self._extract_image_from_response(result)

            return None

        except Exception as e:
            logger.debug(f"Extracted API call failed: {e}")
            return None

    async def _download_image(self, image_url: str) -> Optional[bytes]:
        """Download image from URL"""
        try:
            session = await self._get_session()

            async with session.get(image_url) as response:
                if response.status == 200:
                    image_data = await response.read()
                    logger.info(f"Downloaded image: {len(image_data)} bytes")
                    return image_data
                else:
                    logger.error(f"Failed to download image: {response.status}")
                    return None

        except Exception as e:
            logger.error(f"Error downloading image: {e}")
            return None

    async def _generate_placeholder_image(self, prompt: str, width: int, height: int) -> Optional[bytes]:
        """Generate an attractive placeholder image when real generation fails"""
        try:
            from PIL import Image, ImageDraw, ImageFont, ImageFilter
            import io
            import random
            import math

            # Create a more sophisticated background
            img = Image.new('RGB', (width, height), color=(20, 25, 40))  # Dark blue base
            draw = ImageDraw.Draw(img)

            # Generate gradient background
            self._create_gradient_background(img, prompt)

            # Add geometric patterns
            self._add_geometric_patterns(draw, width, height, prompt)

            # Add main content area
            content_area = self._create_content_area(width, height)

            # Add stylized text
            self._add_stylized_text(draw, prompt, content_area, width, height)

            # Add decorative elements
            self._add_decorative_elements(draw, width, height, prompt)

            # Apply subtle blur effect to background elements
            # img = img.filter(ImageFilter.GaussianBlur(radius=0.5))

            # Convert to bytes
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='PNG', quality=95)
            img_bytes.seek(0)

            logger.info(f"Generated enhanced placeholder image for: {prompt[:50]}...")
            return img_bytes.getvalue()

        except Exception as e:
            logger.error(f"Enhanced placeholder generation error: {e}")
            # Fallback to simple placeholder
            return await self._generate_simple_placeholder(prompt, width, height)

    def _create_gradient_background(self, img, prompt: str):
        """Create a gradient background based on prompt content"""
        width, height = img.size
        pixels = img.load()

        # Choose colors based on prompt keywords
        color_schemes = {
            'landscape': [(30, 60, 90), (120, 180, 220)],  # Blue gradient
            'mountain': [(40, 30, 60), (180, 120, 80)],    # Purple to orange
            'forest': [(20, 40, 20), (80, 120, 60)],       # Green gradient
            'city': [(40, 40, 60), (120, 120, 140)],       # Gray gradient
            'sunset': [(60, 30, 40), (220, 120, 80)],      # Red to orange
            'ocean': [(20, 40, 80), (80, 160, 200)],       # Blue gradient
            'space': [(10, 10, 30), (60, 40, 80)],         # Dark purple
            'fire': [(80, 20, 20), (220, 120, 40)],        # Red to yellow
            'default': [(30, 40, 60), (120, 140, 180)]     # Default blue
        }

        # Select color scheme based on prompt
        scheme_key = 'default'
        prompt_lower = prompt.lower()
        for key in color_schemes:
            if key in prompt_lower:
                scheme_key = key
                break

        start_color, end_color = color_schemes[scheme_key]

        # Create diagonal gradient
        for y in range(height):
            for x in range(width):
                # Calculate gradient position (0 to 1)
                gradient_pos = (x + y) / (width + height)

                # Interpolate colors
                r = int(start_color[0] + (end_color[0] - start_color[0]) * gradient_pos)
                g = int(start_color[1] + (end_color[1] - start_color[1]) * gradient_pos)
                b = int(start_color[2] + (end_color[2] - start_color[2]) * gradient_pos)

                pixels[x, y] = (r, g, b)

    def _add_geometric_patterns(self, draw, width: int, height: int, prompt: str):
        """Add subtle geometric patterns"""
        import random

        # Set seed based on prompt for consistent patterns
        random.seed(hash(prompt) % 1000)

        # Add subtle circles with low opacity
        for _ in range(6):
            x = random.randint(0, width)
            y = random.randint(0, height)
            radius = random.randint(30, 100)

            # Draw semi-transparent circles
            alpha = random.randint(20, 40)
            circle_color = (255, 255, 255, alpha)

            # Draw circle outline only for subtle effect
            draw.ellipse([x-radius, y-radius, x+radius, y+radius],
                        outline=(255, 255, 255), width=1)

    def _create_content_area(self, width: int, height: int) -> tuple:
        """Define the main content area"""
        margin = min(width, height) // 8
        return (margin, margin, width - margin, height - margin)

    def _add_stylized_text(self, draw, prompt: str, content_area: tuple, width: int, height: int):
        """Add stylized text with better formatting"""
        try:
            # Try to load a better font
            font_size = min(width, height) // 15
            font = ImageFont.load_default()

            # Process prompt text
            words = prompt.split()
            if len(words) > 8:
                # Take first 8 words and add "..."
                display_text = ' '.join(words[:8]) + "..."
            else:
                display_text = prompt

            # Wrap text properly
            lines = self._wrap_text(display_text, font, content_area[2] - content_area[0])

            # Calculate text positioning
            line_height = font_size + 8
            total_text_height = len(lines) * line_height
            start_y = content_area[1] + (content_area[3] - content_area[1] - total_text_height) // 2

            # Draw text with shadow effect
            for i, line in enumerate(lines):
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                x = content_area[0] + (content_area[2] - content_area[0] - text_width) // 2
                y = start_y + i * line_height

                # Draw shadow
                draw.text((x + 2, y + 2), line, fill=(0, 0, 0), font=font)
                # Draw main text
                draw.text((x, y), line, fill=(255, 255, 255), font=font)

        except Exception as e:
            logger.debug(f"Text rendering error: {e}")

    def _wrap_text(self, text: str, font, max_width: int) -> list:
        """Wrap text to fit within max_width"""
        words = text.split()
        lines = []
        current_line = []

        for word in words:
            test_line = ' '.join(current_line + [word])
            bbox = font.getbbox(test_line) if hasattr(font, 'getbbox') else (0, 0, len(test_line) * 10, 20)

            if bbox[2] <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                    current_line = [word]
                else:
                    lines.append(word)

        if current_line:
            lines.append(' '.join(current_line))

        return lines

    def _add_decorative_elements(self, draw, width: int, height: int, prompt: str):
        """Add decorative elements based on prompt content"""
        import random
        random.seed(hash(prompt) % 1000)

        # Add corner decorations
        corner_size = min(width, height) // 12

        # Top-left corner
        draw.polygon([
            (0, 0), (corner_size, 0), (0, corner_size)
        ], fill=(200, 200, 200))

        # Bottom-right corner
        draw.polygon([
            (width, height), (width - corner_size, height), (width, height - corner_size)
        ], fill=(200, 200, 200))

        # Add subtle border
        border_width = 2
        draw.rectangle([0, 0, width-1, height-1], outline=(180, 180, 180), width=border_width)

    async def _generate_simple_placeholder(self, prompt: str, width: int, height: int) -> Optional[bytes]:
        """Generate a simple fallback placeholder"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import io

            # Create a simple gradient
            img = Image.new('RGB', (width, height), color=(40, 50, 70))
            draw = ImageDraw.Draw(img)

            # Simple gradient
            for y in range(height):
                color_intensity = int(40 + (y / height) * 60)
                draw.line([(0, y), (width, y)], fill=(color_intensity, color_intensity + 10, color_intensity + 20))

            # Add simple text
            try:
                font = ImageFont.load_default()
            except:
                font = None

            # Simplified text
            display_text = prompt[:50] + "..." if len(prompt) > 50 else prompt
            bbox = draw.textbbox((0, 0), display_text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            x = (width - text_width) // 2
            y = (height - text_height) // 2

            # Draw text with shadow
            draw.text((x + 1, y + 1), display_text, fill=(0, 0, 0), font=font)
            draw.text((x, y), display_text, fill=(255, 255, 255), font=font)

            # Convert to bytes
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='PNG')
            img_bytes.seek(0)

            return img_bytes.getvalue()

        except Exception as e:
            logger.error(f"Simple placeholder generation error: {e}")
            return None

    async def cleanup(self):
        """Clean up resources"""
        if self.session and not self.session.closed:
            await self.session.close()


class PerchancePromptEnhancer:
    """Enhances prompts for better Perchance AI results"""

    def __init__(self):
        self.style_modifiers = {
            "realistic": "photorealistic, high quality, detailed",
            "artistic": "artistic, creative, stylized",
            "cartoon": "cartoon style, animated, colorful",
            "anime": "anime style, manga, japanese art",
            "abstract": "abstract art, modern, conceptual",
            "vintage": "vintage style, retro, classic",
            "cyberpunk": "cyberpunk, futuristic, neon, sci-fi",
            "fantasy": "fantasy art, magical, mystical"
        }

        self.quality_enhancers = [
            "high quality", "detailed", "sharp focus", "professional",
            "masterpiece", "best quality", "ultra detailed"
        ]

    def enhance_prompt(self, prompt: str, style: str = "realistic",
                      add_quality: bool = True) -> str:
        """Enhance prompt for better generation results"""
        enhanced = prompt.strip()

        # Add style modifiers
        if style in self.style_modifiers:
            enhanced += f", {self.style_modifiers[style]}"

        # Add quality enhancers
        if add_quality:
            quality_terms = ", ".join(self.quality_enhancers[:3])  # Use first 3
            enhanced += f", {quality_terms}"

        return enhanced

    def create_negative_prompt(self, style: str = "realistic") -> str:
        """Create appropriate negative prompt"""
        base_negative = "low quality, blurry, distorted, deformed, ugly, bad anatomy"

        style_specific = {
            "realistic": "cartoon, anime, painting, drawing, sketch",
            "cartoon": "realistic, photographic, dark, gritty",
            "anime": "realistic, western style, 3d render",
            "artistic": "photographic, realistic, plain"
        }

        if style in style_specific:
            return f"{base_negative}, {style_specific[style]}"

        return base_negative
