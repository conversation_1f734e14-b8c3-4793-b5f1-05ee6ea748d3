#!/usr/bin/env python3
"""
Diagnose Viral Automation Services

Verifică de ce serviciile de automatizare virală nu sunt disponibile în interfața web.
"""

import sys
from pathlib import Path

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

def diagnose_viral_services():
    """Diagnostichează serviciile de automatizare virală"""
    print("🔍 Diagnosing viral automation services availability...")
    print("=" * 60)
    
    results = {}
    
    # Test 1: Check if GPT4Free is working
    print("1️⃣ Testing GPT4Free service...")
    try:
        from app.services.gpt4free_service import gpt4free_service
        is_available = gpt4free_service.is_available()
        print(f"   GPT4Free service available: {is_available}")
        
        if is_available:
            status = gpt4free_service.get_status()
            models_count = len(status.get("available_models", []))
            providers_count = len(status.get("available_providers", []))
            print(f"   Models: {models_count}")
            print(f"   Providers: {providers_count}")
            results["gpt4free"] = True
        else:
            print("   GPT4Free service not available")
            results["gpt4free"] = False
    except Exception as e:
        print(f"   ❌ GPT4Free service error: {e}")
        results["gpt4free"] = False
    
    print()
    
    # Test 2: Check viral automation interface imports
    print("2️⃣ Testing viral automation interface...")
    try:
        from webui.components.viral_automation_interface import VIRAL_SERVICES_AVAILABLE
        print(f"   VIRAL_SERVICES_AVAILABLE = {VIRAL_SERVICES_AVAILABLE}")
        results["interface"] = VIRAL_SERVICES_AVAILABLE
    except Exception as e:
        print(f"   ❌ Viral automation interface error: {e}")
        results["interface"] = False
    
    print()
    
    # Test 3: Check individual services
    print("3️⃣ Testing individual services...")
    services = [
        "viral_content_generator",
        "one_click_viral_generator", 
        "romanian_content_optimizer",
        "performance_analyzer",
        "contextual_image_ai"
    ]
    
    service_results = {}
    for service_name in services:
        try:
            module = __import__(f"app.services.{service_name}", fromlist=[service_name])
            print(f"   ✅ {service_name}: Available")
            service_results[service_name] = True
        except Exception as e:
            print(f"   ❌ {service_name}: {e}")
            service_results[service_name] = False
    
    results["services"] = service_results
    
    print()
    
    # Test 4: Check if the issue is in the interface import
    print("4️⃣ Testing interface function import...")
    try:
        from webui.components.viral_automation_interface import render_viral_automation_interface
        print("   ✅ render_viral_automation_interface: Available")
        results["render_function"] = True
    except Exception as e:
        print(f"   ❌ render_viral_automation_interface: {e}")
        results["render_function"] = False
    
    print()
    
    # Test 5: Check Main.py integration
    print("5️⃣ Testing Main.py integration...")
    try:
        # Check if VIRAL_AUTOMATION_AVAILABLE is set correctly in Main.py
        import webui.Main as main_module
        if hasattr(main_module, 'VIRAL_AUTOMATION_AVAILABLE'):
            viral_available = main_module.VIRAL_AUTOMATION_AVAILABLE
            print(f"   VIRAL_AUTOMATION_AVAILABLE in Main.py: {viral_available}")
            results["main_integration"] = viral_available
        else:
            print("   ❌ VIRAL_AUTOMATION_AVAILABLE not found in Main.py")
            results["main_integration"] = False
    except Exception as e:
        print(f"   ❌ Main.py integration error: {e}")
        results["main_integration"] = False
    
    print()
    print("=" * 60)
    print("📊 DIAGNOSIS SUMMARY")
    print("=" * 60)
    
    # Print summary
    print(f"GPT4Free Service: {'✅' if results.get('gpt4free') else '❌'}")
    print(f"Interface Available: {'✅' if results.get('interface') else '❌'}")
    print(f"Render Function: {'✅' if results.get('render_function') else '❌'}")
    print(f"Main.py Integration: {'✅' if results.get('main_integration') else '❌'}")
    
    print("\nIndividual Services:")
    for service, available in results.get("services", {}).items():
        print(f"  {service}: {'✅' if available else '❌'}")
    
    # Determine the issue
    print("\n" + "=" * 60)
    print("🔧 ISSUE ANALYSIS")
    print("=" * 60)
    
    if not results.get("gpt4free"):
        print("❌ PRIMARY ISSUE: GPT4Free service is not available")
        print("💡 SOLUTION: Check GPT4Free installation and configuration")
    elif not results.get("interface"):
        print("❌ PRIMARY ISSUE: Viral automation interface imports are failing")
        print("💡 SOLUTION: Check service imports in viral_automation_interface.py")
    elif not results.get("main_integration"):
        print("❌ PRIMARY ISSUE: Main.py integration is not working")
        print("💡 SOLUTION: Check VIRAL_AUTOMATION_AVAILABLE variable in Main.py")
    else:
        print("✅ All components appear to be working")
        print("💡 The issue might be elsewhere or already resolved")
    
    return results


if __name__ == "__main__":
    diagnose_viral_services()
