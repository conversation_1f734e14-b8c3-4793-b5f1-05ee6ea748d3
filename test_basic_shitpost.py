#!/usr/bin/env python3
"""
Basic Shitpost Generator Test
Simple test without async operations to verify core functionality
"""

import os
import sys
from pathlib import Path

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

def test_basic_imports():
    """Test that basic modules can be imported"""
    print("🧪 Testing Basic Imports...")
    
    try:
        from app.services.shitpost_generator import ShitpostGenerator
        print("✅ ShitpostGenerator imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import ShitpostGenerator: {e}")
        return False

def test_text_generation():
    """Test basic text generation without AI"""
    print("\n📝 Testing Text Generation...")
    
    try:
        from app.services.shitpost_generator import ShitpostGenerator
        generator = ShitpostGenerator()
        
        themes = ['romanian', 'gaming', 'philosophical', 'random_chaos']
        
        for theme in themes:
            text = generator._generate_absurd_text(theme)
            print(f"✅ {theme}: {text[:60]}...")
            
        return True
        
    except Exception as e:
        print(f"❌ Text generation failed: {e}")
        return False

def test_gpt4free_availability():
    """Test if GPT4Free is available (without using it)"""
    print("\n🤖 Testing GPT4Free Availability...")
    
    try:
        from app.services.gpt4free_service import gpt4free_service
        
        is_available = gpt4free_service.is_available()
        print(f"GPT4Free available: {is_available}")
        
        if is_available:
            status = gpt4free_service.get_status()
            print(f"Default model: {status.get('default_model', 'Unknown')}")
            print(f"Available models: {len(status.get('available_models', []))}")
        
        return True
        
    except ImportError as e:
        print(f"⚠️ GPT4Free not available: {e}")
        return False

def test_ai_services_availability():
    """Test if AI image services are available"""
    print("\n🎨 Testing AI Image Services...")
    
    try:
        from app.services.free_ai_services import free_ai_generator
        
        status = free_ai_generator.get_service_status()
        print("AI Services Status:")
        
        for service, info in status.items():
            if isinstance(info, dict):
                available = info.get('available', False)
                free = info.get('free', False)
                print(f"  {service}: {'✅' if available else '❌'} Available, {'💰' if not free else '🆓'} {'Free' if free else 'Paid'}")
            else:
                print(f"  {service}: {info}")
        
        return True
        
    except ImportError as e:
        print(f"⚠️ AI services not available: {e}")
        return False

def test_basic_video_generation():
    """Test basic video generation (may fail due to dependencies)"""
    print("\n🎬 Testing Basic Video Generation...")
    
    try:
        from app.services.shitpost_generator import ShitpostGenerator
        generator = ShitpostGenerator()
        
        print("Attempting to generate a basic shitpost...")
        print("(This may fail if FFmpeg/ImageMagick are not installed)")
        
        # Try to generate a very short video
        result = generator.generate_random_shitpost(
            theme="romanian",
            duration=3,  # Very short for testing
            chaos_level=3,  # Low chaos to reduce processing
            custom_text="Test shitpost pentru verificare"
        )
        
        if result['success']:
            print(f"✅ Video generated successfully!")
            print(f"   Output: {result['output_path']}")
            print(f"   Text: {result['text_generated']}")
            
            # Check if file exists
            if os.path.exists(result['output_path']):
                file_size = os.path.getsize(result['output_path'])
                print(f"   File size: {file_size / 1024:.1f} KB")
            else:
                print("   ⚠️ Output file not found")
            
            return True
        else:
            print(f"❌ Video generation failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"⚠️ Video generation test failed: {e}")
        print("   This is expected if video dependencies are missing")
        return False

def test_cli_functionality():
    """Test CLI functionality"""
    print("\n💻 Testing CLI Functionality...")
    
    try:
        import shitpost_cli
        
        cli = shitpost_cli.ShitpostCLI()
        parser = cli.create_parser()
        
        # Test argument parsing
        test_args = ['generate', '--theme', 'romanian', '--chaos', '5', '--duration', '10']
        args = parser.parse_args(test_args)
        
        print(f"✅ CLI parser working:")
        print(f"   Command: {args.command}")
        print(f"   Theme: {args.theme}")
        print(f"   Chaos: {args.chaos}")
        print(f"   Duration: {args.duration}")
        
        return True
        
    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        return False

def test_web_ui_components():
    """Test web UI components can be imported"""
    print("\n🌐 Testing Web UI Components...")
    
    try:
        from webui.components.shitpost_ui import render_shitpost_tab, _check_ai_services_status
        
        print("✅ Shitpost UI components imported successfully")
        
        # Test AI services status check
        status = _check_ai_services_status()
        print("AI Services Status:")
        for service, available in status.items():
            print(f"  {service}: {'✅' if available else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Web UI test failed: {e}")
        return False

def run_basic_tests():
    """Run all basic tests"""
    print("🧪 Basic Shitpost Generator Test Suite")
    print("=" * 50)
    print("Testing core functionality without async operations")
    print()
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Text Generation", test_text_generation),
        ("GPT4Free Availability", test_gpt4free_availability),
        ("AI Services Availability", test_ai_services_availability),
        ("CLI Functionality", test_cli_functionality),
        ("Web UI Components", test_web_ui_components),
        ("Basic Video Generation", test_basic_video_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"💥 {test_name} CRASHED: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary")
    print("=" * 60)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! Basic functionality is working.")
    elif passed >= total * 0.7:
        print("\n✅ Most tests passed. Core functionality is working.")
        print("Some failures may be due to missing optional dependencies.")
    else:
        print("\n⚠️ Several tests failed. Check the output above for issues.")
    
    print("\n🚀 Next Steps:")
    if passed < total:
        print("1. Install missing dependencies:")
        print("   pip install -U g4f[all] aiohttp psutil")
        print("2. Install video dependencies (FFmpeg, ImageMagick)")
        print("3. Run setup script: python setup_shitpost_generator.py")
    
    print("4. Try the web interface: python webui.bat")
    print("5. Try the CLI: python shitpost_cli.py generate --theme romanian")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = run_basic_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Tests cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        sys.exit(1)
