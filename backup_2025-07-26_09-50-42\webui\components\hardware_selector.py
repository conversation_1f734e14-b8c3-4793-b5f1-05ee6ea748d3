"""
Componenta UI pentru selectarea și configurarea hardware-ului de procesare video.
"""

import streamlit as st
from typing import Optional, Dict, Any
from loguru import logger

import sys
import os

# Adaugă calea către modulele aplicației
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from app.utils.hardware_detector import HardwareInfo, get_hardware_detector
from app.utils.hardware_config import get_hardware_configurator


def render_hardware_selector() -> Optional[HardwareInfo]:
    """
    Renderează selectorul de hardware și returnează hardware-ul selectat.
    
    Returns:
        Optional[HardwareInfo]: Hardware-ul selectat sau None
    """
    st.subheader("🔧 Configurare Hardware Procesare")
    
    # Inițializează detectorul de hardware
    detector = get_hardware_detector()
    configurator = get_hardware_configurator()
    
    # Obține lista hardware-ului disponibil
    available_hardware = detector.get_available_hardware()
    
    if not available_hardware:
        st.error("❌ Nu s-a detectat niciun hardware disponibil pentru procesarea video!")
        return None
    
    # Creează coloane pentru layout
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Selectorul de hardware
        hardware_options = []
        hardware_map = {}
        
        for i, hw in enumerate(available_hardware):
            display_name = f"{hw.name} - {hw.estimated_speed}"
            if hw.memory_gb:
                display_name += f" ({hw.memory_gb:.1f}GB)"
            
            hardware_options.append(display_name)
            hardware_map[display_name] = hw
        
        # Determină indexul implicit (hardware recomandat)
        recommended_hw = detector.get_recommended_hardware()
        default_index = 0
        if recommended_hw:
            for i, hw in enumerate(available_hardware):
                if hw.name == recommended_hw.name:
                    default_index = i
                    break
        
        selected_option = st.selectbox(
            "Selectează Hardware pentru Procesare:",
            options=hardware_options,
            index=default_index,
            help="Alege hardware-ul pentru procesarea video. GPU-urile oferă performanțe mai bune."
        )
        
        selected_hardware = hardware_map.get(selected_option)
        
        if selected_hardware:
            # Afișează informații detaliate despre hardware-ul selectat
            _render_hardware_details(selected_hardware)
            
            # Buton pentru aplicarea configurației
            if st.button("🚀 Aplică Configurația Hardware", type="primary"):
                with st.spinner("Configurare hardware..."):
                    success = configurator.configure_hardware(selected_hardware)
                    
                    if success:
                        st.success(f"✅ Hardware configurat cu succes: {selected_hardware.name}")
                        st.session_state["configured_hardware"] = selected_hardware
                    else:
                        st.error("❌ Configurarea hardware-ului a eșuat!")
    
    with col2:
        # Informații despre configurația curentă
        _render_current_config(configurator)
    
    return selected_hardware


def _render_hardware_details(hardware: HardwareInfo) -> None:
    """Renderează detaliile hardware-ului selectat."""
    st.markdown("### 📊 Detalii Hardware")
    
    # Creează metrici
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            label="Tip Hardware",
            value=hardware.type.upper(),
            help="Tipul de hardware detectat"
        )
    
    with col2:
        st.metric(
            label="Scor Performanță",
            value=f"{hardware.performance_score}/10",
            help="Scorul estimat de performanță (10 = cel mai rapid)"
        )
    
    with col3:
        st.metric(
            label="Viteză Estimată",
            value=hardware.estimated_speed,
            help="Viteza estimată de procesare"
        )
    
    # Informații suplimentare
    if hardware.memory_gb:
        st.info(f"💾 **Memorie disponibilă:** {hardware.memory_gb:.1f}GB")
    
    if hardware.compute_capability:
        st.info(f"🔢 **Compute Capability:** {hardware.compute_capability}")
    
    # Recomandări bazate pe tip
    _render_hardware_recommendations(hardware)


def _render_hardware_recommendations(hardware: HardwareInfo) -> None:
    """Renderează recomandări pentru hardware-ul selectat."""
    recommendations = {
        "nvidia": {
            "icon": "🚀",
            "title": "NVIDIA GPU - Recomandat",
            "description": "Cel mai rapid pentru procesarea video cu suport CUDA. Ideal pentru videoclipuri complexe și rezoluții mari.",
            "tips": [
                "Asigură-te că ai driverele NVIDIA actualizate",
                "Verifică că CUDA este instalat corect",
                "Monitorizează temperatura GPU-ului în timpul procesării"
            ]
        },
        "amd": {
            "icon": "⚡",
            "title": "AMD GPU - Bun",
            "description": "Performanțe bune pentru procesarea video. Folosește optimizări CPU pentru compatibilitate maximă.",
            "tips": [
                "Asigură-te că ai driverele AMD actualizate",
                "Procesarea se va face prin CPU optimizat",
                "Consideră upgrade la NVIDIA pentru performanțe mai bune"
            ]
        },
        "intel": {
            "icon": "🔧",
            "title": "Intel GPU - Acceptabil",
            "description": "Performanțe moderate. GPU-urile Intel mai noi (ARC/Xe) oferă suport hardware pentru video.",
            "tips": [
                "GPU-urile Intel ARC/Xe oferă performanțe mai bune",
                "GPU-urile integrate sunt mai lente",
                "Consideră folosirea CPU-ului pentru videoclipuri complexe"
            ]
        },
        "cpu": {
            "icon": "🖥️",
            "title": "CPU - Compatibil",
            "description": "Procesare prin CPU. Mai lent decât GPU-urile, dar compatibil cu toate sistemele.",
            "tips": [
                "Închide alte aplicații pentru performanțe mai bune",
                "Procesarea va dura mai mult",
                "Consideră upgrade la un GPU pentru viteze mai mari"
            ]
        }
    }
    
    rec = recommendations.get(hardware.type, recommendations["cpu"])

    st.markdown(f"### {rec['icon']} {rec['title']}")
    st.write(rec["description"])

    # Afișează sfaturile direct, fără expander imbricat
    st.markdown("**💡 Sfaturi pentru optimizare:**")
    for tip in rec["tips"]:
        st.write(f"• {tip}")


def _render_current_config(configurator) -> None:
    """Renderează informații despre configurația curentă."""
    st.markdown("### ⚙️ Configurație Curentă")
    
    current_hw = configurator.get_current_hardware()
    
    if current_hw:
        st.success(f"✅ **Activ:** {current_hw.name}")
        
        # Informații despre performanță
        perf_info = configurator.get_performance_info()
        
        for key, value in perf_info.items():
            if key != "Hardware":  # Nu afișa din nou numele
                st.write(f"**{key}:** {value}")
        
        # Buton pentru resetarea configurației
        if st.button("🔄 Resetează Configurația", help="Resetează la configurația implicită"):
            configurator._reset_config()
            if "configured_hardware" in st.session_state:
                del st.session_state["configured_hardware"]
            st.rerun()
    else:
        st.warning("⚠️ Nicio configurație hardware activă")
        st.write("Selectează și aplică o configurație hardware pentru a începe.")


def get_configured_hardware() -> Optional[HardwareInfo]:
    """
    Returnează hardware-ul configurat din session state.
    
    Returns:
        Optional[HardwareInfo]: Hardware-ul configurat sau None
    """
    return st.session_state.get("configured_hardware")


def is_hardware_configured() -> bool:
    """
    Verifică dacă hardware-ul este configurat.
    
    Returns:
        bool: True dacă hardware-ul este configurat
    """
    return "configured_hardware" in st.session_state


def render_hardware_status_badge() -> None:
    """Renderează un badge cu statusul hardware-ului configurat."""
    configured_hw = get_configured_hardware()
    
    if configured_hw:
        # Badge verde pentru hardware configurat
        st.markdown(
            f"""
            <div style="
                background-color: #d4edda;
                border: 1px solid #c3e6cb;
                color: #155724;
                padding: 8px 12px;
                border-radius: 4px;
                margin: 10px 0;
                font-size: 14px;
            ">
                🚀 <strong>Hardware activ:</strong> {configured_hw.name} ({configured_hw.estimated_speed})
            </div>
            """,
            unsafe_allow_html=True
        )
    else:
        # Badge galben pentru hardware neconfigurat
        st.markdown(
            """
            <div style="
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
                padding: 8px 12px;
                border-radius: 4px;
                margin: 10px 0;
                font-size: 14px;
            ">
                ⚠️ <strong>Hardware neconfigurat</strong> - Configurează hardware-ul pentru performanțe optime
            </div>
            """,
            unsafe_allow_html=True
        )
