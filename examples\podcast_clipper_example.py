#!/usr/bin/env python3
"""
Exemplu de utilizare pentru Podcast Clipper
Demonstrează cum să folosești serviciul de extragere clipuri din podcast-uri
"""

import sys
import os
from pathlib import Path

# Adaugă root directory la path
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

from app.services.podcast_clipper_service import (
    PodcastClipperService, 
    PodcastClipConfig
)

def progress_callback(message: str, progress: float):
    """Callback pentru afișarea progresului"""
    print(f"[{progress*100:.1f}%] {message}")

def main():
    """Exemplu principal de utilizare"""
    print("🎙️ PODCAST CLIPPER - EXEMPLU DE UTILIZARE")
    print("=" * 50)
    
    # Configurație pentru procesare
    config = PodcastClipConfig(
        confidence_threshold=0.6,      # Prag încredere pentru detectarea persoanelor
        max_people=2,                  # Maxim 2 persoane detectate
        clip_duration=30,              # Clipuri de 30 secunde
        min_speaker_time=5,            # Minim 5 secunde per vorbitor
        enable_captions=True,          # Activează subtitrările
        caption_style="Modern",        # Stil modern pentru subtitrări
        highlight_words=True,          # Evidențiere cuvinte
        output_resolution="1080x1920", # Rezoluție Full HD vertical
        frame_rate=30,                 # 30 FPS
        video_quality="High",          # Calitate înaltă
        audio_enhancement=True,        # Îmbunătățire audio
        noise_reduction=True,          # Reducere zgomot
        normalize_audio=True           # Normalizare volum
    )
    
    print("⚙️ Configurație:")
    print(f"  - Prag încredere: {config.confidence_threshold}")
    print(f"  - Persoane maxime: {config.max_people}")
    print(f"  - Durata clipuri: {config.clip_duration}s")
    print(f"  - Rezoluție: {config.output_resolution}")
    print(f"  - Subtitrări: {'Da' if config.enable_captions else 'Nu'}")
    
    # Inițializează serviciul
    print("\n🔧 Inițializare serviciu...")
    service = PodcastClipperService()
    
    # Exemplu cu fișier video (trebuie să existe)
    video_path = "input.mp4"  # Schimbă cu calea către videoclipul tău
    
    if not os.path.exists(video_path):
        print(f"\n⚠️ Fișierul video '{video_path}' nu există!")
        print("Pentru a testa funcționalitatea:")
        print("1. Plasează un videoclip podcast în directorul curent")
        print("2. Redenumește-l 'input.mp4'")
        print("3. Rulează din nou acest script")
        return
    
    print(f"\n📁 Procesare video: {video_path}")
    
    try:
        # Procesează podcast-ul
        clips = service.process_podcast(
            video_path=video_path,
            config=config,
            progress_callback=progress_callback
        )
        
        print(f"\n✅ Procesare completă! Generate {len(clips)} clipuri:")
        
        # Afișează informații despre clipurile generate
        for i, clip in enumerate(clips, 1):
            print(f"\n📹 Clip {i}:")
            print(f"  ⏱️ Timp: {clip.start_time:.1f}s - {clip.end_time:.1f}s")
            print(f"  ⏳ Durata: {clip.duration:.1f}s")
            print(f"  🗣️ Vorbitor: {clip.speaker_id}")
            print(f"  📊 Încredere: {clip.confidence_score:.2f}")
            if clip.transcript:
                preview = clip.transcript[:100] + "..." if len(clip.transcript) > 100 else clip.transcript
                print(f"  📝 Transcript: {preview}")
            print(f"  💾 Fișier: {clip.video_path}")
        
        print(f"\n🎉 Succes! {len(clips)} clipuri generate din podcast.")
        
    except Exception as e:
        print(f"\n❌ Eroare în procesarea podcast-ului: {e}")
        print("\n🔍 Verificări recomandate:")
        print("1. Asigură-te că fișierul video este valid")
        print("2. Verifică că toate dependințele sunt instalate")
        print("3. Rulează: python install_podcast_clipper.py")

def test_service_initialization():
    """Testează inițializarea serviciului"""
    print("\n🧪 Test inițializare serviciu...")
    
    try:
        service = PodcastClipperService()
        
        # Verifică dacă modelele sunt încărcate
        if service.yolo_net is not None:
            print("✅ Model YOLO încărcat cu succes")
        else:
            print("⚠️ Model YOLO nu este disponibil")
        
        if service.whisper_model is not None:
            print("✅ Model Whisper încărcat cu succes")
        else:
            print("⚠️ Model Whisper nu este disponibil")
        
        print("✅ Serviciul a fost inițializat cu succes!")
        return True
        
    except Exception as e:
        print(f"❌ Eroare la inițializarea serviciului: {e}")
        return False

def show_requirements():
    """Afișează cerințele pentru funcționare"""
    print("\n📋 CERINȚE PENTRU PODCAST CLIPPER")
    print("=" * 40)
    print("\n🔧 Dependințe Python:")
    print("  - opencv-python (detectarea persoanelor)")
    print("  - whisper (transcripția audio)")
    print("  - moviepy (procesarea video)")
    print("  - torch (machine learning)")
    print("  - numpy (calcule numerice)")
    print("  - librosa (procesarea audio)")
    
    print("\n📁 Fișiere necesare:")
    print("  - models/yolov3.cfg (configurație YOLO)")
    print("  - models/yolov3.weights (greutăți YOLO ~250MB)")
    print("  - models/coco.names (etichete COCO)")
    
    print("\n💻 Cerințe sistem:")
    print("  - CPU: Intel i5/AMD Ryzen 5+")
    print("  - RAM: 8GB+ (recomandat 16GB)")
    print("  - GPU: NVIDIA cu CUDA (opțional)")
    print("  - Spațiu: ~2GB pentru modele")
    
    print("\n🚀 Pentru instalare:")
    print("  python install_podcast_clipper.py")

if __name__ == "__main__":
    # Verifică argumentele din linia de comandă
    if len(sys.argv) > 1:
        if sys.argv[1] == "--test":
            test_service_initialization()
        elif sys.argv[1] == "--requirements":
            show_requirements()
        elif sys.argv[1] == "--help":
            print("🎙️ PODCAST CLIPPER - EXEMPLU DE UTILIZARE")
            print("\nUtilizare:")
            print("  python podcast_clipper_example.py           # Rulează exemplul principal")
            print("  python podcast_clipper_example.py --test    # Testează inițializarea")
            print("  python podcast_clipper_example.py --requirements # Afișează cerințele")
            print("  python podcast_clipper_example.py --help    # Afișează acest mesaj")
        else:
            print(f"❌ Argument necunoscut: {sys.argv[1]}")
            print("Folosește --help pentru opțiuni disponibile")
    else:
        main()
