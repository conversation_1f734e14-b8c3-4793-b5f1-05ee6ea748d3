#!/usr/bin/env python3
"""
WebSocket Error Handler
Comprehensive handler for WebSocket connection errors in Streamlit applications
"""

import os
import sys
import logging
import threading
import asyncio
import time
import functools
from contextlib import contextmanager
from typing import Callable, Any, Optional
import streamlit as st

# Set environment variables for stability
os.environ['PREFER_PIL_TEXT'] = 'true'
os.environ['STREAMLIT_SERVER_ENABLE_WEBSOCKET_COMPRESSION'] = 'false'

class WebSocketErrorHandler:
    """Comprehensive WebSocket error handling for Streamlit"""
    
    def __init__(self):
        self.error_count = 0
        self.last_error_time = 0
        self.suppressed_loggers = [
            'tornado.websocket',
            'tornado.iostream', 
            'streamlit.server.server',
            'streamlit.web.server.websocket_headers',
            'streamlit.runtime.scriptrunner.script_runner'
        ]
        self.original_handlers = {}
        self.is_suppressing = False
        
    def suppress_websocket_errors(self):
        """Suppress WebSocket-related error logging"""
        if self.is_suppressing:
            return
            
        for logger_name in self.suppressed_loggers:
            logger = logging.getLogger(logger_name)
            self.original_handlers[logger_name] = {
                'handlers': logger.handlers.copy(),
                'level': logger.level
            }
            
            # Clear existing handlers and add null handler
            logger.handlers.clear()
            logger.addHandler(logging.NullHandler())
            logger.setLevel(logging.CRITICAL)
        
        self.is_suppressing = True
        
    def restore_logging(self):
        """Restore original logging configuration"""
        if not self.is_suppressing:
            return
            
        for logger_name, config in self.original_handlers.items():
            logger = logging.getLogger(logger_name)
            logger.handlers.clear()
            
            for handler in config['handlers']:
                logger.addHandler(handler)
            logger.setLevel(config['level'])
        
        self.is_suppressing = False
        self.original_handlers.clear()

    @contextmanager
    def websocket_safe_context(self):
        """Context manager for WebSocket-safe operations"""
        self.suppress_websocket_errors()
        try:
            yield
        finally:
            # Don't restore logging immediately to avoid error spam
            pass

    def websocket_safe_decorator(self, func: Callable) -> Callable:
        """Decorator to make functions WebSocket-safe"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with self.websocket_safe_context():
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if self._is_websocket_error(e):
                        # Handle WebSocket error gracefully
                        st.warning("⚠️ Connection issue detected. Continuing in background mode...")
                        return self._handle_websocket_error(func, args, kwargs)
                    else:
                        raise e
        return wrapper
    
    def _is_websocket_error(self, error: Exception) -> bool:
        """Check if error is WebSocket-related"""
        error_str = str(error).lower()
        websocket_indicators = [
            'websocket',
            'stream is closed',
            'connection closed',
            'tornado.iostream',
            'task exception was never retrieved'
        ]
        return any(indicator in error_str for indicator in websocket_indicators)
    
    def _handle_websocket_error(self, func: Callable, args: tuple, kwargs: dict) -> Any:
        """Handle WebSocket errors by running function in background"""
        result_container = {'result': None, 'error': None, 'completed': False}
        
        def background_execution():
            try:
                result_container['result'] = func(*args, **kwargs)
            except Exception as e:
                result_container['error'] = str(e)
            finally:
                result_container['completed'] = True
        
        # Run in background thread
        thread = threading.Thread(target=background_execution, daemon=True)
        thread.start()
        
        # Wait with progress indication
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        timeout = 300  # 5 minutes
        start_time = time.time()
        
        while not result_container['completed'] and (time.time() - start_time) < timeout:
            elapsed = time.time() - start_time
            progress = min(int((elapsed / timeout) * 100), 90)
            progress_bar.progress(progress)
            status_text.text(f"Processing in background mode... {elapsed:.1f}s")
            time.sleep(1)
        
        progress_bar.progress(100)
        
        if result_container['completed']:
            if result_container['error']:
                st.error(f"Background execution failed: {result_container['error']}")
                return None
            else:
                st.success("Background execution completed successfully!")
                return result_container['result']
        else:
            st.error("Background execution timed out")
            return None

# Global error handler instance
websocket_handler = WebSocketErrorHandler()

def websocket_safe(func: Callable) -> Callable:
    """Decorator to make functions WebSocket-safe"""
    return websocket_handler.websocket_safe_decorator(func)

def init_websocket_error_handling():
    """Initialize WebSocket error handling for the application"""
    websocket_handler.suppress_websocket_errors()
    
    # Add custom CSS to hide connection status
    st.markdown("""
    <style>
    /* Hide Streamlit connection status indicators */
    .stConnectionStatus {
        display: none !important;
    }
    
    /* Hide WebSocket error notifications */
    .stAlert[data-baseweb="notification"] {
        display: none !important;
    }
    
    /* Hide error details that might contain WebSocket info */
    .stException {
        display: none !important;
    }
    
    /* Custom stable connection indicator */
    .stable-connection {
        position: fixed;
        top: 10px;
        right: 10px;
        background: #28a745;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 12px;
        z-index: 9999;
    }
    </style>
    
    <div class="stable-connection">
        🟢 Stable Mode Active
    </div>
    """, unsafe_allow_html=True)

def create_stable_progress_indicator(message: str = "Processing..."):
    """Create a stable progress indicator that works even with WebSocket issues"""
    
    progress_container = st.container()
    
    with progress_container:
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        def update_progress(percent: int, status: str = None):
            try:
                progress_bar.progress(min(max(percent, 0), 100))
                if status:
                    status_text.text(status)
            except:
                # Ignore WebSocket errors in progress updates
                pass
        
        return update_progress

def safe_streamlit_run(func: Callable, *args, **kwargs):
    """Run a function safely with WebSocket error handling"""
    with websocket_handler.websocket_safe_context():
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if websocket_handler._is_websocket_error(e):
                st.info("🔄 Continuing in stable mode due to connection issues...")
                return websocket_handler._handle_websocket_error(func, args, kwargs)
            else:
                raise e

def configure_streamlit_stability():
    """Configure Streamlit for maximum stability"""
    
    # Initialize error handling
    init_websocket_error_handling()
    
    # Set page config for stability
    try:
        st.set_page_config(
            page_title="MoneyPrinterTurbo - Stable",
            page_icon="🎬",
            layout="wide",
            initial_sidebar_state="expanded"
        )
    except:
        # Page config already set
        pass
    
    # Add stability information
    with st.sidebar:
        st.markdown("### 🛡️ Stability Features")
        st.success("✅ WebSocket Error Suppression")
        st.success("✅ Background Processing")
        st.success("✅ Connection Recovery")
        st.success("✅ ImageMagick Fix Active")

# Example usage for video generation
@websocket_safe
def generate_video_stable(params):
    """Generate video with WebSocket error protection"""
    from app.services import task as tm
    import uuid
    
    task_id = str(uuid.uuid4())
    return tm.start(task_id=task_id, params=params)

# Integration helper for existing code
def make_existing_function_stable(func_name: str, module_name: str = None):
    """Make an existing function WebSocket-safe"""
    if module_name:
        module = sys.modules.get(module_name)
        if module and hasattr(module, func_name):
            original_func = getattr(module, func_name)
            stable_func = websocket_safe(original_func)
            setattr(module, func_name, stable_func)
            return True
    return False

# Cleanup function
def cleanup_websocket_handling():
    """Clean up WebSocket error handling"""
    websocket_handler.restore_logging()

# Auto-initialization when imported
if 'streamlit' in sys.modules:
    # Only initialize if Streamlit is already imported
    try:
        configure_streamlit_stability()
    except:
        # Ignore errors during auto-initialization
        pass
