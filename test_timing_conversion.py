#!/usr/bin/env python3
"""
Test Timing Conversion

This script tests the timing conversion in subtitle generation.
"""

import sys
from pathlib import Path

# Add the project root to the path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

def test_timing_conversion():
    """Test timing conversion from 100ns to seconds"""
    print("⏰ Testing Timing Conversion...")
    
    try:
        from edge_tts.submaker import mktimestamp
        
        # Test timing values (in 100ns units)
        test_times = [
            (0, "0 seconds"),
            (10000000, "1 second"),
            (30000000, "3 seconds"),
            (60000000, "6 seconds"),
            (150000000, "15 seconds")
        ]
        
        print("📊 Timing conversion tests:")
        for time_100ns, description in test_times:
            time_seconds = time_100ns / 10000000
            timestamp = mktimestamp(time_seconds)
            print(f"   {time_100ns:>10} (100ns) → {time_seconds:>6.1f}s → {timestamp} ({description})")
        
        # Test the formatter function
        print("\n📝 Testing subtitle formatter:")
        
        def formatter(idx: int, start_time: float, end_time: float, sub_text: str) -> str:
            start_t = mktimestamp(start_time).replace(".", ",")
            end_t = mktimestamp(end_time).replace(".", ",")
            return f"{idx}\n{start_t} --> {end_t}\n{sub_text}\n"
        
        # Test with realistic timing
        test_subtitle = formatter(
            idx=1,
            start_time=0.0,
            end_time=3.0,
            sub_text="Hello, this is a test subtitle."
        )
        
        print("   Sample subtitle:")
        for line in test_subtitle.strip().split('\n'):
            print(f"     {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ Timing conversion test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_real_subtitle_creation():
    """Test subtitle creation with proper timing"""
    print("\n🎬 Testing Real Subtitle Creation...")
    
    try:
        from app.services.voice import create_subtitle
        from edge_tts import SubMaker
        
        # Create a SubMaker with proper timing
        sub_maker = SubMaker()
        sub_maker.subs = [
            "Hello, this is a test.",
            "We are testing subtitle generation.",
            "This should have proper timing now."
        ]
        
        # Use proper timing values (in 100ns units)
        sub_maker.offset = [
            (0, 30000000),          # 0-3 seconds
            (30000000, 60000000),   # 3-6 seconds
            (60000000, 90000000)    # 6-9 seconds
        ]
        
        test_script = "Hello, this is a test. We are testing subtitle generation. This should have proper timing now."
        
        # Create test output file
        test_dir = Path("./storage/test_timing")
        test_dir.mkdir(parents=True, exist_ok=True)
        subtitle_file = test_dir / "test_timing.srt"
        
        # Create subtitle
        create_subtitle(sub_maker, test_script, str(subtitle_file))
        
        if subtitle_file.exists():
            print("✅ Subtitle file created with timing")
            
            # Read and display content
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print("📋 Subtitle content:")
                for line in content.strip().split('\n'):
                    print(f"   {line}")
            
            return True
        else:
            print("❌ Subtitle file was not created")
            return False
            
    except Exception as e:
        print(f"❌ Real subtitle creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run timing conversion tests"""
    print("🚀 Timing Conversion Test Suite")
    print("=" * 40)
    
    try:
        # Test 1: Basic timing conversion
        test1_success = test_timing_conversion()
        
        # Test 2: Real subtitle creation
        test2_success = test_real_subtitle_creation()
        
        print("\n" + "=" * 40)
        print("📋 TEST SUMMARY")
        print("=" * 40)
        
        results = {
            "Timing Conversion": test1_success,
            "Real Subtitle Creation": test2_success
        }
        
        all_passed = all(results.values())
        
        for test_name, passed in results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"  {status} {test_name}")
        
        if all_passed:
            print(f"\n🎉 TIMING SYSTEM WORKING!")
            print(f"✅ Subtitle timing should be correct now")
        else:
            print(f"\n❌ TIMING ISSUES DETECTED")
            
        return all_passed
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
