#!/usr/bin/env python3
"""
Enhanced Shitpost Generator Demo
Demonstrates all features of the AI-powered shitpost video generator
"""

import os
import sys
import asyncio
import random
from pathlib import Path

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

class ShitpostDemo:
    """Demo class for showcasing shitpost generator features"""
    
    def __init__(self):
        self.demo_outputs = []
        
    def print_header(self):
        """Print demo header"""
        print("🤖 Enhanced Shitpost Generator Demo")
        print("=" * 50)
        print("This demo showcases the AI-powered features of the")
        print("enhanced shitpost video generator.")
        print()
    
    def check_services(self):
        """Check which AI services are available"""
        print("🔍 Checking AI Services Availability...")
        print("-" * 40)
        
        services = {
            'shitpost_generator': False,
            'gpt4free': False,
            'free_ai_images': False,
            'local_sd': False
        }
        
        # Check Shitpost Generator
        try:
            from app.services.shitpost_generator import ShitpostGenerator
            services['shitpost_generator'] = True
            print("✅ Shitpost Generator: Available")
        except ImportError:
            print("❌ Shitpost Generator: Not Available")
        
        # Check GPT4Free
        try:
            from app.services.gpt4free_service import gpt4free_service
            services['gpt4free'] = gpt4free_service.is_available()
            if services['gpt4free']:
                print("✅ GPT4Free: Available")
                status = gpt4free_service.get_status()
                print(f"   Model: {status.get('default_model', 'Unknown')}")
            else:
                print("⚠️ GPT4Free: Installed but not available")
        except ImportError:
            print("❌ GPT4Free: Not Installed")
        
        # Check Free AI Images
        try:
            from app.services.free_ai_services import free_ai_generator
            services['free_ai_images'] = True
            print("✅ Free AI Images: Available")
            
            status = free_ai_generator.get_service_status()
            for service, info in status.items():
                if isinstance(info, dict) and 'available' in info:
                    status_icon = "✅" if info['available'] else "❌"
                    print(f"   {service}: {status_icon}")
        except ImportError:
            print("❌ Free AI Images: Not Available")
        
        # Check Local Stable Diffusion
        try:
            from app.services.ai_image_generator import StableDiffusionAPI
            sd_api = StableDiffusionAPI()
            services['local_sd'] = sd_api.available
            if services['local_sd']:
                print("✅ Local Stable Diffusion: Available")
            else:
                print("ℹ️ Local Stable Diffusion: Not Configured")
        except ImportError:
            print("❌ Local Stable Diffusion: Not Available")
        
        print()
        return services
    
    async def demo_text_generation(self):
        """Demo text generation capabilities"""
        print("📝 Text Generation Demo")
        print("-" * 30)
        
        try:
            from app.services.shitpost_generator import ShitpostGenerator
            generator = ShitpostGenerator()
            
            themes = ['romanian', 'gaming', 'philosophical', 'chaos']
            
            for theme in themes:
                text = generator._generate_absurd_text(theme)
                print(f"🎭 {theme.title()}: {text}")
                self.demo_outputs.append(f"{theme}: {text}")
            
            print()
            
        except Exception as e:
            print(f"❌ Text generation demo failed: {e}")
            print()
    
    async def demo_gpt4free_generation(self):
        """Demo GPT4Free text generation"""
        print("🤖 GPT4Free AI Text Generation Demo")
        print("-" * 40)
        
        try:
            from app.services.gpt4free_service import gpt4free_service
            
            if not gpt4free_service.is_available():
                print("⚠️ GPT4Free not available, skipping demo")
                print()
                return
            
            # Demo different types of generation
            demos = [
                ("Basic Shitpost", "romanian", 7),
                ("Gaming Meme", "gaming", 6),
                ("Philosophical Chaos", "philosophical", 8)
            ]
            
            for name, theme, chaos in demos:
                print(f"🎯 {name} (Theme: {theme}, Chaos: {chaos}):")
                
                try:
                    result = await gpt4free_service.generate_shitpost_content(
                        theme=theme,
                        chaos_level=chaos,
                        style="absurd"
                    )
                    
                    if result:
                        print(f"   {result}")
                        self.demo_outputs.append(f"GPT4Free {name}: {result}")
                    else:
                        print("   ⚠️ Generation returned None (possibly rate limited)")
                        
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                
                print()
            
        except ImportError:
            print("❌ GPT4Free not available")
            print()
    
    async def demo_ai_image_generation(self):
        """Demo AI image generation"""
        print("🎨 AI Image Generation Demo")
        print("-" * 35)
        
        try:
            from app.services.free_ai_services import free_ai_generator
            
            # Demo image generation
            prompts = [
                ("Romanian Meme", "confused romanian guy", "romanian"),
                ("Gaming Chaos", "gamer rage moment", "gaming"),
                ("Abstract Meme", "surreal internet humor", "chaos")
            ]
            
            for name, prompt, style in prompts:
                print(f"🖼️ {name}: '{prompt}' (Style: {style})")
                
                try:
                    image_data = await free_ai_generator.generate_meme_image(
                        prompt=prompt,
                        style=style,
                        chaos_level=5,
                        width=256,  # Small size for demo
                        height=256
                    )
                    
                    if image_data:
                        print(f"   ✅ Generated {len(image_data)} bytes")
                        self.demo_outputs.append(f"AI Image {name}: {len(image_data)} bytes")
                    else:
                        print("   ⚠️ Generation failed or rate limited")
                        
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                
                print()
            
        except ImportError:
            print("❌ Free AI services not available")
            print()
    
    async def demo_video_generation(self):
        """Demo video generation (basic)"""
        print("🎬 Video Generation Demo")
        print("-" * 30)
        
        try:
            from app.services.shitpost_generator import ShitpostGenerator
            generator = ShitpostGenerator()
            
            print("🎯 Generating sample shitpost video...")
            print("   Theme: romanian")
            print("   Chaos Level: 5")
            print("   Duration: 8 seconds")
            print()
            
            # Note: This might fail due to missing video dependencies
            # but we'll show what would happen
            try:
                result = generator.generate_random_shitpost(
                    theme="romanian",
                    duration=8,
                    chaos_level=5,
                    custom_text="Demo shitpost pentru testare"
                )
                
                if result['success']:
                    print("✅ Video generation successful!")
                    print(f"   Output: {result['output_path']}")
                    print(f"   Text: {result['text_generated']}")
                    print(f"   Size: {result.get('file_size', 0) / (1024*1024):.1f} MB")
                    self.demo_outputs.append(f"Video: {result['output_path']}")
                else:
                    print(f"❌ Video generation failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"⚠️ Video generation failed (expected if dependencies missing): {e}")
                print("   This is normal if FFmpeg/ImageMagick are not installed")
            
            print()
            
        except ImportError:
            print("❌ Shitpost generator not available")
            print()
    
    def demo_cli_usage(self):
        """Demo CLI usage examples"""
        print("💻 CLI Usage Examples")
        print("-" * 25)
        
        examples = [
            ("Basic Generation", "python shitpost_cli.py generate --theme romanian --chaos 8"),
            ("Batch Generation", "python shitpost_cli.py batch --count 5 --theme gaming"),
            ("AI Generation", "python shitpost_cli.py ai-generate --prompt 'confused guy' --style absurd"),
            ("Service Status", "python shitpost_cli.py config --test-services"),
            ("System Status", "python shitpost_cli.py status --verbose")
        ]
        
        for name, command in examples:
            print(f"🔧 {name}:")
            print(f"   {command}")
            print()
    
    def show_summary(self):
        """Show demo summary"""
        print("📊 Demo Summary")
        print("-" * 20)
        
        if self.demo_outputs:
            print("✅ Generated Content:")
            for i, output in enumerate(self.demo_outputs, 1):
                print(f"   {i}. {output[:80]}{'...' if len(output) > 80 else ''}")
        else:
            print("⚠️ No content generated (services may not be available)")
        
        print()
        print("🚀 Next Steps:")
        print("1. Install missing dependencies:")
        print("   pip install -U g4f[all] aiohttp psutil")
        print()
        print("2. Run setup script:")
        print("   python setup_shitpost_generator.py")
        print()
        print("3. Start web interface:")
        print("   python webui.bat  # Windows")
        print("   sh webui.sh       # Linux/Mac")
        print()
        print("4. Try the CLI:")
        print("   python shitpost_cli.py generate --theme romanian --chaos 8")
        print()
        print("📖 Read the documentation:")
        print("   See SHITPOST_GENERATOR_README.md for detailed usage")
    
    async def run_demo(self):
        """Run the complete demo"""
        self.print_header()
        
        # Check services
        services = self.check_services()
        
        # Run demos based on available services
        await self.demo_text_generation()
        
        if services['gpt4free']:
            await self.demo_gpt4free_generation()
        
        if services['free_ai_images']:
            await self.demo_ai_image_generation()
        
        if services['shitpost_generator']:
            await self.demo_video_generation()
        
        # Show CLI examples
        self.demo_cli_usage()
        
        # Show summary
        self.show_summary()

async def main():
    """Main demo function"""
    demo = ShitpostDemo()
    
    try:
        await demo.run_demo()
    except KeyboardInterrupt:
        print("\n⚠️ Demo cancelled by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
