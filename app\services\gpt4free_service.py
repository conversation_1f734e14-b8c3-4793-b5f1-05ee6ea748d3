#!/usr/bin/env python3
"""
GPT4Free Service for MoneyPrinterTurbo

This service provides free access to premium AI models through gpt4free.
"""

import asyncio
import time
import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from loguru import logger

try:
    from g4f.client import Client
    from g4f import Provider, Model
    GPT4FREE_AVAILABLE = True
except ImportError:
    GPT4FREE_AVAILABLE = False
    logger.warning("GPT4Free not installed. Run: pip install -U g4f[all]")


@dataclass
class GPT4FreeConfig:
    """Configuration for GPT4Free service"""
    enabled: bool = True
    default_model: str = "gpt-4o-mini"
    fallback_models: List[str] = None
    preferred_providers: List[str] = None
    web_search_enabled: bool = True
    max_retries: int = 3
    timeout: int = 30
    streaming: bool = False
    romanian_optimization: bool = True
    
    def __post_init__(self):
        if self.fallback_models is None:
            self.fallback_models = [
                "gpt-4o",
                "gpt-4",
                "gemini-pro",
                "claude-3-sonnet"
            ]
        
        if self.preferred_providers is None:
            self.preferred_providers = [
                "auto",
                "Bing",
                "ChatGPT",
                "Gemini"
            ]


class GPT4FreeService:
    """Service for interacting with GPT4Free models"""
    
    def __init__(self, config: GPT4FreeConfig = None):
        self.config = config or GPT4FreeConfig()
        self.client = None
        self.available_models = []
        self.available_providers = []
        
        if GPT4FREE_AVAILABLE and self.config.enabled:
            self._initialize_client()
        else:
            logger.warning("GPT4Free service disabled or not available")
    
    def _initialize_client(self):
        """Initialize GPT4Free client"""
        try:
            self.client = Client()
            self._load_available_models()
            self._load_available_providers()
            logger.info("✅ GPT4Free service initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize GPT4Free client: {e}")
            self.client = None
    
    def _load_available_models(self):
        """Load available models from GPT4Free"""
        try:
            # Common models available in GPT4Free
            self.available_models = [
                "gpt-4o",
                "gpt-4o-mini", 
                "gpt-4",
                "gpt-3.5-turbo",
                "gemini-pro",
                "gemini-2.5",
                "claude-3-sonnet",
                "claude-3-haiku",
                "deepseek-r1",
                "llama-3.1-70b"
            ]
            logger.info(f"📋 Loaded {len(self.available_models)} available models")
        except Exception as e:
            logger.error(f"❌ Failed to load available models: {e}")
    
    def _load_available_providers(self):
        """Load available providers from GPT4Free"""
        try:
            # Common providers available in GPT4Free
            self.available_providers = [
                "auto",
                "Bing",
                "ChatGPT", 
                "Gemini",
                "Claude",
                "DeepSeek",
                "Perplexity"
            ]
            logger.info(f"🔌 Loaded {len(self.available_providers)} available providers")
        except Exception as e:
            logger.error(f"❌ Failed to load available providers: {e}")
    
    def is_available(self) -> bool:
        """Check if GPT4Free service is available"""
        return GPT4FREE_AVAILABLE and self.config.enabled and self.client is not None
    
    async def generate_text(self, 
                          prompt: str, 
                          model: str = None,
                          system_prompt: str = None,
                          max_tokens: int = 2000,
                          temperature: float = 0.7) -> Optional[str]:
        """Generate text using GPT4Free"""
        
        if not self.is_available():
            logger.warning("GPT4Free service not available")
            return None
        
        model = model or self.config.default_model
        
        # Prepare messages
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # Add Romanian optimization if enabled
        if self.config.romanian_optimization and "română" in prompt.lower():
            romanian_system = "Răspunde în română cu un stil natural și captivant. Folosește expresii românești autentice."
            if system_prompt:
                messages[0]["content"] += f" {romanian_system}"
            else:
                messages.append({"role": "system", "content": romanian_system})
        
        messages.append({"role": "user", "content": prompt})
        
        # Try with primary model first, then fallbacks
        models_to_try = [model] + [m for m in self.config.fallback_models if m != model]
        
        for attempt_model in models_to_try:
            try:
                logger.info(f"🤖 Generating text with model: {attempt_model}")
                
                response = self.client.chat.completions.create(
                    model=attempt_model,
                    messages=messages,
                    web_search=self.config.web_search_enabled,
                    stream=self.config.streaming
                )
                
                if response and response.choices:
                    content = response.choices[0].message.content
                    logger.info(f"✅ Generated {len(content)} characters with {attempt_model}")
                    return content
                
            except Exception as e:
                logger.warning(f"⚠️ Model {attempt_model} failed: {e}")
                continue
        
        logger.error("❌ All models failed to generate text")
        return None
    
    async def generate_script(self, 
                            subject: str, 
                            style: str = "captivant",
                            length: str = "mediu",
                            language: str = "română") -> Optional[str]:
        """Generate video script using GPT4Free"""
        
        system_prompt = f"""
        Ești un expert în crearea de scripturi pentru videoclipuri virale pe social media.
        Creează scripturi captivante, emoționante și engaging pentru platforma TikTok/YouTube Shorts.
        
        Stilul cerut: {style}
        Lungimea: {length}
        Limba: {language}
        
        Instrucțiuni:
        - Folosește un ton conversațional și autentic
        - Adaugă elemente de suspans și intrigue
        - Include call-to-action natural
        - Folosește expresii românești autentice
        - Structurează conținutul pentru a capta atenția din primele secunde
        """
        
        prompt = f"""
        Creează un script captivant pentru un videoclip despre: {subject}
        
        Scriptul trebuie să fie:
        - Captivant din primele secunde
        - Emoționant și relatable
        - Structurat pentru social media (30-60 secunde)
        - Cu un final memorabil
        
        Subiectul: {subject}
        """
        
        return await self.generate_text(
            prompt=prompt,
            system_prompt=system_prompt,
            max_tokens=1500,
            temperature=0.8
        )
    
    async def generate_terms(self, subject: str, count: int = 5) -> Optional[List[str]]:
        """Generate search terms using GPT4Free"""
        
        prompt = f"""
        Generează {count} termeni de căutare relevanți pentru subiectul: {subject}
        
        Termenii trebuie să fie:
        - Relevanți pentru găsirea de videoclipuri stock
        - În engleză (pentru compatibilitate cu platformele de stock)
        - Descriptivi și specifici
        - Potriviți pentru conținut video
        
        Returnează doar termenii, câte unul pe linie, fără numerotare.
        """
        
        response = await self.generate_text(
            prompt=prompt,
            max_tokens=200,
            temperature=0.6
        )
        
        if response:
            terms = [term.strip() for term in response.split('\n') if term.strip()]
            return terms[:count]
        
        return None
    
    async def improve_content(self, content: str, improvement_type: str = "general") -> Optional[str]:
        """Improve existing content using GPT4Free"""

        improvement_prompts = {
            "general": "Îmbunătățește acest conținut făcându-l mai captivant și engaging:",
            "romanian": "Îmbunătățește acest text în română, făcându-l mai natural și autentic:",
            "viral": "Transformă acest conținut pentru a fi mai viral pe social media:",
            "emotional": "Adaugă mai multă emoție și impact emoțional la acest conținut:"
        }

        prompt = f"""
        {improvement_prompts.get(improvement_type, improvement_prompts['general'])}

        Conținut original:
        {content}

        Îmbunătățiri necesare:
        - Mai mult impact emoțional
        - Limbaj mai captivant
        - Structură mai bună
        - Flow mai natural
        """

        return await self.generate_text(
            prompt=prompt,
            max_tokens=2000,
            temperature=0.7
        )

    async def generate_shitpost_content(self,
                                      theme: str = "random",
                                      chaos_level: int = 5,
                                      style: str = "absurd",
                                      language: str = "română") -> Optional[str]:
        """Generate shitpost content specifically optimized for viral memes"""

        # Theme-specific prompts for better content generation
        theme_prompts = {
            "romanian": """
            Creează un meme românesc viral cu umor local și referințe culturale.
            Include elemente specifice culturii românești: bunica, mâncare, școală, vecini, etc.
            Folosește expresii românești autentice și situații relatable.
            """,
            "gaming": """
            Creează un meme gaming viral cu referințe la jocuri populare.
            Include situații comune din gaming: rage quit, noob mistakes, pro plays, etc.
            Folosește slang-ul gamerilor și referințe la cultura gaming.
            """,
            "philosophical": """
            Creează un meme filozofic absurd cu "shower thoughts" și realizări existențiale.
            Include concepte profunde prezentate într-un mod amuzant și relatable.
            Folosește formatul "big brain" sau "galaxy brain" pentru idei.
            """,
            "chaos": """
            Creează conținut complet absurd și haotic pentru maximum viral potential.
            Include elemente random, non-sequitur și humor surreal.
            Combină concepte care nu au legătură între ele într-un mod amuzant.
            """
        }

        # Chaos level modifiers
        chaos_modifiers = {
            1: "ușor amuzant și relatable",
            2: "moderat amuzant cu twist-uri neașteptate",
            3: "amuzant cu elemente absurde",
            4: "absurd și haotic",
            5: "foarte absurd cu humor surreal",
            6: "extrem de haotic și random",
            7: "complet nebun și imprevizibil",
            8: "fever dream level absurdity",
            9: "realitate alternativă bizară",
            10: "chaos interdimensional total"
        }

        base_prompt = theme_prompts.get(theme, theme_prompts["chaos"])
        chaos_modifier = chaos_modifiers.get(chaos_level, chaos_modifiers[5])

        system_prompt = f"""
        Ești un expert în crearea de conținut viral pentru meme-uri și shitpost-uri.
        Specializarea ta este să creezi conținut {chaos_modifier} care devine viral pe social media.

        Instrucțiuni pentru conținut:
        - Folosește formatul clasic de meme (setup + punchline)
        - Include referințe culturale relevante
        - Creează situații relatable dar absurde
        - Folosește timing perfect pentru impact maxim
        - Limba: {language}
        - Stil: {style}
        """

        prompt = f"""
        {base_prompt}

        Creează un text pentru meme/shitpost cu următoarele caracteristici:
        - Tema: {theme}
        - Nivel de chaos: {chaos_level}/10 ({chaos_modifier})
        - Stil: {style}

        Formatul poate fi:
        - "Nimeni:\nAbsolut nimeni:\nEu: [acțiune absurdă]"
        - "POV: [situație] și [reacție neașteptată]"
        - "Când [situație normală] dar [twist absurd]"
        - Sau orice format viral popular

        Textul trebuie să fie scurt, punchant și memorabil!
        """

        return await self.generate_text(
            prompt=prompt,
            system_prompt=system_prompt,
            max_tokens=500,
            temperature=0.8 + (chaos_level * 0.02)  # Higher chaos = more creativity
        )

    async def generate_meme_caption(self,
                                   image_description: str = "",
                                   meme_format: str = "classic",
                                   context: str = "") -> Optional[str]:
        """Generate caption for meme images"""

        format_templates = {
            "classic": "Text de sus / Text de jos format clasic",
            "drake": "Respinge: [ceva] / Preferă: [altceva]",
            "expanding_brain": "Progresie de la simplu la complex în 4 pași",
            "distracted_boyfriend": "Situație de alegere între două opțiuni",
            "woman_yelling_cat": "Conflict între două perspective",
            "this_is_fine": "Situație haotică prezentată ca normală"
        }

        system_prompt = f"""
        Ești expert în crearea de caption-uri pentru meme-uri virale.
        Specializarea ta este să creezi texte scurte, punchante și memorabile.

        Format meme: {meme_format}
        Template: {format_templates.get(meme_format, "Format liber")}
        """

        prompt = f"""
        Creează un caption viral pentru un meme cu următoarele caracteristici:

        Format: {meme_format}
        Descriere imagine: {image_description}
        Context: {context}

        Caption-ul trebuie să fie:
        - Scurt și punchant
        - Relatable pentru audiența română
        - Viral potential maxim
        - Potrivit pentru formatul {meme_format}

        Returnează doar textul caption-ului, fără explicații.
        """

        return await self.generate_text(
            prompt=prompt,
            system_prompt=system_prompt,
            max_tokens=200,
            temperature=0.7
        )

    async def generate_viral_hashtags(self,
                                    content: str,
                                    platform: str = "tiktok",
                                    niche: str = "general") -> Optional[List[str]]:
        """Generate viral hashtags for social media content"""

        platform_strategies = {
            "tiktok": "Hashtag-uri TikTok cu mix de trending și niche",
            "instagram": "Hashtag-uri Instagram cu focus pe engagement",
            "youtube": "Hashtag-uri YouTube pentru discoverability",
            "facebook": "Hashtag-uri Facebook pentru reach organic"
        }

        prompt = f"""
        Generează 10-15 hashtag-uri virale pentru următorul conținut:

        Conținut: {content}
        Platformă: {platform}
        Nișă: {niche}

        Strategia pentru {platform}: {platform_strategies.get(platform, "Strategie generală")}

        Include:
        - 3-5 hashtag-uri trending generale
        - 3-5 hashtag-uri specifice nișei
        - 2-3 hashtag-uri românești
        - 2-3 hashtag-uri de comunitate

        Returnează hashtag-urile separate prin virgulă, fără explicații.
        """

        response = await self.generate_text(
            prompt=prompt,
            max_tokens=300,
            temperature=0.6
        )

        if response:
            # Parse hashtags from response
            hashtags = [tag.strip().replace('#', '') for tag in response.split(',')]
            hashtags = [f"#{tag}" for tag in hashtags if tag and len(tag) > 1]
            return hashtags[:15]  # Limit to 15 hashtags

        return None

    async def enhance_shitpost_for_virality(self,
                                          original_content: str,
                                          target_audience: str = "gen_z_romania") -> Optional[str]:
        """Enhance shitpost content specifically for viral potential"""

        audience_profiles = {
            "gen_z_romania": "Generația Z din România - umor absurd, referințe internet, slang modern",
            "millennials_romania": "Millennials români - nostalgie, referințe culturale, umor self-deprecating",
            "gaming_community": "Comunitatea gaming - referințe la jocuri, meme-uri gaming, cultura online",
            "general_romanian": "Audiență românească generală - umor universal, referințe culturale comune"
        }

        system_prompt = f"""
        Ești un expert în optimizarea conținutului pentru viral potential maxim.
        Specializarea ta este să transformi conținut obișnuit în meme-uri virale.

        Audiența țintă: {target_audience}
        Profil: {audience_profiles.get(target_audience, "Audiență generală")}
        """

        prompt = f"""
        Transformă următorul conținut pentru a avea viral potential maxim:

        Conținut original: {original_content}

        Optimizează pentru:
        - Timing perfect (setup + punchline)
        - Referințe culturale relevante pentru {target_audience}
        - Format viral (POV, "Nimeni:", etc.)
        - Limbaj modern și relatable
        - Hook puternic în primele cuvinte

        Păstrează esența originalului dar fă-l mai viral și engaging.
        Returnează doar versiunea optimizată.
        """

        return await self.generate_text(
            prompt=prompt,
            system_prompt=system_prompt,
            max_tokens=400,
            temperature=0.75
        )

    def get_status(self) -> Dict[str, Any]:
        """Get service status information"""
        return {
            "available": self.is_available(),
            "gpt4free_installed": GPT4FREE_AVAILABLE,
            "enabled": self.config.enabled,
            "default_model": self.config.default_model,
            "available_models": self.available_models,
            "available_providers": self.available_providers,
            "romanian_optimization": self.config.romanian_optimization
        }


# Global service instance
gpt4free_service = GPT4FreeService()


async def test_gpt4free_service():
    """Test the GPT4Free service"""
    print("🧪 Testing GPT4Free Service...")
    
    if not gpt4free_service.is_available():
        print("❌ GPT4Free service not available")
        return False
    
    # Test text generation
    response = await gpt4free_service.generate_text(
        "Spune-mi o poveste scurtă despre o aventură în România"
    )
    
    if response:
        print("✅ Text generation successful!")
        print(f"📝 Response: {response[:100]}...")
        return True
    else:
        print("❌ Text generation failed")
        return False


if __name__ == "__main__":
    asyncio.run(test_gpt4free_service())
