"""
Music Video Analyzer Service
Analizează videoclipuri muzicale pentru detectarea genului, beat-urilor și momentelor emoționale
"""

import os
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from moviepy.editor import VideoFileClip
import tempfile

logger = logging.getLogger(__name__)

class MusicVideoAnalyzer:
    """Analizor pentru videoclipuri muzicale"""
    
    def __init__(self):
        self.genre_keywords = self._load_genre_keywords()
        self.emotional_keywords = self._load_emotional_keywords()
        
    def _load_genre_keywords(self) -> Dict[str, List[str]]:
        """Încarcă cuvintele cheie pentru detectarea genurilor"""
        return {
            'manele': [
                'dragoste', 'inimă', 'suflet', 'viață', 'iubire', 'pentru totdeauna',
                'nu te las', 'te iubesc', 'dragostea mea', 'inima mea'
            ],
            'rock': [
                'freedom', 'fight', 'power', 'rebel', 'break', 'strong', 'fire',
                'libertate', 'luptă', 'putere', 'rebel', 'foc', 'tare'
            ],
            'hip_hop': [
                'real', 'street', 'hustle', 'money', 'respect', 'game', 'flow',
                'adevărat', 'stradă', 'bani', 'respect', 'joc', 'flow'
            ],
            'pop': [
                'love', 'heart', 'forever', 'together', 'dream', 'tonight',
                'iubire', 'inimă', 'pentru totdeauna', 'împreună', 'vis', 'în seara asta'
            ],
            'edm': [
                'energy', 'dance', 'feel', 'tonight', 'party', 'beat', 'drop',
                'energie', 'dans', 'simte', 'petrecere', 'ritm'
            ]
        }
    
    def _load_emotional_keywords(self) -> List[str]:
        """Încarcă cuvintele cu încărcătură emoțională"""
        return [
            'dragoste', 'iubire', 'inimă', 'suflet', 'lacrimi', 'durere',
            'fericire', 'tristețe', 'speranță', 'vis', 'amintiri', 'pentru totdeauna',
            'love', 'heart', 'soul', 'tears', 'pain', 'happiness', 'sadness',
            'hope', 'dream', 'memories', 'forever'
        ]
    
    def analyze_video(self, video_path: str) -> Dict:
        """
        Analizează complet un videoclip muzical
        
        Args:
            video_path: Calea către videoclip
            
        Returns:
            Dict cu toate analizele
        """
        logger.info(f"🎵 Începe analiza videoclipului: {video_path}")
        
        try:
            # Încarcă videoclipul
            video = VideoFileClip(video_path)
            
            # Analize de bază
            audio_analysis = self._analyze_audio_basic(video)
            
            # Detectare gen
            genre_info = self._detect_genre_basic(audio_analysis)
            
            # Analiză versuri simplă
            lyrics_analysis = self._analyze_lyrics_basic()
            
            # Detectare momente emoționale
            emotional_peaks = self._detect_emotional_peaks_basic(audio_analysis)
            
            # Analiză structură melodie
            song_structure = self._analyze_song_structure_basic(audio_analysis)
            
            # Cleanup
            video.close()
            
            result = {
                'genre': genre_info,
                'audio': audio_analysis,
                'lyrics': lyrics_analysis,
                'emotional_peaks': emotional_peaks,
                'song_structure': song_structure,
                'duration': video.duration,
                'fps': video.fps,
                'size': video.size
            }
            
            logger.info(f"✅ Analiza completă pentru gen: {genre_info['primary_genre']}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Eroare în analiza video: {str(e)}")
            # Returnează rezultat de fallback
            return self._get_fallback_analysis()
    
    def _analyze_audio_basic(self, video: VideoFileClip) -> Dict:
        """Analizează caracteristicile audio de bază"""
        logger.info("🎵 Analizez caracteristicile audio...")
        
        try:
            duration = video.duration
            
            # Simulează detectarea beat-urilor
            tempo = 120.0  # BPM default
            beat_times = [i * (60.0 / tempo) for i in range(int(duration * tempo / 60))]
            
            # Simulează energia
            energy_timeline = [0.5 + 0.3 * np.sin(i * 0.1) for i in range(int(duration * 10))]
            
            return {
                'tempo': tempo,
                'beat_times': beat_times[:50],  # Maxim 50 beat-uri
                'energy_timeline': energy_timeline,
                'duration': duration,
                'sample_rate': 44100
            }
            
        except Exception as e:
            logger.warning(f"Eroare în analiza audio: {str(e)}")
            return {
                'tempo': 120.0,
                'beat_times': [1.0, 2.0, 3.0, 4.0],
                'energy_timeline': [0.5, 0.6, 0.7, 0.5],
                'duration': 30.0,
                'sample_rate': 44100
            }
    
    def _detect_genre_basic(self, audio_analysis: Dict) -> Dict:
        """Detectează genul muzical de bază"""
        logger.info("🎯 Detectez genul muzical...")
        
        tempo = audio_analysis['tempo']
        energy_avg = np.mean(audio_analysis['energy_timeline'])
        
        # Reguli simple pentru detectarea genului
        if 120 <= tempo <= 140 and energy_avg > 0.5:
            primary_genre = 'manele'
            confidence = 0.7
        elif tempo > 140 and energy_avg > 0.6:
            primary_genre = 'edm'
            confidence = 0.8
        elif tempo < 100 and energy_avg < 0.4:
            primary_genre = 'pop'
            confidence = 0.6
        elif energy_avg > 0.7:
            primary_genre = 'rock'
            confidence = 0.7
        else:
            primary_genre = 'hip_hop'
            confidence = 0.5
        
        return {
            'primary_genre': primary_genre,
            'confidence': confidence,
            'tempo': tempo,
            'energy_level': energy_avg
        }
    
    def _analyze_lyrics_basic(self) -> Dict:
        """Analizează versurile de bază"""
        logger.info("📝 Analizez versurile...")
        
        # Pentru acum returnează date simulate
        return {
            'text': '',
            'sentiment': 0.0,
            'emotional_words': [],
            'word_count': 0,
            'has_lyrics': False
        }
    
    def _detect_emotional_peaks_basic(self, audio_analysis: Dict) -> List[Dict]:
        """Detectează momentele cu încărcătură emoțională"""
        logger.info("💫 Detectez momentele emoționale...")
        
        energy_timeline = audio_analysis['energy_timeline']
        beat_times = audio_analysis['beat_times']
        
        peaks = []
        
        # Găsește vârfurile de energie
        if len(energy_timeline) > 0:
            energy_threshold = np.mean(energy_timeline) + 0.1
            
            for i, energy in enumerate(energy_timeline):
                if energy > energy_threshold and len(peaks) < 5:
                    time_pos = i * (audio_analysis['duration'] / len(energy_timeline))
                    
                    # Găsește cel mai apropiat beat
                    if beat_times:
                        closest_beat = min(beat_times, key=lambda x: abs(x - time_pos))
                    else:
                        closest_beat = time_pos
                    
                    peaks.append({
                        'time': time_pos,
                        'beat_time': closest_beat,
                        'energy_level': float(energy),
                        'type': 'energy_peak'
                    })
        
        return peaks
    
    def _analyze_song_structure_basic(self, audio_analysis: Dict) -> Dict:
        """Analizează structura melodiei de bază"""
        logger.info("🎼 Analizez structura melodiei...")
        
        duration = audio_analysis['duration']
        
        # Structură simplă
        sections = [
            {'start_time': 0, 'end_time': duration * 0.2, 'type': 'intro', 'energy_level': 0.3},
            {'start_time': duration * 0.2, 'end_time': duration * 0.5, 'type': 'verse', 'energy_level': 0.5},
            {'start_time': duration * 0.5, 'end_time': duration * 0.8, 'type': 'chorus', 'energy_level': 0.8},
            {'start_time': duration * 0.8, 'end_time': duration, 'type': 'outro', 'energy_level': 0.4}
        ]
        
        return {
            'sections': sections,
            'total_duration': duration,
            'estimated_structure': ['intro', 'verse', 'chorus', 'outro']
        }
    
    def _get_fallback_analysis(self) -> Dict:
        """Returnează o analiză de fallback în caz de eroare"""
        return {
            'genre': {
                'primary_genre': 'pop',
                'confidence': 0.5,
                'tempo': 120.0,
                'energy_level': 0.5
            },
            'audio': {
                'tempo': 120.0,
                'beat_times': [1.0, 2.0, 3.0, 4.0],
                'energy_timeline': [0.5, 0.6, 0.7, 0.5],
                'duration': 30.0,
                'sample_rate': 44100
            },
            'lyrics': {
                'text': '',
                'sentiment': 0.0,
                'emotional_words': [],
                'word_count': 0,
                'has_lyrics': False
            },
            'emotional_peaks': [
                {'time': 10.0, 'beat_time': 10.0, 'energy_level': 0.8, 'type': 'energy_peak'}
            ],
            'song_structure': {
                'sections': [
                    {'start_time': 0, 'end_time': 10, 'type': 'intro', 'energy_level': 0.3},
                    {'start_time': 10, 'end_time': 20, 'type': 'verse', 'energy_level': 0.5},
                    {'start_time': 20, 'end_time': 30, 'type': 'chorus', 'energy_level': 0.8}
                ],
                'total_duration': 30.0,
                'estimated_structure': ['intro', 'verse', 'chorus']
            },
            'duration': 30.0,
            'fps': 30.0,
            'size': (1080, 1920)
        }
