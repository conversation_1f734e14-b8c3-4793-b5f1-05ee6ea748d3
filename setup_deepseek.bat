@echo off
echo ========================================
echo Configurare DeepSeek API (GRATUIT!)
echo ========================================
echo.

echo DeepSeek ofera API GRATUIT pentru generarea de scripturi!
echo.
echo Pasii pentru configurare:
echo 1. Mergeti la: https://platform.deepseek.com/
echo 2. Creati un cont gratuit
echo 3. Mergeti la API Keys: https://platform.deepseek.com/api_keys
echo 4. Creati o cheie API noua
echo 5. Copiati cheia API
echo.

set /p api_key="Introduceti cheia API DeepSeek: "

if "%api_key%"=="" (
    echo Eroare: Nu ati introdus cheia API!
    pause
    goto :eof
)

echo.
echo Configurez DeepSeek ca furnizor LLM...

powershell -Command "(Get-Content config.toml) -replace 'llm_provider = \".*\"', 'llm_provider = \"deepseek\"' | Set-Content config.toml"
powershell -Command "(Get-Content config.toml) -replace 'deepseek_api_key = \".*\"', 'deepseek_api_key = \"%api_key%\"' | Set-Content config.toml"

echo.
echo ✅ DeepSeek a fost configurat cu succes!
echo.
echo Avantajele DeepSeek:
echo - API GRATUIT (fara costuri)
echo - Foarte rapid si fiabil
echo - Suporta limba romana excelent
echo - Nu necesita VPN
echo.
echo Pentru a testa configurarea:
echo 1. Restartati aplicatia: .\start_webui.bat
echo 2. Introduceti un subiect in romana
echo 3. Generati scriptul video
echo.
pause
