#!/usr/bin/env python3
"""
Streamlit Wrapper for Shitpost Generator
Handles async operations safely
"""

import os
import sys
import asyncio
import warnings
from pathlib import Path

# Suppress specific warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*WebSocketClosedError.*")

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

def setup_asyncio():
    """Setup asyncio to work better with Streamlit"""
    try:
        # Set event loop policy for Windows
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # Create and set a new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
    except Exception as e:
        print(f"Warning: Could not setup asyncio: {e}")

def main():
    """Main wrapper function"""
    print("🤖 Starting Enhanced Shitpost Generator Web Interface...")
    print("🔧 Setting up async environment...")
    
    # Setup asyncio
    setup_asyncio()
    
    # Import and run Streamlit
    try:
        import streamlit.web.cli as stcli
        import streamlit as st
        
        # Set page config early
        try:
            st.set_page_config(
                page_title="Enhanced Shitpost Generator",
                page_icon="🤖",
                layout="wide",
                initial_sidebar_state="auto"
            )
        except:
            pass  # Ignore if already set
        
        # Run the main webui
        webui_path = str(Path(__file__).parent / "webui" / "Main.py")
        
        if os.path.exists(webui_path):
            print(f"✅ Starting web interface: {webui_path}")
            sys.argv = ["streamlit", "run", webui_path, "--server.address", "localhost"]
            stcli.main()
        else:
            print(f"❌ WebUI not found: {webui_path}")
            print("Make sure you're running from the project root directory")
            
    except ImportError as e:
        print(f"❌ Streamlit not available: {e}")
        print("Install with: pip install streamlit")
    except Exception as e:
        print(f"❌ Failed to start web interface: {e}")

if __name__ == "__main__":
    main()
