#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for LLM connection in MoneyPrinterTurbo
"""

import os
import sys
from pathlib import Path

# Add the root directory to the path
root_dir = Path(__file__).parent
sys.path.append(str(root_dir))

from app.services import llm
from app.config import config

def test_llm_connection():
    """Test LLM connection and script generation"""
    print("🧪 Testing LLM Connection")
    print("=" * 50)
    
    # Check current LLM provider
    llm_provider = config.app.get("llm_provider", "openai")
    print(f"📡 Current LLM Provider: {llm_provider}")
    
    # Check API key configuration (G4F doesn't need API key)
    if llm_provider == "g4f":
        print("✅ G4F provider selected (no API key required)")
    else:
        api_key_field = f"{llm_provider}_api_key"
        api_key = config.app.get(api_key_field, "")

        if not api_key:
            print(f"❌ No API key configured for {llm_provider}")
            print(f"   Please set '{api_key_field}' in config.toml")
            return False
        else:
            print(f"✅ API key configured for {llm_provider}")
            print(f"   Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")
    
    # Test script generation
    print(f"\n🎬 Testing script generation...")
    test_subject = "Frumusețile României"
    
    try:
        print(f"   Subject: '{test_subject}'")
        print("   Generating script...")
        
        script = llm.generate_script(
            video_subject=test_subject,
            language="ro-RO",
            paragraph_number=1
        )
        
        if script and len(script.strip()) > 0 and not script.startswith("Error:"):
            print("✅ Script generation successful!")
            print(f"   Generated script: '{script[:100]}...'")
            
            # Check for Romanian content
            romanian_chars = ['ă', 'â', 'î', 'ș', 'ț']
            found_diacritics = [char for char in romanian_chars if char.lower() in script.lower()]
            
            if found_diacritics:
                print(f"   ✅ Romanian diacritics found: {found_diacritics}")
            else:
                print("   ℹ️ No Romanian diacritics detected (may be normal)")
            
            return True
        else:
            print("❌ Script generation failed")
            print(f"   Response: {script}")
            return False
            
    except Exception as e:
        print(f"❌ Script generation error: {e}")
        return False

def provide_solutions():
    """Provide solutions for common LLM issues"""
    print("\n🔧 Common Solutions:")
    print("=" * 50)
    
    print("\n1. 🆓 Use DeepSeek (FREE API):")
    print("   - Run: .\\setup_deepseek.bat")
    print("   - Register at: https://platform.deepseek.com/")
    print("   - Get API key: https://platform.deepseek.com/api_keys")
    print("   - Advantages: Free, fast, excellent Romanian support")
    
    print("\n2. 🔑 Fix OpenAI Configuration:")
    print("   - Get API key: https://platform.openai.com/api-keys")
    print("   - Set in Basic Settings panel or config.toml")
    print("   - Note: OpenAI requires payment after free tier")
    
    print("\n3. 🌙 Try Moonshot (Chinese provider):")
    print("   - Register at: https://platform.moonshot.cn/")
    print("   - Often has free credits")
    print("   - Good for Chinese and English content")
    
    print("\n4. 🔄 Alternative providers:")
    print("   - Qwen (Alibaba): https://dashscope.console.aliyun.com/")
    print("   - Gemini (Google): https://makersuite.google.com/")
    print("   - G4F (Free GPT): No API key needed")

def main():
    """Run LLM connection test"""
    print("🔍 MoneyPrinterTurbo LLM Connection Test")
    print("=" * 60)
    
    # Test connection
    success = test_llm_connection()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    if success:
        print("✅ LLM connection is working correctly!")
        print("   You can now generate video scripts in Romanian.")
        print("   Try creating a video with a Romanian subject.")
    else:
        print("❌ LLM connection failed!")
        print("   Please configure a working LLM provider.")
        provide_solutions()
    
    print(f"\n🌐 Access the interface at: http://localhost:8501")

if __name__ == "__main__":
    main()
