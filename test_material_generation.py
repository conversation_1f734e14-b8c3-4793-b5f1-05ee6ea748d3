#!/usr/bin/env python3
"""
Test Material Generation

Tests the material generation functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))


async def test_material_generation():
    """Test material generation functionality"""
    print("🔧 Testing Material Generation...")
    print("=" * 50)
    
    try:
        # Test 1: Import material functions
        print("1️⃣ Testing material service imports...")
        
        from app.services.material import download_videos
        from app.services.task import get_video_materials, generate_ai_video_materials
        
        print("✅ download_videos imported successfully")
        print("✅ get_video_materials imported successfully") 
        print("✅ generate_ai_video_materials imported successfully")
        
        # Test 2: Test basic material download
        print("\n2️⃣ Testing basic material download...")
        
        from app.models.schema import VideoAspect, VideoConcatMode
        
        try:
            # Test with simple search terms
            materials = download_videos(
                task_id="test_task_123",
                search_terms=["motivation", "success"],
                source="pexels",
                video_aspect=VideoAspect.portrait,
                video_contact_mode=VideoConcatMode.random,
                max_clip_duration=5,
                max_videos=2
            )
            
            if materials:
                print(f"✅ Downloaded {len(materials)} materials")
                for i, material in enumerate(materials):
                    print(f"   Material {i+1}: {material.url if hasattr(material, 'url') else 'No URL'}")
            else:
                print("⚠️ No materials downloaded (might be API key issue)")
                
        except Exception as e:
            print(f"⚠️ Material download error: {e}")
        
        # Test 3: Test VideoParams creation
        print("\n3️⃣ Testing VideoParams creation...")
        
        from app.models.schema import VideoParams
        
        video_params = VideoParams(
            video_subject="Test Subject",
            video_script="Test script for video generation",
            video_terms=["test", "motivation"],
            video_aspect=VideoAspect.portrait,
            video_concat_mode=VideoConcatMode.random,
            video_clip_duration=5,
            video_count=1,
            video_language="ro",
            video_source="pexels",
            voice_name="ro-RO-AlinaNeural",
            voice_rate=1.0,
            voice_volume=1.0,
            bgm_type="random",
            bgm_volume=0.2,
            subtitle_enabled=True,
            subtitle_position="bottom",
            font_name="Arial",
            text_fore_color="#FFFFFF",
            text_background_color="#000000",
            font_size=60,
            stroke_color="#000000",
            stroke_width=2,
            n_threads=2,
            paragraph_number=3
        )
        
        print("✅ VideoParams created successfully")
        print(f"   Video source: {video_params.video_source}")
        print(f"   Voice rate: {video_params.voice_rate}")
        print(f"   Voice volume: {video_params.voice_volume}")
        
        # Test 4: Test the enhanced material generation function
        print("\n4️⃣ Testing enhanced material generation...")
        
        from app.services.one_click_viral_generator import OneClickViralGenerator
        
        generator = OneClickViralGenerator()
        
        # Test the material generation method
        try:
            materials = await generator._generate_materials(
                task_id="test_task_456",
                video_params=video_params,
                contextual_image_plan=None,
                config=None,
                audio_duration=60.0
            )
            
            if materials:
                print(f"✅ Generated {len(materials)} materials using enhanced method")
            elif materials == []:
                print("⚠️ Enhanced method returned empty list (might be API key issue)")
            else:
                print("❌ Enhanced method returned None")
                
        except Exception as e:
            print(f"❌ Enhanced material generation error: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 50)
        print("📊 MATERIAL GENERATION TEST SUMMARY:")
        print("=" * 50)
        print("✅ All imports working correctly")
        print("✅ VideoParams creation successful")
        print("✅ Basic material functions accessible")
        print("⚠️ Material download depends on API keys")
        
        print("\n🎉 Material generation test completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    try:
        success = asyncio.run(test_material_generation())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
