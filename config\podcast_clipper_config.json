{"podcast_clipper": {"enabled": true, "version": "1.0.0", "description": "Configurație pentru funcționalitatea Podcast Clipper", "detection": {"confidence_threshold": 0.5, "max_people": 2, "yolo_model_path": "models/yolov3.weights", "yolo_config_path": "models/yolov3.cfg", "coco_names_path": "models/coco.names", "use_gpu": true, "detection_interval": 1.0}, "audio": {"whisper_model": "base", "language": "auto", "enable_enhancement": true, "noise_reduction": true, "normalize_audio": true, "sample_rate": 16000}, "clips": {"default_duration": 30, "min_speaker_time": 5, "max_clips_per_session": 50, "overlap_threshold": 0.1, "quality_threshold": 0.6}, "video": {"output_resolution": "1080x1920", "frame_rate": 30, "video_quality": "High", "codec": "libx264", "bitrate": "2M", "aspect_ratio": "9:16"}, "captions": {"enabled": true, "style": "Modern", "highlight_words": true, "font_family": "<PERSON><PERSON>", "font_size": 60, "font_color": "#FFFFFF", "highlight_color": "#00FF00", "stroke_color": "#000000", "stroke_width": 2, "position": "bottom", "margin": 50}, "output": {"directory": "outputs/podcast_clips", "filename_template": "clip_{index:03d}_{speaker_id}_{timestamp}.mp4", "create_thumbnails": true, "generate_metadata": true, "export_transcript": true}, "performance": {"batch_size": 4, "max_memory_usage": "8GB", "use_multiprocessing": true, "num_workers": 2, "cache_enabled": true, "cache_size": "1GB"}, "advanced": {"speaker_diarization": {"enabled": true, "min_speakers": 1, "max_speakers": 5, "clustering_threshold": 0.7}, "face_detection": {"enabled": false, "confidence_threshold": 0.8, "use_mediapipe": true}, "scene_detection": {"enabled": false, "threshold": 0.3, "min_scene_length": 2.0}}, "ui": {"show_preview": true, "auto_play_clips": false, "progress_updates": true, "show_debug_info": false, "theme": "dark"}, "logging": {"level": "INFO", "log_file": "logs/podcast_clipper.log", "max_log_size": "10MB", "backup_count": 5}}}