"""
Meme Template System for Popular Formats
Programmatic generation of popular meme layouts with AI content
"""

import os
import json
from typing import Dict, List, Tuple, Optional, Any
from PIL import Image, ImageDraw, ImageFont
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class TextPosition:
    """Position and styling for text overlay"""
    x: float  # Percentage of image width (0-100)
    y: float  # Percentage of image height (0-100)
    width: float  # Percentage of image width for text box
    height: float  # Percentage of image height for text box
    font_size: int = 40
    color: str = "white"
    stroke_color: str = "black"
    stroke_width: int = 2
    alignment: str = "center"  # left, center, right

@dataclass
class MemeTemplateConfig:
    """Configuration for a meme template"""
    name: str
    description: str
    layout_type: str  # single, two_panel, three_panel, four_panel
    text_positions: List[TextPosition]
    background_color: str = "white"
    requires_base_image: bool = False
    base_image_path: Optional[str] = None
    popularity: int = 5  # 1-10 scale

class MemeTemplateEngine:
    """Engine for applying text to meme templates"""
    
    def __init__(self, templates_dir: str = "storage/meme_templates"):
        self.templates_dir = templates_dir
        os.makedirs(templates_dir, exist_ok=True)
        self.templates = self._load_templates()
        self.default_font_path = self._get_default_font()
    
    def _get_default_font(self) -> str:
        """Get default font path, fallback to system fonts"""
        try:
            # Try to use Impact font (classic meme font)
            font_paths = [
                "C:/Windows/Fonts/impact.ttf",  # Windows
                "/System/Library/Fonts/Impact.ttf",  # macOS
                "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf",  # Linux
                "/usr/share/fonts/TTF/DejaVuSans-Bold.ttf"  # Linux alternative
            ]
            
            for path in font_paths:
                if os.path.exists(path):
                    return path
            
            # Fallback to default
            return None
        except Exception:
            return None
    
    def _load_templates(self) -> Dict[str, MemeTemplateConfig]:
        """Load predefined meme templates"""
        templates = {}
        
        # Drake Pointing Template
        templates["drake_pointing"] = MemeTemplateConfig(
            name="drake_pointing",
            description="Drake pointing meme format",
            layout_type="two_panel",
            text_positions=[
                TextPosition(x=55, y=25, width=40, height=20, font_size=32),  # Top text
                TextPosition(x=55, y=75, width=40, height=20, font_size=32)   # Bottom text
            ],
            requires_base_image=True,
            base_image_path="drake_template.jpg",
            popularity=9
        )
        
        # Distracted Boyfriend
        templates["distracted_boyfriend"] = MemeTemplateConfig(
            name="distracted_boyfriend",
            description="Distracted boyfriend meme",
            layout_type="three_panel",
            text_positions=[
                TextPosition(x=15, y=85, width=20, height=10, font_size=24),  # Girlfriend
                TextPosition(x=50, y=85, width=20, height=10, font_size=24),  # Boyfriend
                TextPosition(x=80, y=85, width=20, height=10, font_size=24)   # Other woman
            ],
            requires_base_image=True,
            base_image_path="distracted_boyfriend.jpg",
            popularity=8
        )
        
        # Classic Top/Bottom Text
        templates["classic_meme"] = MemeTemplateConfig(
            name="classic_meme",
            description="Classic meme with top and bottom text",
            layout_type="single",
            text_positions=[
                TextPosition(x=50, y=10, width=90, height=15, font_size=48),  # Top text
                TextPosition(x=50, y=85, width=90, height=15, font_size=48)   # Bottom text
            ],
            requires_base_image=False,
            popularity=10
        )
        
        # Brain Expanding
        templates["expanding_brain"] = MemeTemplateConfig(
            name="expanding_brain",
            description="Expanding brain meme format",
            layout_type="four_panel",
            text_positions=[
                TextPosition(x=70, y=12, width=25, height=15, font_size=28),  # Level 1
                TextPosition(x=70, y=37, width=25, height=15, font_size=28),  # Level 2
                TextPosition(x=70, y=62, width=25, height=15, font_size=28),  # Level 3
                TextPosition(x=70, y=87, width=25, height=15, font_size=28)   # Level 4
            ],
            requires_base_image=True,
            base_image_path="expanding_brain.jpg",
            popularity=7
        )
        
        # Romanian Specific - Confused Guy
        templates["romanian_confused"] = MemeTemplateConfig(
            name="romanian_confused",
            description="Romanian confused guy pointing",
            layout_type="single",
            text_positions=[
                TextPosition(x=50, y=10, width=90, height=20, font_size=36),  # Top text
                TextPosition(x=50, y=85, width=90, height=15, font_size=32)   # Bottom text
            ],
            requires_base_image=False,
            background_color="#f0f0f0",
            popularity=8
        )
        
        # Simple Text Only
        templates["text_only"] = MemeTemplateConfig(
            name="text_only",
            description="Simple text-only meme",
            layout_type="single",
            text_positions=[
                TextPosition(x=50, y=50, width=80, height=40, font_size=44, alignment="center")
            ],
            requires_base_image=False,
            background_color="#000000",
            popularity=6
        )
        
        return templates
    
    def get_template(self, template_name: str) -> Optional[MemeTemplateConfig]:
        """Get template by name"""
        return self.templates.get(template_name)
    
    def list_templates(self, min_popularity: int = 1) -> List[str]:
        """List available templates above popularity threshold"""
        return [name for name, template in self.templates.items() 
                if template.popularity >= min_popularity]
    
    def apply_template(self, template_name: str, texts: List[str], 
                      background_image: Optional[bytes] = None,
                      output_size: Tuple[int, int] = (512, 512)) -> bytes:
        """Apply template with texts to create meme image"""
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
        
        try:
            # Create base image
            if background_image:
                image = Image.open(BytesIO(background_image))
                image = image.resize(output_size, Image.Resampling.LANCZOS)
            elif template.requires_base_image and template.base_image_path:
                base_path = os.path.join(self.templates_dir, template.base_image_path)
                if os.path.exists(base_path):
                    image = Image.open(base_path)
                    image = image.resize(output_size, Image.Resampling.LANCZOS)
                else:
                    # Create colored background as fallback
                    image = Image.new('RGB', output_size, template.background_color)
            else:
                # Create colored background
                image = Image.new('RGB', output_size, template.background_color)
            
            # Apply text overlays
            image = self._add_text_overlays(image, template, texts)
            
            # Convert to bytes
            from io import BytesIO
            output = BytesIO()
            image.save(output, format='JPEG', quality=90)
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error applying template '{template_name}': {e}")
            return self._create_fallback_meme(texts[0] if texts else "Error", output_size)
    
    def _add_text_overlays(self, image: Image.Image, template: MemeTemplateConfig, 
                          texts: List[str]) -> Image.Image:
        """Add text overlays to image based on template"""
        draw = ImageDraw.Draw(image)
        
        for i, text_pos in enumerate(template.text_positions):
            if i < len(texts) and texts[i]:
                text = texts[i].upper()  # Memes are traditionally uppercase
                
                # Calculate position and size
                img_width, img_height = image.size
                x = int(text_pos.x * img_width / 100)
                y = int(text_pos.y * img_height / 100)
                box_width = int(text_pos.width * img_width / 100)
                box_height = int(text_pos.height * img_height / 100)
                
                # Get font
                font = self._get_font(text_pos.font_size)
                
                # Wrap text to fit box
                wrapped_text = self._wrap_text(text, font, box_width, draw)
                
                # Calculate text position for alignment
                text_x, text_y = self._calculate_text_position(
                    wrapped_text, x, y, box_width, box_height, 
                    text_pos.alignment, font, draw
                )
                
                # Draw text with stroke
                if text_pos.stroke_width > 0:
                    # Draw stroke
                    for dx in range(-text_pos.stroke_width, text_pos.stroke_width + 1):
                        for dy in range(-text_pos.stroke_width, text_pos.stroke_width + 1):
                            if dx != 0 or dy != 0:
                                draw.multiline_text(
                                    (text_x + dx, text_y + dy), wrapped_text,
                                    font=font, fill=text_pos.stroke_color,
                                    align=text_pos.alignment
                                )
                
                # Draw main text
                draw.multiline_text(
                    (text_x, text_y), wrapped_text,
                    font=font, fill=text_pos.color,
                    align=text_pos.alignment
                )
        
        return image
    
    def _get_font(self, size: int) -> ImageFont.FreeTypeFont:
        """Get font with specified size"""
        try:
            if self.default_font_path:
                return ImageFont.truetype(self.default_font_path, size)
            else:
                return ImageFont.load_default()
        except Exception:
            return ImageFont.load_default()
    
    def _wrap_text(self, text: str, font: ImageFont.FreeTypeFont, 
                   max_width: int, draw: ImageDraw.Draw) -> str:
        """Wrap text to fit within specified width"""
        words = text.split()
        lines = []
        current_line = []
        
        for word in words:
            test_line = ' '.join(current_line + [word])
            bbox = draw.textbbox((0, 0), test_line, font=font)
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                    current_line = [word]
                else:
                    # Word is too long, add it anyway
                    lines.append(word)
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return '\n'.join(lines)
    
    def _calculate_text_position(self, text: str, x: int, y: int, 
                                box_width: int, box_height: int,
                                alignment: str, font: ImageFont.FreeTypeFont,
                                draw: ImageDraw.Draw) -> Tuple[int, int]:
        """Calculate text position based on alignment"""
        bbox = draw.multiline_textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # Horizontal alignment
        if alignment == "center":
            text_x = x + (box_width - text_width) // 2
        elif alignment == "right":
            text_x = x + box_width - text_width
        else:  # left
            text_x = x
        
        # Vertical centering
        text_y = y + (box_height - text_height) // 2
        
        return text_x, text_y
    
    def _create_fallback_meme(self, text: str, size: Tuple[int, int]) -> bytes:
        """Create simple fallback meme when template fails"""
        try:
            image = Image.new('RGB', size, 'black')
            draw = ImageDraw.Draw(image)
            font = self._get_font(36)
            
            # Center text
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (size[0] - text_width) // 2
            y = (size[1] - text_height) // 2
            
            # Draw with white text and black stroke
            for dx in range(-2, 3):
                for dy in range(-2, 3):
                    if dx != 0 or dy != 0:
                        draw.text((x + dx, y + dy), text, font=font, fill='black')
            draw.text((x, y), text, font=font, fill='white')
            
            from io import BytesIO
            output = BytesIO()
            image.save(output, format='JPEG', quality=90)
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error creating fallback meme: {e}")
            # Return minimal image
            image = Image.new('RGB', size, 'red')
            from io import BytesIO
            output = BytesIO()
            image.save(output, format='JPEG', quality=90)
            return output.getvalue()
    
    def create_custom_template(self, name: str, layout_type: str,
                              text_positions: List[Dict[str, Any]]) -> bool:
        """Create custom template from configuration"""
        try:
            positions = []
            for pos_dict in text_positions:
                position = TextPosition(
                    x=pos_dict.get('x', 50),
                    y=pos_dict.get('y', 50),
                    width=pos_dict.get('width', 80),
                    height=pos_dict.get('height', 20),
                    font_size=pos_dict.get('font_size', 36),
                    color=pos_dict.get('color', 'white'),
                    stroke_color=pos_dict.get('stroke_color', 'black'),
                    stroke_width=pos_dict.get('stroke_width', 2),
                    alignment=pos_dict.get('alignment', 'center')
                )
                positions.append(position)
            
            template = MemeTemplateConfig(
                name=name,
                description=f"Custom template: {name}",
                layout_type=layout_type,
                text_positions=positions,
                popularity=5
            )
            
            self.templates[name] = template
            return True
            
        except Exception as e:
            logger.error(f"Error creating custom template: {e}")
            return False
