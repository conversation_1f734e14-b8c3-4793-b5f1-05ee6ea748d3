#!/usr/bin/env python3
"""
Test Improved Error Handling

Tests the improved error handling and fallback mechanisms.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))


async def test_improved_error_handling():
    """Test the improved error handling and fallback mechanisms"""
    print("🔧 Testing Improved Error Handling...")
    print("=" * 60)
    
    try:
        # Test imports
        from webui.components.viral_automation_interface import (
            create_fallback_topic,
            create_emergency_fallback_topic,
            create_fallback_script
        )
        from app.services.one_click_viral_generator import ViralVideoConfig
        
        print("✅ All functions imported successfully")
        
        # Test configuration
        config = ViralVideoConfig(
            category="motivation",
            target_audience="tineri români 18-35",
            platform="tiktok",
            duration=60
        )
        
        print("✅ Configuration created")
        
        # Test fallback topic creation
        print("\n🧪 Testing fallback topic creation...")
        fallback_topic = create_fallback_topic(config)
        
        print(f"✅ Fallback topic created:")
        print(f"   Title: {fallback_topic.title}")
        print(f"   Viral potential: {fallback_topic.viral_potential:.1f}/10")
        print(f"   Romanian relevance: {fallback_topic.romanian_relevance:.1f}/10")
        
        # Test emergency fallback topic creation
        print("\n🧪 Testing emergency fallback topic creation...")
        emergency_topic = create_emergency_fallback_topic(config)
        
        print(f"✅ Emergency fallback topic created:")
        print(f"   Title: {emergency_topic.title}")
        print(f"   Viral potential: {emergency_topic.viral_potential:.1f}/10")
        print(f"   Romanian relevance: {emergency_topic.romanian_relevance:.1f}/10")
        
        # Test fallback script creation
        print("\n🧪 Testing fallback script creation...")
        fallback_script = create_fallback_script(fallback_topic, config)
        
        print(f"✅ Fallback script created:")
        print(f"   Length: {len(fallback_script.script_text)} characters")
        print(f"   Duration: {fallback_script.estimated_duration}s")
        print(f"   Hook: {fallback_script.hook}")
        
        # Test different categories
        print("\n🧪 Testing different category fallbacks...")
        categories = ["lifestyle_romania", "food_romania", "travel_romania", "unknown_category"]
        
        for category in categories:
            config.category = category
            topic = create_fallback_topic(config)
            print(f"   {category}: {topic.title}")
        
        # Test the enhanced generation function simulation
        print("\n🧪 Testing enhanced generation simulation...")
        
        # Simulate the improved error handling logic
        topic = None
        
        # Simulate AI generation failure
        try:
            # This would normally be the AI generation call
            raise Exception("Simulated AI generation failure")
        except Exception as e:
            print(f"   ⚠️ Simulated error: {e} - using fallback")
            topic = create_fallback_topic(config)
        
        # Check if topic creation was successful
        if not topic:
            print("   ⚠️ Fallback topic creation failed - using emergency fallback")
            topic = create_emergency_fallback_topic(config)
        
        print(f"   ✅ Final topic: {topic.title}")
        
        # Test script generation with fallback
        script = None
        try:
            # This would normally be the AI script generation call
            raise Exception("Simulated script generation failure")
        except Exception as e:
            print(f"   ⚠️ Simulated script error: {e} - using fallback")
            script = create_fallback_script(topic, config)
        
        # Ensure we always have a script
        if not script:
            print("   ⚠️ Fallback script creation failed - creating emergency script")
            script = create_fallback_script(topic, config)
        
        print(f"   ✅ Final script: {len(script.script_text)} characters")
        
        print("\n" + "=" * 60)
        print("📊 IMPROVED ERROR HANDLING SUMMARY:")
        print("=" * 60)
        print("✅ Multiple fallback layers implemented")
        print("✅ Emergency fallback topics for when all else fails")
        print("✅ Guaranteed topic and script creation")
        print("✅ No more 'None' returns that cause process failure")
        print("✅ Graceful degradation with user-friendly messages")
        print("✅ Process continues even when AI generation fails")
        
        print("\n🎉 Improved error handling test completed successfully!")
        print("🚀 The system now has robust multi-layer fallback mechanisms!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    try:
        success = asyncio.run(test_improved_error_handling())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
