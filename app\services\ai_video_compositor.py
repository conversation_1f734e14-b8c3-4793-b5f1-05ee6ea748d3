"""
AI Video Compositor
Creates videos from AI-generated image sequences with smooth transitions and text overlays
"""

import os
import time
import tempfile
import numpy as np
from typing import List, Optional, Dict, Any
from pathlib import Path
import logging
from PIL import Image, ImageEnhance, ImageFilter
from io import BytesIO

logger = logging.getLogger(__name__)

try:
    from moviepy.editor import (
        ImageClip, CompositeVideoClip, TextClip, ColorClip,
        concatenate_videoclips, VideoFileClip
    )
    from moviepy.video.fx import resize, fadein, fadeout
    MOVIEPY_AVAILABLE = True
except ImportError:
    logger.warning("MoviePy not available for AI video composition")
    MOVIEPY_AVAILABLE = False

from .ai_sequence_generator import AIImageSegment, AISequenceConfig

class AIVideoCompositor:
    """Composes videos from AI-generated image sequences"""
    
    def __init__(self):
        self.temp_dir = Path("storage/temp_ai_video")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # Transition effects
        self.transition_types = [
            "fade", "slide_left", "slide_right", "zoom_in", "zoom_out", 
            "rotate", "chaos_wipe", "glitch", "dissolve"
        ]
        
        # Text positioning presets
        self.text_positions = {
            "top": ("center", 100),
            "center": ("center", "center"),
            "bottom": ("center", lambda h: h - 150),
            "top_left": (50, 50),
            "top_right": (lambda w: w - 50, 50),
            "bottom_left": (50, lambda h: h - 100),
            "bottom_right": (lambda w: w - 50, lambda h: h - 100)
        }
    
    async def create_video_from_sequence(
        self, 
        segments: List[AIImageSegment], 
        config: AISequenceConfig,
        text_overlays: List[Dict] = None,
        output_path: str = None
    ) -> Dict[str, Any]:
        """Create video from AI image sequence"""
        
        if not MOVIEPY_AVAILABLE:
            return {
                "success": False,
                "error": "MoviePy not available for video composition",
                "output_path": None
            }
        
        if not segments:
            return {
                "success": False,
                "error": "No image segments provided",
                "output_path": None
            }
        
        try:
            logger.info(f"🎬 Creating video from {len(segments)} AI image segments")
            
            # Filter segments with actual image data
            valid_segments = [s for s in segments if s.image_data]
            if not valid_segments:
                return {
                    "success": False,
                    "error": "No valid image segments with data",
                    "output_path": None
                }
            
            logger.info(f"📊 Using {len(valid_segments)} valid segments")
            
            # Create video clips from segments
            video_clips = await self._create_clips_from_segments(valid_segments, config)
            
            if not video_clips:
                return {
                    "success": False,
                    "error": "Failed to create video clips from segments",
                    "output_path": None
                }
            
            # Combine clips with transitions
            main_video = self._combine_clips_with_transitions(video_clips, config)
            
            # Add text overlays if provided
            if text_overlays:
                main_video = self._add_text_overlays(main_video, text_overlays, config)
            
            # Apply chaos effects based on chaos level
            main_video = self._apply_chaos_effects(main_video, config.chaos_level)
            
            # Generate output path if not provided
            if not output_path:
                timestamp = int(time.time())
                output_path = f"storage/videos/ai_shitpost_{config.theme}_{timestamp}.mp4"
            
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Write final video
            logger.info(f"💾 Writing video to: {output_path}")
            main_video.write_videofile(
                output_path,
                fps=30,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # Cleanup
            main_video.close()
            for clip in video_clips:
                clip.close()
            
            # Get file size
            file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
            
            logger.info(f"✅ AI video created successfully: {output_path}")
            
            return {
                "success": True,
                "output_path": output_path,
                "file_size": file_size,
                "segments_used": len(valid_segments),
                "total_segments": len(segments),
                "duration": config.duration,
                "resolution": f"{config.width}x{config.height}",
                "chaos_level": config.chaos_level
            }
            
        except Exception as e:
            logger.error(f"❌ Error creating AI video: {e}")
            return {
                "success": False,
                "error": str(e),
                "output_path": None
            }
    
    async def _create_clips_from_segments(
        self, 
        segments: List[AIImageSegment], 
        config: AISequenceConfig
    ) -> List:
        """Create MoviePy clips from image segments"""
        clips = []
        
        for i, segment in enumerate(segments):
            try:
                # Convert image data to PIL Image
                image = Image.open(BytesIO(segment.image_data))
                
                # Resize to target resolution
                image = image.resize((config.width, config.height), Image.Resampling.LANCZOS)
                
                # Apply chaos-level specific effects to image
                image = self._apply_image_effects(image, config.chaos_level, i)
                
                # Save to temporary file
                temp_path = self.temp_dir / f"segment_{i}.jpg"
                image.save(temp_path, "JPEG", quality=90)
                
                # Create MoviePy clip
                clip = ImageClip(str(temp_path), duration=segment.duration)
                clip = clip.set_start(segment.start_time)
                
                clips.append(clip)
                logger.debug(f"Created clip {i+1}: {segment.duration:.1f}s at {segment.start_time:.1f}s")
                
            except Exception as e:
                logger.error(f"Error creating clip {i+1}: {e}")
                continue
        
        return clips
    
    def _apply_image_effects(self, image: Image.Image, chaos_level: int, index: int) -> Image.Image:
        """Apply chaos-level specific effects to individual images"""
        
        if chaos_level <= 3:
            return image  # Keep original for low chaos
        
        # Apply effects based on chaos level
        if chaos_level >= 4:
            # Enhance colors
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(1.2 + (chaos_level * 0.1))
        
        if chaos_level >= 6:
            # Increase contrast
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.3 + (chaos_level * 0.1))
            
            # Add slight rotation for chaos
            rotation = (index % 3 - 1) * (chaos_level - 5)  # -1, 0, or 1 degrees * chaos
            if rotation != 0:
                image = image.rotate(rotation, expand=False, fillcolor=(0, 0, 0))
        
        if chaos_level >= 8:
            # Deep fried effect
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(2.0)
            
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)
            
            # Add noise/grain effect
            if chaos_level >= 9:
                image = image.filter(ImageFilter.SHARPEN)
        
        return image
    
    def _combine_clips_with_transitions(self, clips: List, config: AISequenceConfig) -> Any:
        """Combine clips with smooth transitions"""
        if len(clips) == 1:
            return clips[0]
        
        # Add transitions between clips
        transition_clips = []
        
        for i, clip in enumerate(clips):
            if i == 0:
                # First clip - add fade in
                clip = clip.fx(fadein, config.transition_duration)
            elif i == len(clips) - 1:
                # Last clip - add fade out
                clip = clip.fx(fadeout, config.transition_duration)
            else:
                # Middle clips - add both fade in and out for overlap
                clip = clip.fx(fadein, config.transition_duration)
                clip = clip.fx(fadeout, config.transition_duration)
            
            transition_clips.append(clip)
        
        # Concatenate all clips
        final_video = concatenate_videoclips(transition_clips, method="compose")
        
        return final_video
    
    def _add_text_overlays(self, video: Any, text_overlays: List[Dict], config: AISequenceConfig) -> Any:
        """Add text overlays to video"""
        text_clips = []
        
        for overlay in text_overlays:
            try:
                text = overlay.get("text", "")
                start_time = overlay.get("start_time", 0)
                duration = overlay.get("duration", config.duration)
                position = overlay.get("position", "center")
                fontsize = overlay.get("fontsize", 50)
                color = overlay.get("color", "white")
                stroke_color = overlay.get("stroke_color", "black")
                stroke_width = overlay.get("stroke_width", 2)
                
                # Create text clip with fallback to PIL if ImageMagick fails
                try:
                    text_clip = TextClip(
                        text,
                        fontsize=fontsize,
                        color=color,
                        stroke_color=stroke_color,
                        stroke_width=stroke_width,
                        font='Arial-Bold'
                    ).set_duration(duration).set_start(start_time)
                    
                    # Set position
                    if isinstance(position, str) and position in self.text_positions:
                        text_clip = text_clip.set_position(self.text_positions[position])
                    else:
                        text_clip = text_clip.set_position(position)
                    
                    text_clips.append(text_clip)
                    
                except Exception as text_error:
                    logger.warning(f"Failed to create text clip with MoviePy: {text_error}")
                    # Could add PIL-based text rendering fallback here
                    continue
                    
            except Exception as e:
                logger.error(f"Error creating text overlay: {e}")
                continue
        
        if text_clips:
            # Composite video with text overlays
            final_video = CompositeVideoClip([video] + text_clips)
            return final_video
        
        return video
    
    def _apply_chaos_effects(self, video: Any, chaos_level: int) -> Any:
        """Apply chaos-level specific video effects"""
        
        if chaos_level <= 3:
            return video  # No effects for low chaos
        
        try:
            # Apply effects based on chaos level
            if chaos_level >= 5:
                # Add slight zoom effect for chaos levels 5+
                video = video.fx(resize, lambda t: 1 + 0.05 * np.sin(2 * np.pi * t))
            
            if chaos_level >= 7:
                # Add more dramatic zoom oscillation
                video = video.fx(resize, lambda t: 1 + 0.1 * np.sin(4 * np.pi * t))
            
            if chaos_level >= 9:
                # Maximum chaos - rapid zoom changes
                video = video.fx(resize, lambda t: 1 + 0.15 * np.sin(8 * np.pi * t))
            
        except Exception as e:
            logger.warning(f"Failed to apply chaos effects: {e}")
        
        return video
    
    def cleanup_temp_files(self):
        """Clean up temporary files"""
        try:
            for file_path in self.temp_dir.glob("segment_*.jpg"):
                file_path.unlink()
            logger.debug("Cleaned up temporary image files")
        except Exception as e:
            logger.warning(f"Error cleaning up temp files: {e}")

# Global instance
ai_video_compositor = AIVideoCompositor()
