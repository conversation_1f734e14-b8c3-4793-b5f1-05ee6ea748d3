#!/usr/bin/env python3
"""
GPT4Free Component for MoneyPrinterTurbo Web Interface

This component provides GPT4Free integration in the Streamlit interface.
"""

import streamlit as st
import asyncio
from typing import Optional

try:
    from app.services.gpt4free_service import gpt4free_service
    GPT4FREE_AVAILABLE = True
except ImportError:
    GPT4FREE_AVAILABLE = False


def render_gpt4free_sidebar():
    """Render GPT4Free options in sidebar"""
    
    if not GPT4FREE_AVAILABLE:
        st.sidebar.warning("🚫 GPT4Free nu este instalat")
        if st.sidebar.button("📥 Instalează GPT4Free"):
            st.sidebar.info("Rulează: pip install -U g4f[all]")
        return None
    
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 🤖 GPT4Free AI")
    
    # Service status
    status = gpt4free_service.get_status()
    
    if status["available"]:
        st.sidebar.success("✅ GPT4Free activ")
    else:
        st.sidebar.error("❌ GPT4Free inactiv")
        return None
    
    # Model selection
    available_models = status.get("available_models", [])
    if available_models:
        selected_model = st.sidebar.selectbox(
            "🎯 Model AI:",
            available_models,
            index=0 if "gpt-4o-mini" not in available_models else available_models.index("gpt-4o-mini"),
            help="Selectează modelul AI pentru generarea de conținut"
        )
    else:
        selected_model = "gpt-4o-mini"
    
    # Options
    use_web_search = st.sidebar.checkbox(
        "🌐 Căutare web",
        value=True,
        help="Activează căutarea web pentru informații actualizate"
    )
    
    romanian_optimization = st.sidebar.checkbox(
        "🇷🇴 Optimizare română",
        value=True,
        help="Optimizează răspunsurile pentru limba română"
    )
    
    return {
        "model": selected_model,
        "web_search": use_web_search,
        "romanian_optimization": romanian_optimization
    }


def render_gpt4free_script_generator():
    """Render GPT4Free script generator"""
    
    if not GPT4FREE_AVAILABLE or not gpt4free_service.is_available():
        st.warning("🚫 GPT4Free nu este disponibil")
        return None
    
    st.markdown("### 🤖 Generator Script AI (GPT4Free)")
    
    col1, col2 = st.columns(2)
    
    with col1:
        subject = st.text_input(
            "📝 Subiectul videoclipului:",
            placeholder="Ex: Cele mai frumoase locuri din România",
            help="Descrie subiectul pentru care vrei să generezi un script"
        )
        
        style = st.selectbox(
            "🎨 Stilul scriptului:",
            ["captivant", "educațional", "distractiv", "emoțional", "motivațional"],
            help="Selectează stilul dorit pentru script"
        )
    
    with col2:
        length = st.selectbox(
            "⏱️ Lungimea scriptului:",
            ["scurt (30s)", "mediu (60s)", "lung (90s)"],
            index=1,
            help="Selectează lungimea dorită pentru script"
        )
        
        language = st.selectbox(
            "🌍 Limba:",
            ["română", "engleză", "mixtă"],
            help="Selectează limba pentru script"
        )
    
    if st.button("🚀 Generează Script cu AI", type="primary"):
        if not subject:
            st.error("❌ Te rog să introduci un subiect!")
            return None
        
        with st.spinner("🤖 Generez scriptul cu AI..."):
            try:
                # Run async function in sync context
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                script = loop.run_until_complete(
                    gpt4free_service.generate_script(
                        subject=subject,
                        style=style,
                        length=length.split()[0],  # Extract "scurt", "mediu", "lung"
                        language=language
                    )
                )
                
                loop.close()
                
                if script:
                    st.success("✅ Script generat cu succes!")
                    
                    # Display generated script
                    st.markdown("#### 📜 Script generat:")
                    st.text_area(
                        "Script:",
                        value=script,
                        height=200,
                        help="Poți copia și edita acest script"
                    )
                    
                    # Option to use this script
                    if st.button("📋 Folosește acest script"):
                        st.session_state.generated_script = script
                        st.success("✅ Script salvat! Poți să-l folosești în generarea videoclipului.")
                    
                    return script
                else:
                    st.error("❌ Nu am putut genera scriptul. Încearcă din nou.")
                    return None
                    
            except Exception as e:
                st.error(f"❌ Eroare la generarea scriptului: {e}")
                return None
    
    return None


def render_gpt4free_content_improver():
    """Render GPT4Free content improver"""
    
    if not GPT4FREE_AVAILABLE or not gpt4free_service.is_available():
        return None
    
    st.markdown("### ✨ Îmbunătățitor Conținut AI")
    
    content_to_improve = st.text_area(
        "📝 Conținut de îmbunătățit:",
        placeholder="Introdu textul pe care vrei să-l îmbunătățești...",
        height=150,
        help="Introdu conținutul pe care vrei să-l îmbunătățești cu AI"
    )
    
    improvement_type = st.selectbox(
        "🎯 Tipul îmbunătățirii:",
        ["general", "viral", "emotional", "romanian"],
        format_func=lambda x: {
            "general": "🔧 Îmbunătățire generală",
            "viral": "🔥 Optimizare pentru viral",
            "emotional": "❤️ Mai mult impact emoțional", 
            "romanian": "🇷🇴 Optimizare pentru română"
        }[x],
        help="Selectează tipul de îmbunătățire dorit"
    )
    
    if st.button("✨ Îmbunătățește cu AI"):
        if not content_to_improve:
            st.error("❌ Te rog să introduci conținutul de îmbunătățit!")
            return None
        
        with st.spinner("🤖 Îmbunătățesc conținutul..."):
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                improved_content = loop.run_until_complete(
                    gpt4free_service.improve_content(
                        content=content_to_improve,
                        improvement_type=improvement_type
                    )
                )
                
                loop.close()
                
                if improved_content:
                    st.success("✅ Conținut îmbunătățit!")
                    
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.markdown("#### 📝 Original:")
                        st.text_area("Original", value=content_to_improve, height=150, disabled=True)
                    
                    with col2:
                        st.markdown("#### ✨ Îmbunătățit:")
                        st.text_area("Îmbunătățit", value=improved_content, height=150)
                    
                    return improved_content
                else:
                    st.error("❌ Nu am putut îmbunătăți conținutul.")
                    return None
                    
            except Exception as e:
                st.error(f"❌ Eroare la îmbunătățirea conținutului: {e}")
                return None
    
    return None


def render_gpt4free_terms_generator():
    """Render GPT4Free search terms generator"""
    
    if not GPT4FREE_AVAILABLE or not gpt4free_service.is_available():
        return None
    
    st.markdown("### 🔍 Generator Termeni de Căutare AI")
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        subject_for_terms = st.text_input(
            "📝 Subiect pentru termeni:",
            placeholder="Ex: peisaje montane din România",
            help="Descrie subiectul pentru care vrei termeni de căutare"
        )
    
    with col2:
        terms_count = st.number_input(
            "📊 Numărul de termeni:",
            min_value=3,
            max_value=10,
            value=5,
            help="Câți termeni să generez"
        )
    
    if st.button("🔍 Generează Termeni AI"):
        if not subject_for_terms:
            st.error("❌ Te rog să introduci un subiect!")
            return None
        
        with st.spinner("🤖 Generez termenii de căutare..."):
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                terms = loop.run_until_complete(
                    gpt4free_service.generate_terms(
                        subject=subject_for_terms,
                        count=terms_count
                    )
                )
                
                loop.close()
                
                if terms:
                    st.success("✅ Termeni generați cu succes!")
                    
                    st.markdown("#### 🏷️ Termeni de căutare generați:")
                    for i, term in enumerate(terms, 1):
                        st.write(f"{i}. **{term}**")
                    
                    # Option to use these terms
                    terms_string = ", ".join(terms)
                    st.text_area(
                        "Termeni (pentru copiere):",
                        value=terms_string,
                        height=100,
                        help="Poți copia acești termeni"
                    )
                    
                    return terms
                else:
                    st.error("❌ Nu am putut genera termenii.")
                    return None
                    
            except Exception as e:
                st.error(f"❌ Eroare la generarea termenilor: {e}")
                return None
    
    return None


def render_gpt4free_status():
    """Render GPT4Free status information"""
    
    if not GPT4FREE_AVAILABLE:
        st.error("🚫 GPT4Free nu este instalat")
        st.markdown("""
        ### 📥 Instalare GPT4Free
        
        Pentru a instala GPT4Free, rulează:
        ```bash
        pip install -U g4f[all]
        ```
        
        Sau folosește scriptul de instalare:
        ```bash
        python install_gpt4free.py
        ```
        """)
        return
    
    status = gpt4free_service.get_status()
    
    if status["available"]:
        st.success("✅ GPT4Free este activ și funcțional!")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 🤖 Modele disponibile:")
            for model in status.get("available_models", []):
                st.write(f"• {model}")
        
        with col2:
            st.markdown("#### 🔌 Provideri disponibili:")
            for provider in status.get("available_providers", []):
                st.write(f"• {provider}")
        
        st.markdown("#### ⚙️ Configurație:")
        st.write(f"• Model implicit: **{status.get('default_model', 'N/A')}**")
        st.write(f"• Optimizare română: **{'✅ Da' if status.get('romanian_optimization') else '❌ Nu'}**")
        
    else:
        st.error("❌ GPT4Free nu este disponibil")
        st.write("Verifică instalarea și configurația.")


def render_gpt4free_main_component():
    """Render main GPT4Free component"""
    
    st.markdown("## 🤖 GPT4Free AI Assistant")
    st.markdown("*Acces gratuit la modele AI premium pentru generarea de conținut*")
    
    # Status check
    if not GPT4FREE_AVAILABLE:
        render_gpt4free_status()
        return
    
    # Tabs for different features
    tab1, tab2, tab3, tab4 = st.tabs([
        "📜 Generator Script", 
        "✨ Îmbunătățitor Conținut", 
        "🔍 Termeni Căutare",
        "ℹ️ Status"
    ])
    
    with tab1:
        render_gpt4free_script_generator()
    
    with tab2:
        render_gpt4free_content_improver()
    
    with tab3:
        render_gpt4free_terms_generator()
    
    with tab4:
        render_gpt4free_status()
