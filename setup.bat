@echo off
echo ========================================
echo MoneyPrinterTurbo Setup Script
echo ========================================
echo.

echo Checking if virtual environment exists...
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    echo Virtual environment created.
) else (
    echo Virtual environment already exists.
)

echo.
echo Activating virtual environment and installing dependencies...
call .\venv\Scripts\activate
pip install -r requirements.txt

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo To start the application:
echo 1. Web Interface: run start_webui.bat
echo 2. API Service: run start_api.bat
echo.
pause
