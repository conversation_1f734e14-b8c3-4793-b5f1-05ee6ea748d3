"""
Performance Optimization Module for Enhanced Shitpost Generator
Handles caching, memory optimization, batch processing, and resource management
"""

import os
import gc
import psutil
import asyncio
import threading
import time
from typing import Dict, List, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import logging
import json
from moviepy.editor import VideoFileClip
import tempfile

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    generation_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    cache_hit_rate: float
    file_size_mb: float
    success_rate: float

class MemoryManager:
    """Manages memory usage and cleanup"""
    
    def __init__(self, max_memory_mb: int = 2048):
        self.max_memory_mb = max_memory_mb
        self.current_clips = []
        self.temp_files = []
    
    def get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def check_memory_limit(self) -> bool:
        """Check if memory usage is within limits"""
        current_usage = self.get_memory_usage()
        return current_usage < self.max_memory_mb
    
    def register_clip(self, clip: VideoFileClip):
        """Register a clip for cleanup tracking"""
        self.current_clips.append(clip)
    
    def cleanup_clips(self):
        """Clean up all registered clips"""
        for clip in self.current_clips:
            try:
                clip.close()
            except Exception as e:
                logger.warning(f"Error closing clip: {e}")
        self.current_clips.clear()
        gc.collect()
    
    def register_temp_file(self, filepath: str):
        """Register temporary file for cleanup"""
        self.temp_files.append(filepath)
    
    def cleanup_temp_files(self):
        """Clean up temporary files"""
        for filepath in self.temp_files:
            try:
                if os.path.exists(filepath):
                    os.remove(filepath)
            except Exception as e:
                logger.warning(f"Error removing temp file {filepath}: {e}")
        self.temp_files.clear()
    
    def force_cleanup(self):
        """Force cleanup of all resources"""
        self.cleanup_clips()
        self.cleanup_temp_files()
        gc.collect()

class BatchProcessor:
    """Handles batch processing of multiple shitposts"""
    
    def __init__(self, max_workers: int = 3):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.memory_manager = MemoryManager()
    
    async def process_batch(self, requests: List[Dict[str, Any]], 
                           generator_func: Callable) -> List[Dict[str, Any]]:
        """Process multiple shitpost requests in parallel"""
        logger.info(f"Processing batch of {len(requests)} shitposts")
        
        # Split into smaller chunks to manage memory
        chunk_size = min(self.max_workers, 3)
        results = []
        
        for i in range(0, len(requests), chunk_size):
            chunk = requests[i:i + chunk_size]
            chunk_results = await self._process_chunk(chunk, generator_func)
            results.extend(chunk_results)
            
            # Cleanup between chunks
            self.memory_manager.force_cleanup()
            await asyncio.sleep(1)  # Brief pause to let system recover
        
        return results
    
    async def _process_chunk(self, chunk: List[Dict[str, Any]], 
                            generator_func: Callable) -> List[Dict[str, Any]]:
        """Process a chunk of requests"""
        loop = asyncio.get_event_loop()
        futures = []
        
        for request in chunk:
            future = loop.run_in_executor(
                self.executor, 
                self._safe_generate, 
                generator_func, 
                request
            )
            futures.append(future)
        
        results = await asyncio.gather(*futures, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Error processing request {i}: {result}")
                processed_results.append({
                    'success': False,
                    'error': str(result),
                    'request_index': i
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    def _safe_generate(self, generator_func: Callable, request: Dict[str, Any]) -> Dict[str, Any]:
        """Safely generate shitpost with error handling"""
        try:
            start_time = time.time()
            start_memory = self.memory_manager.get_memory_usage()
            
            # Check memory before processing
            if not self.memory_manager.check_memory_limit():
                self.memory_manager.force_cleanup()
            
            # Generate shitpost
            result = generator_func(**request)
            
            # Add performance metrics
            end_time = time.time()
            end_memory = self.memory_manager.get_memory_usage()
            
            if result.get('success'):
                result['performance'] = {
                    'generation_time': end_time - start_time,
                    'memory_used_mb': end_memory - start_memory,
                    'peak_memory_mb': end_memory
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Error in safe_generate: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            # Always cleanup
            self.memory_manager.force_cleanup()

class ResourceMonitor:
    """Monitors system resources and adjusts processing accordingly"""
    
    def __init__(self):
        self.monitoring = False
        self.metrics_history = []
        self.max_history = 100
    
    def start_monitoring(self):
        """Start resource monitoring in background"""
        self.monitoring = True
        thread = threading.Thread(target=self._monitor_loop, daemon=True)
        thread.start()
    
    def stop_monitoring(self):
        """Stop resource monitoring"""
        self.monitoring = False
    
    def _monitor_loop(self):
        """Background monitoring loop"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # Keep only recent metrics
                if len(self.metrics_history) > self.max_history:
                    self.metrics_history.pop(0)
                
                time.sleep(5)  # Monitor every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)
    
    def _collect_metrics(self) -> Dict[str, float]:
        """Collect current system metrics"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'timestamp': time.time(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / 1024 / 1024 / 1024,
                'disk_free_gb': disk.free / 1024 / 1024 / 1024
            }
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            return {}
    
    def get_current_load(self) -> Dict[str, float]:
        """Get current system load"""
        if not self.metrics_history:
            return self._collect_metrics()
        return self.metrics_history[-1]
    
    def should_throttle(self) -> bool:
        """Determine if processing should be throttled"""
        current = self.get_current_load()
        
        # Throttle if CPU > 80% or Memory > 85%
        return (current.get('cpu_percent', 0) > 80 or 
                current.get('memory_percent', 0) > 85)
    
    def get_recommended_workers(self) -> int:
        """Get recommended number of workers based on system load"""
        current = self.get_current_load()
        cpu_percent = current.get('cpu_percent', 50)
        memory_percent = current.get('memory_percent', 50)
        
        # Conservative approach
        if cpu_percent > 70 or memory_percent > 70:
            return 1
        elif cpu_percent > 50 or memory_percent > 50:
            return 2
        else:
            return 3

class QualityOptimizer:
    """Optimizes quality settings based on chaos level and performance requirements"""
    
    def __init__(self):
        self.quality_presets = {
            'low': {
                'resolution': (480, 480),
                'fps': 15,
                'bitrate': '500k',
                'codec_preset': 'ultrafast'
            },
            'medium': {
                'resolution': (720, 720),
                'fps': 24,
                'bitrate': '1000k',
                'codec_preset': 'fast'
            },
            'high': {
                'resolution': (1080, 1080),
                'fps': 30,
                'bitrate': '2000k',
                'codec_preset': 'medium'
            }
        }
    
    def get_optimal_settings(self, chaos_level: int, duration: int, 
                           system_load: Dict[str, float]) -> Dict[str, Any]:
        """Get optimal quality settings based on parameters"""
        
        # Base quality on chaos level
        if chaos_level >= 8:
            base_quality = 'low'  # High chaos = lower quality for effect
        elif chaos_level >= 5:
            base_quality = 'medium'
        else:
            base_quality = 'high'
        
        # Adjust based on system load
        cpu_percent = system_load.get('cpu_percent', 50)
        memory_percent = system_load.get('memory_percent', 50)
        
        if cpu_percent > 70 or memory_percent > 70:
            if base_quality == 'high':
                base_quality = 'medium'
            elif base_quality == 'medium':
                base_quality = 'low'
        
        # Adjust based on duration
        if duration > 30:
            if base_quality == 'high':
                base_quality = 'medium'
        
        settings = self.quality_presets[base_quality].copy()
        
        # Add chaos-specific adjustments
        if chaos_level >= 7:
            # For high chaos, reduce quality further for "deep fried" effect
            settings['resolution'] = (min(settings['resolution'][0], 480), 
                                    min(settings['resolution'][1], 480))
            settings['bitrate'] = '300k'
        
        return settings

class PerformanceOptimizer:
    """Main performance optimization coordinator"""
    
    def __init__(self):
        self.memory_manager = MemoryManager()
        self.batch_processor = BatchProcessor()
        self.resource_monitor = ResourceMonitor()
        self.quality_optimizer = QualityOptimizer()
        self.metrics = []
        
        # Start monitoring
        self.resource_monitor.start_monitoring()
    
    def optimize_single_generation(self, **kwargs) -> Dict[str, Any]:
        """Optimize settings for single shitpost generation"""
        system_load = self.resource_monitor.get_current_load()
        
        # Get optimal quality settings
        chaos_level = kwargs.get('chaos_level', 5)
        duration = kwargs.get('duration', 10)
        
        optimal_settings = self.quality_optimizer.get_optimal_settings(
            chaos_level, duration, system_load
        )
        
        # Update kwargs with optimal settings
        kwargs.update(optimal_settings)
        
        return kwargs
    
    async def optimize_batch_generation(self, requests: List[Dict[str, Any]], 
                                       generator_func: Callable) -> List[Dict[str, Any]]:
        """Optimize batch generation with performance monitoring"""
        
        # Adjust batch processor workers based on system load
        recommended_workers = self.resource_monitor.get_recommended_workers()
        self.batch_processor.max_workers = recommended_workers
        
        # Process batch
        results = await self.batch_processor.process_batch(requests, generator_func)
        
        # Collect performance metrics
        self._collect_batch_metrics(results)
        
        return results
    
    def _collect_batch_metrics(self, results: List[Dict[str, Any]]):
        """Collect metrics from batch results"""
        successful_results = [r for r in results if r.get('success')]
        
        if successful_results:
            avg_time = sum(r.get('performance', {}).get('generation_time', 0) 
                          for r in successful_results) / len(successful_results)
            
            success_rate = len(successful_results) / len(results)
            
            metrics = PerformanceMetrics(
                generation_time=avg_time,
                memory_usage_mb=self.memory_manager.get_memory_usage(),
                cpu_usage_percent=self.resource_monitor.get_current_load().get('cpu_percent', 0),
                cache_hit_rate=0.0,  # TODO: Implement cache hit tracking
                file_size_mb=sum(r.get('file_size', 0) for r in successful_results) / 1024 / 1024,
                success_rate=success_rate
            )
            
            self.metrics.append(metrics)
            logger.info(f"Batch metrics: {success_rate:.2%} success, {avg_time:.2f}s avg time")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        if not self.metrics:
            return {"message": "No metrics available"}
        
        recent_metrics = self.metrics[-10:]  # Last 10 batches
        
        return {
            "avg_generation_time": sum(m.generation_time for m in recent_metrics) / len(recent_metrics),
            "avg_memory_usage_mb": sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics),
            "avg_success_rate": sum(m.success_rate for m in recent_metrics) / len(recent_metrics),
            "total_batches_processed": len(self.metrics),
            "current_system_load": self.resource_monitor.get_current_load(),
            "recommended_workers": self.resource_monitor.get_recommended_workers()
        }
    
    def cleanup(self):
        """Cleanup all resources"""
        self.resource_monitor.stop_monitoring()
        self.memory_manager.force_cleanup()
        self.batch_processor.executor.shutdown(wait=True)
