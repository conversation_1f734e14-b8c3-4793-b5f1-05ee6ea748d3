#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pentru convertirea documentației Markdown în format Microsoft Word
Optimizat pentru competiția InfoEducatie
"""

import os
import sys
from pathlib import Path
import subprocess

def install_required_packages():
    """Instalează pachetele necesare pentru conversie"""
    required_packages = [
        'python-docx',
        'markdown',
        'beautifulsoup4',
        'Pillow'
    ]
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} este deja instalat")
        except ImportError:
            print(f"📦 Instalez {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])

def convert_markdown_to_word():
    """Convertește documentația Markdown în format Word"""
    
    # Instalează dependințele
    install_required_packages()
    
    # Import după instalare
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.style import WD_STYLE_TYPE
    except ImportError as e:
        print(f"❌ Eroare la importul python-docx: {e}")
        print("💡 Încercați: pip install python-docx")
        return False

    try:
        import markdown
        from bs4 import BeautifulSoup
        import re
    except ImportError as e:
        print(f"❌ Eroare la importul dependințelor: {e}")
        return False
    
    # Citește fișierul Markdown
    markdown_file = Path("DOCUMENTATIE_INFOEDUCATIE.md")
    
    if not markdown_file.exists():
        print("❌ Fișierul DOCUMENTATIE_INFOEDUCATIE.md nu a fost găsit!")
        return False
    
    print("📖 Citesc documentația Markdown...")
    with open(markdown_file, 'r', encoding='utf-8') as f:
        markdown_content = f.read()
    
    # Convertește Markdown în HTML
    print("🔄 Convertesc Markdown în HTML...")
    html = markdown.markdown(markdown_content, extensions=['codehilite', 'tables', 'toc'])
    soup = BeautifulSoup(html, 'html.parser')
    
    # Creează documentul Word
    print("📄 Creez documentul Word...")
    doc = Document()
    
    # Configurează stilurile
    setup_document_styles(doc)
    
    # Adaugă conținutul
    add_title_page(doc)
    add_table_of_contents(doc)
    process_html_content(doc, soup)
    
    # Salvează documentul
    output_file = "DOCUMENTATIE_INFOEDUCATIE_2025.docx"
    doc.save(output_file)
    
    print(f"✅ Documentul Word a fost creat cu succes: {output_file}")
    print(f"📊 Dimensiune fișier: {os.path.getsize(output_file) / 1024 / 1024:.1f} MB")
    
    return True

def setup_document_styles(doc):
    """Configurează stilurile pentru document"""

    try:
        # Încearcă să folosească stilurile custom dacă sunt disponibile
        if WD_STYLE_TYPE:
            # Stil pentru titlul principal
            title_style = doc.styles.add_style('Custom Title', WD_STYLE_TYPE.PARAGRAPH)
            title_font = title_style.font
            title_font.name = 'Arial'
            title_font.size = Pt(24)
            title_font.bold = True
            title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
            title_style.paragraph_format.space_after = Pt(12)

            # Stil pentru subtitluri
            subtitle_style = doc.styles.add_style('Custom Subtitle', WD_STYLE_TYPE.PARAGRAPH)
            subtitle_font = subtitle_style.font
            subtitle_font.name = 'Arial'
            subtitle_font.size = Pt(16)
            subtitle_font.bold = True
            subtitle_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
            subtitle_style.paragraph_format.space_after = Pt(6)

            # Stil pentru cod
            code_style = doc.styles.add_style('Custom Code', WD_STYLE_TYPE.PARAGRAPH)
            code_font = code_style.font
            code_font.name = 'Consolas'
            code_font.size = Pt(9)
            code_style.paragraph_format.left_indent = Inches(0.5)
            code_style.paragraph_format.space_before = Pt(6)
            code_style.paragraph_format.space_after = Pt(6)
    except Exception as e:
        print(f"⚠️ Nu s-au putut crea stilurile custom: {e}")
        print("📝 Se vor folosi stilurile implicite")

def add_title_page(doc):
    """Adaugă pagina de titlu"""

    # Titlul principal
    title = doc.add_paragraph("APARAT DE SCOS MASELE LA FRAIERI")
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title.runs[0].font.size = Pt(24)
    title.runs[0].font.bold = True

    # Subtitlul
    subtitle = doc.add_paragraph("Generator Automat de Videoclipuri cu Suport pentru Limba Română")
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subtitle.runs[0].font.size = Pt(16)
    subtitle.runs[0].font.bold = True
    
    # Informații competiție
    doc.add_paragraph("\n" * 3)
    
    info_para = doc.add_paragraph()
    info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    info_run = info_para.add_run("Documentație de Proiect pentru Competiția InfoEducatie 2025\n")
    info_run.font.size = Pt(14)
    info_run.font.bold = True
    
    # Detalii autor (de completat)
    details = [
        "Autor: [Numele Participantului]",
        "Școala: [Numele Școlii]", 
        "Clasa: [Clasa]",
        "Profesor Coordonator: [Numele Profesorului]",
        "Data: 26 Iulie 2025",
        "Categoria: Utilitar"
    ]
    
    doc.add_paragraph("\n" * 2)
    
    for detail in details:
        para = doc.add_paragraph(detail)
        para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        para.runs[0].font.size = Pt(12)
    
    # Page break
    doc.add_page_break()

def add_table_of_contents(doc):
    """Adaugă cuprinsul"""

    toc_title = doc.add_paragraph("CUPRINS")
    toc_title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    toc_title.runs[0].font.size = Pt(20)
    toc_title.runs[0].font.bold = True
    
    # Lista secțiunilor (va fi completată manual în Word)
    toc_items = [
        "1. Prezentarea Generală a Proiectului",
        "2. Documentația Tehnică", 
        "3. Caracteristici și Funcționalități",
        "4. Procesul de Dezvoltare",
        "5. Exemple de Cod",
        "6. Ghidul Utilizatorului",
        "7. Rezultate și Testare",
        "8. Dezvoltări Viitoare",
        "9. Concluzii",
        "10. Anexe"
    ]
    
    for item in toc_items:
        para = doc.add_paragraph(item)
        para.style = 'List Number'
    
    doc.add_paragraph("\nNotă: Numerele paginilor vor fi adăugate automat în Microsoft Word.")
    doc.add_page_break()

def process_html_content(doc, soup):
    """Procesează conținutul HTML și îl adaugă în document"""
    
    for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'p', 'pre', 'code', 'ul', 'ol', 'table']):
        
        if element.name in ['h1', 'h2', 'h3', 'h4']:
            # Titluri
            level = int(element.name[1])
            text = element.get_text().strip()
            
            if level == 1:
                para = doc.add_paragraph(text, style='Heading 1')
            elif level == 2:
                para = doc.add_paragraph(text, style='Heading 2')
            elif level == 3:
                para = doc.add_paragraph(text, style='Heading 3')
            else:
                para = doc.add_paragraph(text, style='Heading 4')
                
        elif element.name == 'p':
            # Paragrafe
            text = element.get_text().strip()
            if text:
                para = doc.add_paragraph(text)
                
        elif element.name in ['pre', 'code']:
            # Cod
            text = element.get_text().strip()
            if text:
                para = doc.add_paragraph(text)
                para.runs[0].font.name = 'Consolas'
                para.runs[0].font.size = Pt(9)
                para.paragraph_format.left_indent = Inches(0.5)
                
        elif element.name in ['ul', 'ol']:
            # Liste
            for li in element.find_all('li'):
                text = li.get_text().strip()
                if text:
                    para = doc.add_paragraph(text, style='List Bullet' if element.name == 'ul' else 'List Number')

def create_infoeducatie_template():
    """Creează un template specific pentru InfoEducatie"""
    
    print("📋 Creez template-ul InfoEducatie...")
    
    template_content = """
# INSTRUCȚIUNI PENTRU FINALIZAREA DOCUMENTAȚIEI INFOEDUCATIE

## Pași pentru completarea documentului Word:

### 1. Completarea Informațiilor Personale
- Deschideți documentul Word generat
- Înlocuiți placeholder-urile cu informațiile voastre:
  - [Numele Participantului] → numele vostru complet
  - [Numele Școlii] → școala voastră
  - [Clasa] → clasa actuală
  - [Numele Profesorului] → profesorul coordonator

### 2. Adăugarea Screenshot-urilor
- Inserați capturi de ecran ale aplicației în acțiune
- Adăugați imagini cu interfața în română
- Includeți exemple de videoclipuri generate

### 3. Formatarea Finală
- Verificați că toate titlurile sunt formatate corect
- Adăugați numerotarea paginilor
- Generați cuprinsul automat în Word
- Verificați că toate codurile sunt formatate corespunzător

### 4. Verificarea Finală
- Verificați ortografia și gramatica
- Asigurați-vă că toate linkurile funcționează
- Controlați că documentul respectă cerințele InfoEducatie

### 5. Export Final
- Salvați documentul ca .docx
- Creați și un PDF pentru backup
- Verificați că dimensiunea fișierului este acceptabilă

## Resurse Suplimentare:
- Ghidul InfoEducatie: https://infoeducatie.ro/
- Template-uri oficiale disponibile pe site
- Exemple de documentații câștigătoare din anii anteriori

Succes la competiție! 🏆
"""
    
    with open("INSTRUCTIUNI_FINALIZARE.md", 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    print("✅ Instrucțiuni create: INSTRUCTIUNI_FINALIZARE.md")

def main():
    """Funcția principală"""
    
    print("🏆 Convertor Documentație InfoEducatie 2025")
    print("=" * 50)
    
    try:
        # Convertește documentația
        success = convert_markdown_to_word()
        
        if success:
            # Creează instrucțiunile de finalizare
            create_infoeducatie_template()
            
            print("\n" + "=" * 50)
            print("🎉 CONVERSIE COMPLETĂ!")
            print("=" * 50)
            print("\n📄 Fișiere create:")
            print("   • DOCUMENTATIE_INFOEDUCATIE_2025.docx")
            print("   • INSTRUCTIUNI_FINALIZARE.md")
            print("\n📋 Următorii pași:")
            print("   1. Deschideți documentul Word")
            print("   2. Completați informațiile personale")
            print("   3. Adăugați screenshot-uri")
            print("   4. Verificați formatarea")
            print("   5. Exportați versiunea finală")
            print("\n🏆 Succes la InfoEducatie 2025!")
            
        else:
            print("❌ Conversia a eșuat!")
            
    except Exception as e:
        print(f"❌ Eroare în timpul conversiei: {e}")
        print("💡 Asigurați-vă că aveți Python și pip instalate corect")

if __name__ == "__main__":
    main()
