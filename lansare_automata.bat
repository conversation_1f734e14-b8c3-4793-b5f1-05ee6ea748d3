@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: ========================================
:: 🇷🇴 Lansator Automat - Aparat de Scos Masele la Fraieri
:: Versiunea 2.0 - Lansare cu verificări complete
:: ========================================

title Aparat de Scos Masele la Fraieri - Lansator Automat

:: Culori pentru output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

echo.
echo %BLUE%========================================%RESET%
echo %BLUE%🇷🇴 Aparat de Scos Masele la Fraieri%RESET%
echo %BLUE%========================================%RESET%
echo %GREEN%Lansator Automat cu Verificări Complete%RESET%
echo.

:: Verifică dacă suntem în directorul corect
if not exist "webui\Main.py" (
    echo %RED%❌ EROARE: Nu sunt în directorul corect!%RESET%
    echo %YELLOW%Vă rugăm să rulați acest script din directorul MoneyPrinterTurbo%RESET%
    echo.
    pause
    exit /b 1
)

:: Verifică dacă Python este instalat
echo %BLUE%🔍 Verificare Python...%RESET%
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Python nu este instalat sau nu este în PATH!%RESET%
    echo %YELLOW%Vă rugăm să instalați Python 3.10+ de la: https://python.org%RESET%
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo %GREEN%✅ Python %PYTHON_VERSION% detectat%RESET%

:: Verifică dacă mediul virtual există
echo %BLUE%🔍 Verificare mediu virtual...%RESET%
if not exist "venv\Scripts\python.exe" (
    echo %YELLOW%⚠️ Mediul virtual nu există. Îl creez acum...%RESET%
    python -m venv venv
    if errorlevel 1 (
        echo %RED%❌ Eroare la crearea mediului virtual!%RESET%
        pause
        exit /b 1
    )
    echo %GREEN%✅ Mediu virtual creat cu succes%RESET%
) else (
    echo %GREEN%✅ Mediu virtual găsit%RESET%
)

:: Activează mediul virtual
echo %BLUE%🔄 Activare mediu virtual...%RESET%
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo %RED%❌ Eroare la activarea mediului virtual!%RESET%
    pause
    exit /b 1
)
echo %GREEN%✅ Mediu virtual activat%RESET%

:: Verifică dependințele
echo %BLUE%🔍 Verificare dependințe...%RESET%
python -c "import streamlit, fastapi, moviepy" >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠️ Dependințe lipsă. Le instalez acum...%RESET%
    echo %BLUE%📦 Instalare dependințe (poate dura câteva minute)...%RESET%
    pip install -r requirements.txt --quiet
    if errorlevel 1 (
        echo %RED%❌ Eroare la instalarea dependințelor!%RESET%
        echo %YELLOW%Încercați manual: pip install -r requirements.txt%RESET%
        pause
        exit /b 1
    )
    echo %GREEN%✅ Dependințe instalate cu succes%RESET%
) else (
    echo %GREEN%✅ Toate dependințele sunt instalate%RESET%
)

:: Verifică configurația
echo %BLUE%🔍 Verificare configurație...%RESET%
if not exist "config.toml" (
    echo %YELLOW%⚠️ Fișierul config.toml nu există. Îl copiez din exemplu...%RESET%
    copy config.example.toml config.toml >nul
    echo %GREEN%✅ Configurație inițializată%RESET%
) else (
    echo %GREEN%✅ Configurație găsită%RESET%
)

:: Verifică cheile API
echo %BLUE%🔍 Verificare chei API...%RESET%
python -c "
import toml
try:
    config = toml.load('config.toml')
    pexels_keys = config.get('app', {}).get('pexels_api_keys', [])
    llm_provider = config.get('app', {}).get('llm_provider', '')
    llm_key_field = f'{llm_provider}_api_key'
    llm_key = config.get('app', {}).get(llm_key_field, '')
    
    if not pexels_keys or not any(pexels_keys):
        print('WARN_PEXELS')
    if not llm_key:
        print('WARN_LLM')
    if pexels_keys and any(pexels_keys) and llm_key:
        print('API_OK')
except Exception as e:
    print('CONFIG_ERROR')
" > temp_check.txt

set /p API_STATUS=<temp_check.txt
del temp_check.txt

if "!API_STATUS!"=="CONFIG_ERROR" (
    echo %RED%❌ Eroare la citirea configurației!%RESET%
    pause
    exit /b 1
)

if "!API_STATUS!"=="WARN_PEXELS" (
    echo %YELLOW%⚠️ Cheia API Pexels nu este configurată%RESET%
    echo %YELLOW%   Configurați în Basic Settings din interfață%RESET%
)

if "!API_STATUS!"=="WARN_LLM" (
    echo %YELLOW%⚠️ Cheia API LLM nu este configurată%RESET%
    echo %YELLOW%   Configurați în Basic Settings din interfață%RESET%
)

if "!API_STATUS!"=="API_OK" (
    echo %GREEN%✅ Chei API configurate corect%RESET%
)

:: Verifică porturile
echo %BLUE%🔍 Verificare porturi...%RESET%
netstat -an | find "8501" | find "LISTENING" >nul
if not errorlevel 1 (
    echo %YELLOW%⚠️ Portul 8501 este deja ocupat%RESET%
    echo %YELLOW%   Aplicația poate fi deja pornită%RESET%
) else (
    echo %GREEN%✅ Portul 8501 este disponibil%RESET%
)

:: Meniu de opțiuni
echo.
echo %BLUE%========================================%RESET%
echo %GREEN%🚀 Opțiuni de Lansare%RESET%
echo %BLUE%========================================%RESET%
echo.
echo %YELLOW%1.%RESET% 🌐 Lansează Interfața Web (Recomandat)
echo %YELLOW%2.%RESET% 🔧 Lansează Serviciul API
echo %YELLOW%3.%RESET% 🧪 Testează Conexiunea LLM
echo %YELLOW%4.%RESET% 📊 Verifică Statusul Complet
echo %YELLOW%5.%RESET% 🔄 Actualizează Dependințele
echo %YELLOW%6.%RESET% ⚙️ Configurare Rapidă API
echo %YELLOW%0.%RESET% ❌ Ieșire
echo.

set /p "choice=Alegeți opțiunea (1-6, 0 pentru ieșire): "

if "%choice%"=="1" goto :start_webui
if "%choice%"=="2" goto :start_api
if "%choice%"=="3" goto :test_llm
if "%choice%"=="4" goto :full_status
if "%choice%"=="5" goto :update_deps
if "%choice%"=="6" goto :quick_config
if "%choice%"=="0" goto :exit
goto :invalid_choice

:start_webui
echo.
echo %GREEN%🌐 Lansare Interfață Web...%RESET%
echo %BLUE%📍 URL: http://localhost:8501%RESET%
echo %YELLOW%💡 Interfața se va deschide automat în browser%RESET%
echo %YELLOW%💡 Pentru a opri aplicația, apăsați Ctrl+C%RESET%
echo.
timeout /t 3 /nobreak >nul
streamlit run webui\Main.py --browser.gatherUsageStats=False --server.enableCORS=True
goto :end

:start_api
echo.
echo %GREEN%🔧 Lansare Serviciu API...%RESET%
echo %BLUE%📍 URL: http://127.0.0.1:8080%RESET%
echo %BLUE%📖 Documentație: http://127.0.0.1:8080/docs%RESET%
echo %YELLOW%💡 Pentru a opri serviciul, apăsați Ctrl+C%RESET%
echo.
timeout /t 3 /nobreak >nul
python main.py
goto :end

:test_llm
echo.
echo %GREEN%🧪 Testare Conexiune LLM...%RESET%
echo.
if exist "test_llm_connection.py" (
    python test_llm_connection.py
) else (
    echo %RED%❌ Scriptul de test nu există!%RESET%
)
echo.
pause
goto :menu

:full_status
echo.
echo %GREEN%📊 Status Complet al Sistemului%RESET%
echo %BLUE%========================================%RESET%
echo.
if exist "verify_api_config.py" (
    python verify_api_config.py
) else (
    echo %YELLOW%⚠️ Scriptul de verificare nu există%RESET%
    echo %GREEN%Status de bază:%RESET%
    echo %GREEN%✅ Python: Funcțional%RESET%
    echo %GREEN%✅ Mediu virtual: Activ%RESET%
    echo %GREEN%✅ Configurație: Prezentă%RESET%
)
echo.
pause
goto :menu

:update_deps
echo.
echo %GREEN%🔄 Actualizare Dependințe...%RESET%
echo.
pip install -r requirements.txt --upgrade
echo.
echo %GREEN%✅ Dependințe actualizate%RESET%
pause
goto :menu

:quick_config
echo.
echo %GREEN%⚙️ Configurare Rapidă API%RESET%
echo %BLUE%========================================%RESET%
echo.
echo %YELLOW%Configurarea se face prin interfața web:%RESET%
echo %BLUE%1. Lansați interfața web (opțiunea 1)%RESET%
echo %BLUE%2. Găsiți secțiunea "Basic Settings"%RESET%
echo %BLUE%3. Configurați cheile API necesare%RESET%
echo.
echo %YELLOW%Chei API necesare:%RESET%
echo %GREEN%• Pexels API Key: https://www.pexels.com/api/%RESET%
echo %GREEN%• OpenRouter API Key: https://openrouter.ai/settings/keys%RESET%
echo.
pause
goto :menu

:invalid_choice
echo.
echo %RED%❌ Opțiune invalidă! Vă rugăm să alegeți 1-6 sau 0.%RESET%
timeout /t 2 /nobreak >nul
goto :menu

:menu
cls
goto :start

:exit
echo.
echo %GREEN%👋 La revedere!%RESET%
echo %YELLOW%Mulțumim că folosiți Aparatul de Scos Masele la Fraieri!%RESET%
echo.
timeout /t 2 /nobreak >nul
exit /b 0

:end
echo.
echo %GREEN%✅ Aplicația s-a închis%RESET%
pause
exit /b 0
