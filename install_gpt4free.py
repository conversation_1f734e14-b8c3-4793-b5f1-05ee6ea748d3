#!/usr/bin/env python3
"""
GPT4Free Installation Script for MoneyPrinterTurbo

This script installs and configures gpt4free integration.
"""

import subprocess
import sys
import os
from pathlib import Path

def install_gpt4free():
    """Install gpt4free package"""
    print("🚀 Installing GPT4Free...")
    
    try:
        # Install gpt4free with all features
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-U", "g4f[all]"
        ])
        print("✅ GPT4Free installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install GPT4Free: {e}")
        return False

def test_gpt4free():
    """Test gpt4free installation"""
    print("🧪 Testing GPT4Free installation...")
    
    try:
        from g4f.client import Client
        
        client = Client()
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": "Hello, test message"}],
            web_search=False
        )
        
        if response and response.choices:
            print("✅ GPT4Free test successful!")
            print(f"📝 Response: {response.choices[0].message.content[:100]}...")
            return True
        else:
            print("❌ GPT4Free test failed - no response")
            return False
            
    except Exception as e:
        print(f"❌ GPT4Free test failed: {e}")
        return False

def create_gpt4free_config():
    """Create configuration for gpt4free integration"""
    print("⚙️ Creating GPT4Free configuration...")
    
    config_content = """
# GPT4Free Configuration for MoneyPrinterTurbo

[gpt4free]
# Enable GPT4Free integration
enabled = true

# Default model to use
default_model = "gpt-4o-mini"

# Fallback models (in order of preference)
fallback_models = [
    "gpt-4o",
    "gpt-4",
    "gemini-pro",
    "claude-3-sonnet"
]

# Provider preferences
preferred_providers = [
    "auto",
    "Bing",
    "ChatGPT",
    "Gemini"
]

# Enable web search for better content
web_search_enabled = true

# Maximum retries for failed requests
max_retries = 3

# Request timeout (seconds)
timeout = 30

# Enable streaming responses
streaming = false

# Romanian language optimization
romanian_optimization = true
"""
    
    config_path = Path("config_gpt4free.toml")
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ Configuration created: {config_path}")
    return True

def main():
    """Main installation process"""
    print("🎯 GPT4Free Integration Setup for MoneyPrinterTurbo")
    print("=" * 60)
    
    # Step 1: Install gpt4free
    if not install_gpt4free():
        print("❌ Installation failed!")
        return False
    
    # Step 2: Test installation
    if not test_gpt4free():
        print("⚠️ Installation completed but testing failed")
        print("💡 You may need to restart your environment")
    
    # Step 3: Create configuration
    create_gpt4free_config()
    
    print("\n" + "=" * 60)
    print("🎉 GPT4Free Integration Setup Complete!")
    print("\n📋 Next Steps:")
    print("1. Restart your MoneyPrinterTurbo application")
    print("2. Check the new GPT4Free options in the interface")
    print("3. Test script generation with improved AI models")
    print("\n✨ Benefits:")
    print("- Free access to GPT-4o, Gemini, Claude models")
    print("- Better Romanian language support")
    print("- More creative and varied content generation")
    print("- No API costs!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
