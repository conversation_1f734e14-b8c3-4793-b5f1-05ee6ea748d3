#!/bin/bash
# Fix ImageMagick warnings by preferring PIL
export PREFER_PIL_TEXT=true
echo "🤖 Enhanced Shitpost Generator CLI"

# Activate virtual environment if it exists
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
fi

# Show help if no arguments
if [ $# -eq 0 ]; then
    echo "Usage examples:"
    echo "  $0 generate --theme romanian --chaos 8"
    echo "  $0 batch --count 5 --theme gaming"
    echo "  $0 ai-generate --prompt 'confused guy' --style absurd"
    echo "  $0 config --test-services"
    echo
    python3 shitpost_cli.py --help
else
    python3 shitpost_cli.py "$@"
fi
