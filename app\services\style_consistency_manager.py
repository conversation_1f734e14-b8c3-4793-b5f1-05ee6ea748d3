"""
Style Consistency Manager for Contextual Image Generation

This service ensures visual coherence across all generated images by maintaining
consistent style parameters, color schemes, and artistic direction.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)


class StyleCategory(Enum):
    """Categories of visual styles"""
    REALISTIC = "realistic"
    CINEMATIC = "cinematic"
    ARTISTIC = "artistic"
    HISTORICAL = "historical"
    FANTASY = "fantasy"
    DRAMATIC = "dramatic"
    COMEDIC = "comedic"


@dataclass
class StyleProfile:
    """Defines a consistent style profile for image generation"""
    name: str
    category: StyleCategory
    base_prompt: str
    color_palette: List[str]
    lighting_style: str
    composition_style: str
    quality_modifiers: List[str]
    negative_prompts: List[str]
    consistency_keywords: List[str]


@dataclass
class SceneStyleMapping:
    """Maps scene types to appropriate style modifications"""
    scene_type: str
    style_modifiers: List[str]
    lighting_adjustments: str
    composition_hints: str
    priority_keywords: List[str]


class StyleConsistencyManager:
    """Manages style consistency across contextual image generation"""
    
    def __init__(self):
        self.current_style_profile = None
        self.scene_style_mappings = self._initialize_scene_mappings()
        self.style_profiles = self._initialize_style_profiles()
        self.generated_prompts = []
        
    def _initialize_style_profiles(self) -> Dict[str, StyleProfile]:
        """Initialize predefined style profiles"""
        profiles = {}
        
        # Realistic Style
        profiles['realistic'] = StyleProfile(
            name="Realistic",
            category=StyleCategory.REALISTIC,
            base_prompt="photorealistic, high detail, professional photography",
            color_palette=["natural colors", "balanced saturation", "realistic lighting"],
            lighting_style="natural lighting, soft shadows",
            composition_style="professional composition, rule of thirds",
            quality_modifiers=["4K resolution", "sharp focus", "detailed textures"],
            negative_prompts=["cartoon", "anime", "low quality", "blurry"],
            consistency_keywords=["photorealistic", "natural", "detailed"]
        )
        
        # Cinematic Style
        profiles['cinematic'] = StyleProfile(
            name="Cinematic",
            category=StyleCategory.CINEMATIC,
            base_prompt="cinematic composition, film lighting, movie scene",
            color_palette=["cinematic color grading", "dramatic contrast", "film tones"],
            lighting_style="dramatic lighting, cinematic shadows",
            composition_style="wide angle, cinematic framing, depth of field",
            quality_modifiers=["film quality", "cinematic depth", "professional cinematography"],
            negative_prompts=["amateur", "snapshot", "poor lighting"],
            consistency_keywords=["cinematic", "dramatic", "film"]
        )
        
        # Historical Style
        profiles['historical'] = StyleProfile(
            name="Historical",
            category=StyleCategory.HISTORICAL,
            base_prompt="historically accurate, period appropriate, authentic details",
            color_palette=["period colors", "muted tones", "historical accuracy"],
            lighting_style="period appropriate lighting, natural illumination",
            composition_style="classical composition, historical perspective",
            quality_modifiers=["historically accurate", "period details", "authentic"],
            negative_prompts=["modern", "anachronistic", "contemporary"],
            consistency_keywords=["historical", "period", "authentic", "medieval"]
        )
        
        # Fantasy Style
        profiles['fantasy'] = StyleProfile(
            name="Fantasy",
            category=StyleCategory.FANTASY,
            base_prompt="fantasy art style, magical atmosphere, enchanted",
            color_palette=["vibrant colors", "magical hues", "fantasy palette"],
            lighting_style="magical lighting, ethereal glow, mystical atmosphere",
            composition_style="fantasy composition, magical elements",
            quality_modifiers=["fantasy art", "magical details", "enchanted"],
            negative_prompts=["realistic", "mundane", "ordinary"],
            consistency_keywords=["fantasy", "magical", "mystical", "enchanted"]
        )
        
        # Dramatic Style
        profiles['dramatic'] = StyleProfile(
            name="Dramatic",
            category=StyleCategory.DRAMATIC,
            base_prompt="dramatic atmosphere, intense mood, powerful composition",
            color_palette=["high contrast", "dramatic colors", "bold tones"],
            lighting_style="dramatic lighting, strong shadows, intense illumination",
            composition_style="powerful composition, dramatic angles",
            quality_modifiers=["dramatic intensity", "powerful mood", "striking"],
            negative_prompts=["peaceful", "calm", "subtle"],
            consistency_keywords=["dramatic", "intense", "powerful"]
        )
        
        return profiles

    def _initialize_scene_mappings(self) -> Dict[str, SceneStyleMapping]:
        """Initialize scene-specific style mappings"""
        mappings = {}
        
        mappings['battle'] = SceneStyleMapping(
            scene_type='battle',
            style_modifiers=["epic battle scene", "dynamic action", "heroic composition"],
            lighting_adjustments="dramatic lighting, intense shadows",
            composition_hints="dynamic angles, action composition",
            priority_keywords=["battle", "war", "conflict", "heroic"]
        )
        
        mappings['dialogue'] = SceneStyleMapping(
            scene_type='dialogue',
            style_modifiers=["character portrait", "emotional expression", "intimate scene"],
            lighting_adjustments="soft lighting, character focus",
            composition_hints="portrait composition, character emphasis",
            priority_keywords=["character", "emotion", "expression"]
        )
        
        mappings['landscape'] = SceneStyleMapping(
            scene_type='landscape',
            style_modifiers=["scenic landscape", "environmental beauty", "atmospheric"],
            lighting_adjustments="natural lighting, atmospheric mood",
            composition_hints="wide composition, environmental focus",
            priority_keywords=["landscape", "environment", "scenic"]
        )
        
        mappings['action'] = SceneStyleMapping(
            scene_type='action',
            style_modifiers=["dynamic action", "movement", "energy"],
            lighting_adjustments="dynamic lighting, motion emphasis",
            composition_hints="action composition, movement flow",
            priority_keywords=["action", "movement", "dynamic"]
        )
        
        return mappings

    def set_style_profile(self, style_name: str, custom_modifiers: Optional[Dict[str, Any]] = None):
        """Set the current style profile for consistency"""
        if style_name not in self.style_profiles:
            logger.warning(f"Unknown style profile: {style_name}, using realistic")
            style_name = 'realistic'
        
        self.current_style_profile = self.style_profiles[style_name]
        
        # Apply custom modifiers if provided
        if custom_modifiers:
            self._apply_custom_modifiers(custom_modifiers)
        
        logger.info(f"Set style profile to: {self.current_style_profile.name}")

    def _apply_custom_modifiers(self, modifiers: Dict[str, Any]):
        """Apply custom modifications to the current style profile"""
        if not self.current_style_profile:
            return
        
        # Update base prompt
        if 'base_prompt' in modifiers:
            self.current_style_profile.base_prompt = modifiers['base_prompt']
        
        # Update color palette
        if 'color_palette' in modifiers:
            self.current_style_profile.color_palette = modifiers['color_palette']
        
        # Update lighting style
        if 'lighting_style' in modifiers:
            self.current_style_profile.lighting_style = modifiers['lighting_style']

    def generate_consistent_prompt(self, base_prompt: str, scene_type: str = "general",
                                 emotional_tone: str = "neutral") -> str:
        """Generate a style-consistent prompt"""
        if not self.current_style_profile:
            logger.warning("No style profile set, using default")
            return base_prompt
        
        prompt_parts = [base_prompt]
        
        # Add scene-specific modifiers
        if scene_type in self.scene_style_mappings:
            mapping = self.scene_style_mappings[scene_type]
            prompt_parts.extend(mapping.style_modifiers)
            prompt_parts.append(mapping.lighting_adjustments)
            prompt_parts.append(mapping.composition_hints)
        
        # Add style profile elements
        prompt_parts.append(self.current_style_profile.base_prompt)
        prompt_parts.extend(self.current_style_profile.color_palette)
        prompt_parts.append(self.current_style_profile.lighting_style)
        prompt_parts.append(self.current_style_profile.composition_style)
        
        # Add emotional tone modifiers
        tone_modifiers = self._get_tone_modifiers(emotional_tone)
        prompt_parts.extend(tone_modifiers)
        
        # Add quality modifiers
        prompt_parts.extend(self.current_style_profile.quality_modifiers)
        
        # Add consistency keywords
        prompt_parts.extend(self.current_style_profile.consistency_keywords)
        
        # Combine and clean up
        full_prompt = ", ".join(filter(None, prompt_parts))
        full_prompt = self._clean_prompt(full_prompt)
        
        # Store for consistency analysis
        self.generated_prompts.append({
            'prompt': full_prompt,
            'scene_type': scene_type,
            'emotional_tone': emotional_tone,
            'style_profile': self.current_style_profile.name
        })
        
        return full_prompt

    def _get_tone_modifiers(self, emotional_tone: str) -> List[str]:
        """Get style modifiers based on emotional tone"""
        tone_mappings = {
            'dramatic': ["dramatic atmosphere", "intense mood", "powerful emotion"],
            'epic': ["epic scale", "heroic mood", "grand composition"],
            'peaceful': ["serene atmosphere", "calm mood", "gentle lighting"],
            'mysterious': ["mysterious atmosphere", "enigmatic mood", "shadowy"],
            'romantic': ["romantic mood", "warm atmosphere", "soft lighting"],
            'action': ["dynamic energy", "intense action", "movement"],
            'comedic': ["light-hearted", "bright colors", "playful composition"]
        }
        
        return tone_mappings.get(emotional_tone, ["balanced mood"])

    def _clean_prompt(self, prompt: str) -> str:
        """Clean and optimize the prompt"""
        # Remove duplicates while preserving order
        parts = prompt.split(", ")
        seen = set()
        cleaned_parts = []
        
        for part in parts:
            part = part.strip()
            if part and part.lower() not in seen:
                seen.add(part.lower())
                cleaned_parts.append(part)
        
        return ", ".join(cleaned_parts)

    def get_negative_prompt(self) -> str:
        """Get negative prompt for current style"""
        if not self.current_style_profile:
            return "low quality, blurry, distorted"
        
        return ", ".join(self.current_style_profile.negative_prompts)

    def analyze_consistency(self) -> Dict[str, Any]:
        """Analyze consistency across generated prompts"""
        if not self.generated_prompts:
            return {}
        
        # Count style elements
        style_counts = {}
        scene_counts = {}
        tone_counts = {}
        
        for prompt_data in self.generated_prompts:
            style = prompt_data['style_profile']
            scene = prompt_data['scene_type']
            tone = prompt_data['emotional_tone']
            
            style_counts[style] = style_counts.get(style, 0) + 1
            scene_counts[scene] = scene_counts.get(scene, 0) + 1
            tone_counts[tone] = tone_counts.get(tone, 0) + 1
        
        # Calculate consistency metrics
        total_prompts = len(self.generated_prompts)
        style_consistency = max(style_counts.values()) / total_prompts if style_counts else 0
        
        # Check for common keywords across prompts
        all_keywords = []
        for prompt_data in self.generated_prompts:
            keywords = re.findall(r'\b\w+\b', prompt_data['prompt'].lower())
            all_keywords.extend(keywords)
        
        keyword_frequency = {}
        for keyword in all_keywords:
            keyword_frequency[keyword] = keyword_frequency.get(keyword, 0) + 1
        
        common_keywords = {k: v for k, v in keyword_frequency.items() 
                          if v >= total_prompts * 0.5}  # Keywords in 50%+ of prompts
        
        return {
            'total_prompts': total_prompts,
            'style_consistency': style_consistency,
            'style_distribution': style_counts,
            'scene_distribution': scene_counts,
            'tone_distribution': tone_counts,
            'common_keywords': common_keywords,
            'consistency_score': self._calculate_consistency_score()
        }

    def _calculate_consistency_score(self) -> float:
        """Calculate overall consistency score (0.0 to 1.0)"""
        if not self.generated_prompts:
            return 0.0
        
        score = 0.0
        
        # Style consistency (40% weight)
        style_counts = {}
        for prompt_data in self.generated_prompts:
            style = prompt_data['style_profile']
            style_counts[style] = style_counts.get(style, 0) + 1
        
        if style_counts:
            max_style_count = max(style_counts.values())
            style_consistency = max_style_count / len(self.generated_prompts)
            score += style_consistency * 0.4
        
        # Keyword consistency (30% weight)
        if self.current_style_profile:
            consistency_keywords = self.current_style_profile.consistency_keywords
            keyword_presence = 0
            
            for prompt_data in self.generated_prompts:
                prompt_lower = prompt_data['prompt'].lower()
                present_keywords = sum(1 for kw in consistency_keywords if kw in prompt_lower)
                keyword_presence += present_keywords / len(consistency_keywords)
            
            keyword_consistency = keyword_presence / len(self.generated_prompts)
            score += keyword_consistency * 0.3
        
        # Quality modifier consistency (30% weight)
        if self.current_style_profile:
            quality_modifiers = self.current_style_profile.quality_modifiers
            quality_presence = 0
            
            for prompt_data in self.generated_prompts:
                prompt_lower = prompt_data['prompt'].lower()
                present_modifiers = sum(1 for mod in quality_modifiers 
                                      if any(word in prompt_lower for word in mod.split()))
                quality_presence += present_modifiers / len(quality_modifiers)
            
            quality_consistency = quality_presence / len(self.generated_prompts)
            score += quality_consistency * 0.3
        
        return min(score, 1.0)

    def get_style_recommendations(self, scene_types: List[str], 
                                emotional_tones: List[str]) -> Dict[str, Any]:
        """Get style recommendations based on scene analysis"""
        recommendations = {}
        
        # Analyze scene type distribution
        scene_counter = {}
        for scene in scene_types:
            scene_counter[scene] = scene_counter.get(scene, 0) + 1
        
        dominant_scene = max(scene_counter, key=scene_counter.get) if scene_counter else "general"
        
        # Analyze emotional tone distribution
        tone_counter = {}
        for tone in emotional_tones:
            tone_counter[tone] = tone_counter.get(tone, 0) + 1
        
        dominant_tone = max(tone_counter, key=tone_counter.get) if tone_counter else "neutral"
        
        # Recommend style based on dominant characteristics
        if dominant_scene == "battle" and dominant_tone in ["dramatic", "epic"]:
            recommended_style = "dramatic"
        elif dominant_scene == "dialogue" and dominant_tone == "peaceful":
            recommended_style = "cinematic"
        elif "historical" in str(scene_types).lower():
            recommended_style = "historical"
        elif dominant_tone == "mysterious":
            recommended_style = "fantasy"
        else:
            recommended_style = "realistic"
        
        recommendations = {
            'recommended_style': recommended_style,
            'dominant_scene': dominant_scene,
            'dominant_tone': dominant_tone,
            'scene_distribution': scene_counter,
            'tone_distribution': tone_counter,
            'style_reasoning': self._get_style_reasoning(recommended_style, dominant_scene, dominant_tone)
        }
        
        return recommendations

    def _get_style_reasoning(self, style: str, scene: str, tone: str) -> str:
        """Get reasoning for style recommendation"""
        reasoning_map = {
            'realistic': f"Realistic style recommended for balanced {scene} scenes with {tone} tone",
            'cinematic': f"Cinematic style recommended for {scene} scenes requiring dramatic {tone} presentation",
            'historical': f"Historical style recommended to maintain period accuracy for {scene} scenes",
            'fantasy': f"Fantasy style recommended for {tone} {scene} scenes with magical elements",
            'dramatic': f"Dramatic style recommended for intense {scene} scenes with {tone} emotional impact"
        }
        
        return reasoning_map.get(style, f"Style selected based on {scene} scene type and {tone} tone")

    def export_style_data(self, output_path: str):
        """Export style consistency data to JSON file"""
        data = {
            'current_style_profile': {
                'name': self.current_style_profile.name if self.current_style_profile else None,
                'category': self.current_style_profile.category.value if self.current_style_profile else None
            },
            'generated_prompts': self.generated_prompts,
            'consistency_analysis': self.analyze_consistency(),
            'available_styles': list(self.style_profiles.keys()),
            'scene_mappings': {k: {
                'scene_type': v.scene_type,
                'style_modifiers': v.style_modifiers,
                'priority_keywords': v.priority_keywords
            } for k, v in self.scene_style_mappings.items()}
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Style consistency data exported to {output_path}")

    def reset(self):
        """Reset the style manager for a new generation session"""
        self.generated_prompts = []
        logger.info("Style consistency manager reset")
