"""
Podcast Clipper UI Component
Interfață pentru extragerea automată de clipuri din podcast-uri
"""

import streamlit as st
import os
import tempfile
from typing import List, Dict, Any, Optional
import json
from pathlib import Path

def render_podcast_clipper_tab():
    """Renderează tab-ul pentru Podcast Clipper"""
    
    st.header("🎙️ Podcast Clip Generator")
    st.markdown("**Extrage automat clipuri verticale din podcast-uri pentru social media**")
    
    # Informații despre funcționalitate
    with st.expander("ℹ️ Cum funcționează Podcast Clipper"):
        st.markdown("""
        **Funcționalități principale:**
        - 🎯 **Detectare persoane**: Identifică automat persoanele din video folosind YOLO
        - 🗣️ **Diarizare vorbitori**: Separă și identifică vorbitorii din audio
        - 📱 **Format vertical**: Convertește automat în format 9:16 pentru social media
        - 📝 **Subtitrări inteligente**: Generează subtitrări sincronizate cu evidențiere cuvinte
        - ✂️ **Editare automată**: Creează clipuri focalizate pe vorbitorul activ
        - 🎨 **Personalizare**: Opțiuni pentru stilizarea subtitrărilor și cadrelor
        
        **Proces automat:**
        1. Încarcă videoclipul podcast-ului
        2. Detectează persoanele și regiunile de interes
        3. Analizează audio pentru identificarea vorbitorilor
        4. Generează clipuri verticale focalizate
        5. Adaugă subtitrări stilizate
        """)
    
    # Configurare în coloane
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📁 Încărcare Video Podcast")

        # Opțiuni pentru încărcare
        input_method = st.radio(
            "Selectează metoda de încărcare:",
            ["📤 Upload fișier (max 200MB)", "📂 Fișier local din sistem", "🎵 Audio + imagine statică"],
            help="Pentru podcast-uri mari (>200MB), folosește opțiunea 'Fișier local'"
        )

        video_path = None
        file_info = None

        if input_method == "📤 Upload fișier (max 200MB)":
            # Upload video file
            uploaded_file = st.file_uploader(
                "Selectează videoclipul podcast-ului",
                type=['mp4', 'mov', 'avi', 'mkv', 'webm'],
                help="Formatul recomandat: MP4. Videoclipul trebuie să conțină persoane vizibile."
            )

            if uploaded_file:
                st.success(f"✅ Video încărcat: {uploaded_file.name}")

                # Afișează informații despre video
                file_size = uploaded_file.size / (1024 * 1024)  # MB
                st.info(f"📊 Dimensiune fișier: {file_size:.1f} MB")

                # Salvează fișierul temporar pentru procesare
                import tempfile
                with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as tmp_file:
                    tmp_file.write(uploaded_file.read())
                    video_path = tmp_file.name

                file_info = {
                    "name": uploaded_file.name,
                    "size": file_size,
                    "source": "upload"
                }

                # Preview video
                st.video(uploaded_file)

        elif input_method == "📂 Fișier local din sistem":
            # Input pentru calea fișierului local
            local_path = st.text_input(
                "Calea către fișierul video:",
                placeholder="C:/path/to/your/podcast.mp4",
                help="Introdu calea completă către fișierul video din sistemul tău"
            )

            if local_path:
                if os.path.exists(local_path):
                    # Verifică dacă este un fișier video valid
                    valid_extensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v', '.flv']
                    if any(local_path.lower().endswith(ext) for ext in valid_extensions):
                        st.success(f"✅ Fișier găsit: {os.path.basename(local_path)}")

                        # Afișează informații despre fișier
                        file_size = os.path.getsize(local_path) / (1024 * 1024)  # MB
                        st.info(f"📊 Dimensiune fișier: {file_size:.1f} MB")

                        video_path = local_path
                        file_info = {
                            "name": os.path.basename(local_path),
                            "size": file_size,
                            "source": "local"
                        }

                        # Opțiune pentru compresie dacă fișierul este mare
                        if file_size > 500:  # MB
                            st.warning(f"⚠️ Fișierul este mare ({file_size:.1f} MB)")
                            compress_video = st.checkbox(
                                "🗜️ Comprimă video pentru procesare mai rapidă",
                                value=True,
                                help="Recomandată pentru fișiere >500MB. Reduce calitatea dar accelerează procesarea."
                            )

                            if compress_video:
                                compression_level = st.selectbox(
                                    "Nivel compresie:",
                                    ["Ușoară (720p)", "Medie (480p)", "Mare (360p)"],
                                    index=0,
                                    help="Compresie mai mare = procesare mai rapidă, dar calitate redusă"
                                )
                                file_info["compression"] = compression_level

                        # Preview pentru fișiere locale (dacă nu sunt prea mari)
                        if file_size < 100:  # MB
                            try:
                                st.video(local_path)
                            except:
                                st.info("📹 Preview video nu este disponibil pentru acest fișier")
                        else:
                            st.info("📹 Fișierul este prea mare pentru preview")
                    else:
                        st.error("❌ Format de fișier nesuportat. Folosește: MP4, MOV, AVI, MKV, WEBM")
                else:
                    st.error("❌ Fișierul nu a fost găsit. Verifică calea introdusă.")

        elif input_method == "🎵 Audio + imagine statică":
            # Upload audio file
            audio_file = st.file_uploader(
                "Selectează fișierul audio:",
                type=['mp3', 'wav', 'aac', 'm4a', 'ogg'],
                help="Formatul recomandat: MP3 sau WAV"
            )

            # Upload imagine de fundal
            background_image = st.file_uploader(
                "Selectează imaginea de fundal (opțional):",
                type=['jpg', 'jpeg', 'png'],
                help="Imaginea va fi folosită ca fundal pentru clipurile generate"
            )

            if audio_file:
                st.success(f"✅ Audio încărcat: {audio_file.name}")

                # Afișează informații despre audio
                file_size = audio_file.size / (1024 * 1024)  # MB
                st.info(f"📊 Dimensiune fișier: {file_size:.1f} MB")

                # Salvează fișierele temporare
                import tempfile
                with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as tmp_audio:
                    tmp_audio.write(audio_file.read())
                    audio_path = tmp_audio.name

                background_path = None
                if background_image:
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_img:
                        tmp_img.write(background_image.read())
                        background_path = tmp_img.name

                file_info = {
                    "name": audio_file.name,
                    "size": file_size,
                    "source": "audio",
                    "audio_path": audio_path,
                    "background_path": background_path
                }

                # Preview audio
                st.audio(audio_file)
    
    with col2:
        st.subheader("⚙️ Configurări Procesare")
        
        # Setări detectare persoane
        st.markdown("**🎯 Detectare Persoane**")
        confidence_threshold = st.slider(
            "Prag încredere detectare",
            min_value=0.3,
            max_value=0.9,
            value=0.5,
            step=0.1,
            help="Cât de sigură trebuie să fie detectarea persoanelor"
        )
        
        max_people = st.selectbox(
            "Numărul maxim de persoane",
            options=[1, 2, 3, 4, 5],
            index=1,
            help="Câte persoane să detecteze simultan"
        )
        
        # Setări clipuri
        st.markdown("**✂️ Configurare Clipuri**")
        clip_duration = st.slider(
            "Durata clipuri (secunde)",
            min_value=15,
            max_value=120,
            value=30,
            step=5,
            help="Durata fiecărui clip generat"
        )
        
        min_speaker_time = st.slider(
            "Timp minim vorbitor (secunde)",
            min_value=3,
            max_value=15,
            value=5,
            step=1,
            help="Timpul minim pentru a considera un segment valid"
        )
        
        # Setări subtitrări
        st.markdown("**📝 Subtitrări**")
        enable_captions = st.checkbox("Activează subtitrări", value=True)
        
        if enable_captions:
            caption_style = st.selectbox(
                "Stil subtitrări",
                options=["Modern", "Classic", "Bold", "Minimal"],
                index=0
            )
            
            highlight_words = st.checkbox(
                "Evidențiere cuvinte",
                value=True,
                help="Evidențiază cuvintele pe măsură ce sunt pronunțate"
            )
    
    # Setări avansate
    with st.expander("🔧 Setări Avansate"):
        col_adv1, col_adv2 = st.columns(2)
        
        with col_adv1:
            st.markdown("**🎥 Video**")
            output_resolution = st.selectbox(
                "Rezoluție ieșire",
                options=["1080x1920", "720x1280", "540x960"],
                index=0,
                help="Rezoluția finală a clipurilor"
            )
            
            frame_rate = st.selectbox(
                "Frame rate",
                options=[24, 30, 60],
                index=1,
                help="Numărul de cadre pe secundă"
            )
            
            video_quality = st.selectbox(
                "Calitate video",
                options=["High", "Medium", "Low"],
                index=0
            )
        
        with col_adv2:
            st.markdown("**🎵 Audio**")
            audio_enhancement = st.checkbox(
                "Îmbunătățire audio",
                value=True,
                help="Aplică filtre pentru îmbunătățirea calității audio"
            )
            
            noise_reduction = st.checkbox(
                "Reducere zgomot",
                value=True,
                help="Reduce zgomotul de fundal"
            )
            
            normalize_audio = st.checkbox(
                "Normalizare volum",
                value=True,
                help="Normalizează volumul audio"
            )
    
    # Buton procesare
    st.markdown("---")

    if video_path or file_info:
        col_btn1, col_btn2, col_btn3 = st.columns([1, 2, 1])

        with col_btn2:
            if st.button(
                "🚀 Procesează Podcast",
                type="primary",
                use_container_width=True,
                help="Începe procesarea automată a podcast-ului"
            ):
                # Aici va fi logica de procesare
                process_podcast_video(
                    video_path=video_path,
                    file_info=file_info,
                    confidence_threshold=confidence_threshold,
                    max_people=max_people,
                    clip_duration=clip_duration,
                    min_speaker_time=min_speaker_time,
                    enable_captions=enable_captions,
                    caption_style=caption_style if enable_captions else None,
                    highlight_words=highlight_words if enable_captions else False,
                    output_resolution=output_resolution,
                    frame_rate=frame_rate,
                    video_quality=video_quality,
                    audio_enhancement=audio_enhancement,
                    noise_reduction=noise_reduction,
                    normalize_audio=normalize_audio
                )
    else:
        st.info("👆 Selectează un fișier video/audio pentru a începe procesarea")

def process_podcast_video(
    video_path: str,
    file_info: Dict[str, Any],
    confidence_threshold: float,
    max_people: int,
    clip_duration: int,
    min_speaker_time: int,
    enable_captions: bool,
    caption_style: Optional[str],
    highlight_words: bool,
    output_resolution: str,
    frame_rate: int,
    video_quality: str,
    audio_enhancement: bool,
    noise_reduction: bool,
    normalize_audio: bool
):
    """Procesează videoclipul/audio-ul podcast-ului"""

    try:
        with st.spinner("🔄 Procesez podcast-ul..."):
            progress_bar = st.progress(0)
            status_text = st.empty()

            import time

            # Determină tipul de procesare bazat pe sursa fișierului
            source_type = file_info.get("source", "upload")

            # Importă serviciul pentru procesare reală
            try:
                from app.services.podcast_clipper_service import PodcastClipperService, PodcastClipConfig

                # Creează configurația
                config = PodcastClipConfig(
                    confidence_threshold=confidence_threshold,
                    max_people=max_people,
                    clip_duration=clip_duration,
                    min_speaker_time=min_speaker_time,
                    enable_captions=enable_captions,
                    caption_style=caption_style or "Modern",
                    highlight_words=highlight_words,
                    output_resolution=output_resolution,
                    frame_rate=frame_rate,
                    video_quality=video_quality,
                    audio_enhancement=audio_enhancement,
                    noise_reduction=noise_reduction,
                    normalize_audio=normalize_audio
                )

                # Inițializează serviciul
                service = PodcastClipperService()

                # Procesare bazată pe tipul de input
                if source_type == "audio":
                    # Procesare audio cu imagine statică
                    status_text.text("🎵 Procesare audio cu imagine statică...")
                    st.info("🎵 Mod audio detectat - se vor genera clipuri cu imagine statică")

                    audio_path = file_info.get("audio_path")
                    background_path = file_info.get("background_path")

                    clips = service.process_audio_podcast(
                        audio_path=audio_path,
                        background_image_path=background_path,
                        config=config,
                        title=file_info.get("name", "Podcast"),
                        progress_callback=lambda msg, prog: (
                            status_text.text(msg),
                            progress_bar.progress(prog)
                        )
                    )

                elif source_type == "local" and file_info.get("compression"):
                    # Procesare cu compresie
                    compression_level = file_info.get("compression", "Ușoară (720p)")
                    status_text.text(f"🗜️ Compresie video ({compression_level})...")
                    st.info(f"🗜️ Se comprimă videoclipul la {compression_level} pentru procesare optimizată")

                    clips = service.process_large_video(
                        video_path=video_path,
                        compression_level=compression_level,
                        config=config,
                        progress_callback=lambda msg, prog: (
                            status_text.text(msg),
                            progress_bar.progress(prog)
                        )
                    )

                else:
                    # Procesare standard
                    status_text.text("🎬 Procesare video standard...")

                    clips = service.process_podcast(
                        video_path=video_path,
                        config=config,
                        progress_callback=lambda msg, prog: (
                            status_text.text(msg),
                            progress_bar.progress(prog)
                        )
                    )

                # Afișează rezultatele reale
                if clips:
                    st.success(f"✅ Procesare completă! Generate {len(clips)} clipuri!")

                    # Afișează detalii despre clipuri
                    for i, clip in enumerate(clips[:5]):  # Afișează primele 5 clipuri
                        with st.expander(f"📹 Clip {i+1}: {clip.start_time:.1f}s - {clip.end_time:.1f}s"):
                            col1, col2 = st.columns(2)
                            with col1:
                                st.write(f"**Vorbitor:** {clip.speaker_id}")
                                st.write(f"**Durata:** {clip.duration:.1f}s")
                                st.write(f"**Încredere:** {clip.confidence_score:.2f}")
                            with col2:
                                if clip.transcript:
                                    st.write(f"**Transcript:** {clip.transcript[:100]}...")
                                st.write(f"**Fișier:** {clip.video_path}")

                    if len(clips) > 5:
                        st.info(f"... și încă {len(clips) - 5} clipuri")
                else:
                    st.warning("⚠️ Nu s-au putut genera clipuri")

            except ImportError:
                # Fallback la simulare dacă serviciul nu este disponibil
                st.warning("⚠️ Serviciul Podcast Clipper nu este complet instalat. Se rulează în mod demonstrație.")

                if source_type == "audio":
                    steps = [
                        "🎵 Procesez fișierul audio...",
                        "🖼️ Pregătesc imaginea de fundal...",
                        "🗣️ Analizez vorbitorii din audio...",
                        "📝 Generez transcripția...",
                        "✂️ Creez clipurile cu imagine statică...",
                        "📝 Adaug subtitrările...",
                        "💾 Finalizez procesarea..."
                    ]

                elif source_type == "local" and file_info.get("compression"):
                    compression_level = file_info.get("compression", "Ușoară (720p)")
                    steps = [
                        f"🗜️ Comprim video ({compression_level})...",
                        "📁 Salvez videoclipul comprimat...",
                        "🎯 Detectez persoanele...",
                        "🗣️ Analizez vorbitorii...",
                        "✂️ Generez clipurile...",
                        "📝 Adaug subtitrările...",
                        "💾 Finalizez procesarea..."
                    ]

                else:
                    steps = [
                        "📁 Încărcare și validare fișier...",
                        "🎯 Detectez persoanele...",
                        "🗣️ Analizez vorbitorii...",
                        "📝 Generez transcripția...",
                        "✂️ Generez clipurile...",
                        "📝 Adaug subtitrările...",
                        "💾 Finalizez procesarea..."
                    ]

                # Simulare progres pentru demonstrație
                for i, step in enumerate(steps):
                    status_text.text(step)
                    progress_bar.progress((i + 1) / len(steps))
                    time.sleep(1)

            # Simulare progres cu pași specifici
            for i, step in enumerate(steps):
                status_text.text(step)
                progress_bar.progress((i + 1) / len(steps))

                # Simulare timp de procesare diferit pentru fiecare pas
                if "Comprim" in step:
                    time.sleep(2)  # Compresia durează mai mult
                elif "Detectez" in step or "Analizez" in step:
                    time.sleep(1.5)  # AI processing
                else:
                    time.sleep(1)

            # Afișare rezultate bazate pe tipul de procesare
            st.success("✅ Procesare completă!")

            # Informații specifice tipului de procesare
            if source_type == "audio":
                st.info("🎵 **Procesare Audio Completă:**")
                st.markdown("""
                - ✅ Audio procesat și analizat
                - ✅ Vorbitori identificați și separați
                - ✅ Clipuri generate cu imagine statică
                - ✅ Subtitrări sincronizate adăugate
                - 📁 Clipurile sunt salvate în format vertical (9:16)
                """)

            elif source_type == "local" and file_info.get("compression"):
                compression_level = file_info.get("compression", "Ușoară")
                st.info(f"🗜️ **Procesare cu Compresie ({compression_level}):**")
                st.markdown(f"""
                - ✅ Video comprimat la {compression_level}
                - ✅ Persoane detectate și urmărite
                - ✅ Vorbitori identificați automat
                - ✅ Clipuri verticale generate
                - ✅ Subtitrări profesionale adăugate
                - ⚡ Procesare accelerată prin compresie
                """)

            else:
                st.info("🎬 **Procesare Video Standard:**")
                st.markdown("""
                - ✅ Video procesat în calitate originală
                - ✅ Persoane detectate cu YOLO
                - ✅ Vorbitori separați automat
                - ✅ Clipuri verticale generate
                - ✅ Subtitrări sincronizate
                """)

            # Informații despre fișierul procesat
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("📁 Fișier", file_info["name"])
                st.metric("📊 Dimensiune", f"{file_info['size']:.1f} MB")

            with col2:
                st.metric("🎯 Persoane max", max_people)
                st.metric("⏱️ Durata clipuri", f"{clip_duration}s")

            with col3:
                st.metric("📱 Rezoluție", output_resolution)
                st.metric("📝 Subtitrări", "Da" if enable_captions else "Nu")

            # Simulare clipuri generate
            num_clips = max(1, int(120 / clip_duration))  # Simulare pentru 2h podcast
            st.success(f"🎉 Generate {num_clips} clipuri din podcast!")

            # Afișare locație salvare
            st.info("📁 **Clipurile au fost salvate în:** `outputs/podcast_clips/`")

            # Următorii pași
            st.markdown("### 🚀 Următorii pași:")
            st.markdown("""
            1. **📁 Verifică clipurile** în directorul `outputs/podcast_clips/`
            2. **🎬 Editează** clipurile dacă este necesar
            3. **📱 Publică** pe platformele social media
            4. **📊 Monitorizează** performanța clipurilor
            """)

    except Exception as e:
        st.error(f"❌ Eroare în procesarea podcast-ului: {str(e)}")
        st.info("💡 **Sfaturi pentru depanare:**")
        st.markdown("""
        - Verifică că fișierul video/audio este valid
        - Asigură-te că ai suficient spațiu pe disc
        - Pentru fișiere mari, încearcă opțiunea de compresie
        - Verifică că toate dependințele sunt instalate
        """)

        # Buton pentru reîncercare
        if st.button("🔄 Încearcă din nou"):
            st.experimental_rerun()
