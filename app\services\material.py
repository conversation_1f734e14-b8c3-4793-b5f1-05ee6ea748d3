import os
import random
import asyncio
from typing import List, Optional
from urllib.parse import urlencode

import requests
from loguru import logger

# Apply PIL compatibility patch before importing MoviePy and PIL
try:
    from PIL import Image
    # Check if ANTIALIAS exists, if not, patch it
    if not hasattr(Image, 'ANTIALIAS'):
        if hasattr(Image, 'Resampling') and hasattr(Image.Resampling, 'LANCZOS'):
            Image.ANTIALIAS = Image.Resampling.LANCZOS
            logger.info("✅ Applied PIL.Image.ANTIALIAS compatibility patch in material service")
        elif hasattr(Image, 'LANC<PERSON>OS'):
            Image.ANTIALIAS = Image.LANCZOS
            logger.info("✅ Applied PIL.Image.ANTIALIAS compatibility patch in material service")
except Exception as e:
    logger.error(f"❌ PIL compatibility patch failed in material service: {e}")

from moviepy.video.io.VideoFileClip import VideoFileClip
from moviepy.editor import ImageClip
from io import BytesIO

from app.config import config
from app.models.schema import MaterialInfo, VideoAspect, VideoConcatMode
from app.utils import utils

# Import AI video source manager
try:
    from .ai_video_source_manager import AIVideoSourceManager, AIImageConfig, AIProvider
    AI_VIDEO_SOURCE_AVAILABLE = True
except ImportError:
    AI_VIDEO_SOURCE_AVAILABLE = False
    logger.warning("AI Video Source Manager not available")

requested_count = 0


def get_api_key(cfg_key: str):
    api_keys = config.app.get(cfg_key)
    if not api_keys:
        raise ValueError(
            f"\n\n##### {cfg_key} is not set #####\n\nPlease set it in the config.toml file: {config.config_file}\n\n"
            f"{utils.to_json(config.app)}"
        )

    # if only one key is provided, return it
    if isinstance(api_keys, str):
        return api_keys

    global requested_count
    requested_count += 1
    return api_keys[requested_count % len(api_keys)]


def search_videos_pexels(
    search_term: str,
    minimum_duration: int,
    video_aspect: VideoAspect = VideoAspect.portrait,
) -> List[MaterialInfo]:
    aspect = VideoAspect(video_aspect)
    video_orientation = aspect.name
    video_width, video_height = aspect.to_resolution()
    api_key = get_api_key("pexels_api_keys")
    headers = {
        "Authorization": api_key,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    }
    # Build URL
    params = {"query": search_term, "per_page": 20, "orientation": video_orientation}
    query_url = f"https://api.pexels.com/videos/search?{urlencode(params)}"
    logger.info(f"searching videos: {query_url}, with proxies: {config.proxy}")

    try:
        r = requests.get(
            query_url,
            headers=headers,
            proxies=config.proxy,
            verify=False,
            timeout=(30, 60),
        )
        response = r.json()
        video_items = []
        if "videos" not in response:
            logger.error(f"search videos failed: {response}")
            return video_items
        videos = response["videos"]
        # loop through each video in the result
        for v in videos:
            duration = v["duration"]
            # check if video has desired minimum duration
            if duration < minimum_duration:
                continue
            video_files = v["video_files"]
            # loop through each url to determine the best quality
            for video in video_files:
                w = int(video["width"])
                h = int(video["height"])
                if w == video_width and h == video_height:
                    item = MaterialInfo()
                    item.provider = "pexels"
                    item.url = video["link"]
                    item.duration = duration
                    video_items.append(item)
                    break
        return video_items
    except Exception as e:
        logger.error(f"search videos failed: {str(e)}")

    return []


def search_videos_pixabay(
    search_term: str,
    minimum_duration: int,
    video_aspect: VideoAspect = VideoAspect.portrait,
) -> List[MaterialInfo]:
    aspect = VideoAspect(video_aspect)

    video_width, video_height = aspect.to_resolution()

    api_key = get_api_key("pixabay_api_keys")
    # Build URL
    params = {
        "q": search_term,
        "video_type": "all",  # Accepted values: "all", "film", "animation"
        "per_page": 50,
        "key": api_key,
    }
    query_url = f"https://pixabay.com/api/videos/?{urlencode(params)}"
    logger.info(f"searching videos: {query_url}, with proxies: {config.proxy}")

    try:
        r = requests.get(
            query_url, proxies=config.proxy, verify=False, timeout=(30, 60)
        )
        response = r.json()
        video_items = []
        if "hits" not in response:
            logger.error(f"search videos failed: {response}")
            return video_items
        videos = response["hits"]
        # loop through each video in the result
        for v in videos:
            duration = v["duration"]
            # check if video has desired minimum duration
            if duration < minimum_duration:
                continue
            video_files = v["videos"]
            # loop through each url to determine the best quality
            for video_type in video_files:
                video = video_files[video_type]
                w = int(video["width"])
                # h = int(video["height"])
                if w >= video_width:
                    item = MaterialInfo()
                    item.provider = "pixabay"
                    item.url = video["url"]
                    item.duration = duration
                    video_items.append(item)
                    break
        return video_items
    except Exception as e:
        logger.error(f"search videos failed: {str(e)}")

    return []


async def generate_ai_images(
    search_terms: List[str],
    video_aspect: VideoAspect = VideoAspect.portrait,
    style: str = "realistic",
    provider: str = "auto",
    script_context: str = "",
    count: int = 5,
    audio_duration: float = 30.0
) -> List[MaterialInfo]:
    """Generate AI images for video materials with advanced script analysis"""
    if not AI_VIDEO_SOURCE_AVAILABLE:
        logger.error("AI Video Source Manager not available")
        return []

    try:
        # Check if we have script context for advanced generation
        if script_context and len(script_context.strip()) > 50:
            logger.info("🎬 Using advanced script-based AI generation")
            return await _generate_ai_images_from_script(
                script_context, video_aspect, style, provider, audio_duration
            )
        else:
            logger.info("🎯 Using term-based AI generation")
            return await _generate_ai_images_from_terms(
                search_terms, video_aspect, style, provider, count
            )

    except Exception as e:
        logger.error(f"AI image generation failed: {e}")
        return []


async def _generate_ai_images_from_script(
    script: str,
    video_aspect: VideoAspect,
    style: str,
    provider: str,
    audio_duration: float
) -> List[MaterialInfo]:
    """Generate AI images using advanced script analysis"""
    try:
        from .ai_video_generator import AIVideoGenerator, AIVideoConfig
        from .ai_video_source_manager import AIProvider

        # Initialize advanced AI generator
        ai_generator = AIVideoGenerator()

        # Configure generation optimized for audio duration
        config = AIVideoConfig.create_for_audio_duration(
            audio_duration=audio_duration,
            clip_duration=3,  # 3 seconds per image for better coverage
            style=style,
            provider=AIProvider(provider) if provider != "auto" else AIProvider.AUTO,
            video_aspect=video_aspect,
            quality_threshold=0.3,
            enable_caching=True,
            fallback_enabled=True
        )

        # Generate materials
        result = await ai_generator.generate_video_materials(script, audio_duration, config)

        await ai_generator.cleanup()

        if result.success:
            logger.success(f"✅ Advanced AI generation: {result.images_generated} images in {result.total_generation_time:.2f}s")
            logger.info(f"📊 Segments analyzed: {result.segments_analyzed}")
            logger.info(f"💾 Cache hits: {result.cache_hits}")

            # Convert materials to video clips
            video_materials = []
            for material in result.materials:
                if os.path.exists(material.url):
                    # Convert AI image to video clip
                    video_path = await save_ai_image_as_video(
                        open(material.url, 'rb').read(),
                        f"ai_segment_{len(video_materials)}",
                        video_aspect,
                        duration=int(material.duration)
                    )

                    if video_path:
                        video_material = MaterialInfo()
                        video_material.provider = material.provider
                        video_material.url = video_path
                        video_material.duration = int(material.duration)
                        video_materials.append(video_material)

            return video_materials
        else:
            logger.warning(f"❌ Advanced AI generation failed: {result.error_message}")
            return []

    except Exception as e:
        logger.error(f"Advanced AI generation error: {e}")
        return []


async def _generate_ai_images_from_terms(
    search_terms: List[str],
    video_aspect: VideoAspect,
    style: str,
    provider: str,
    count: int
) -> List[MaterialInfo]:
    """Generate AI images using traditional term-based approach"""
    try:
        # Initialize AI manager
        ai_manager = AIVideoSourceManager()

        # Configure for video aspect ratio
        config = AIImageConfig()
        config.to_video_aspect_resolution(video_aspect.name)
        config.style = style
        config.provider = AIProvider(provider) if provider != "auto" else AIProvider.AUTO

        ai_materials = []

        # Ensure we generate enough images for the requested count
        terms_to_use = search_terms[:count]
        if len(terms_to_use) < count:
            # Repeat terms if we don't have enough unique terms
            while len(terms_to_use) < count:
                terms_to_use.extend(search_terms[:min(len(search_terms), count - len(terms_to_use))])

        for i, term in enumerate(terms_to_use):
            try:
                logger.info(f"🎨 Generating AI image {i+1}/{count} for term: {term}")

                # Generate image based on search term
                result = await ai_manager.generate_image_from_topic(term, config)

                if result.success and result.image_data:
                    # Save AI image as video material
                    image_path = await save_ai_image_as_video(
                        result.image_data,
                        term,
                        video_aspect,
                        duration=5  # Default 5 seconds per image
                    )

                    if image_path:
                        material = MaterialInfo()
                        material.provider = f"ai_{result.provider_used}"
                        material.url = image_path  # Local file path
                        material.duration = 5  # 5 seconds duration
                        ai_materials.append(material)

                        logger.info(f"✅ Generated AI image material: {image_path}")
                    else:
                        logger.error(f"Failed to save AI image for term: {term}")
                else:
                    logger.warning(f"❌ Failed to generate AI image for: {term} - {result.error_message}")

            except Exception as e:
                logger.error(f"Error generating AI image for term '{term}': {e}")

        await ai_manager.cleanup()
        logger.info(f"Generated {len(ai_materials)} AI images using term-based approach")
        return ai_materials

    except Exception as e:
        logger.error(f"Term-based AI generation failed: {e}")
        return []


async def save_ai_image_as_video(
    image_data: bytes,
    term: str,
    video_aspect: VideoAspect,
    duration: int = 5,
    save_dir: str = ""
) -> str:
    """Convert AI image to video clip and save"""
    try:
        if not save_dir:
            save_dir = utils.storage_dir("cache_videos")

        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # Generate unique filename
        term_hash = utils.md5(term + str(len(image_data)))
        video_path = f"{save_dir}/ai-{term_hash}.mp4"

        # Check if already exists
        if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
            logger.info(f"AI video already exists: {video_path}")
            return video_path

        # Convert image data to PIL Image
        image = Image.open(BytesIO(image_data))

        # Ensure RGB mode
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Get target resolution
        aspect = VideoAspect(video_aspect)
        target_width, target_height = aspect.to_resolution()

        # Resize image to target resolution
        image = image.resize((target_width, target_height), Image.Resampling.LANCZOS)

        # Convert PIL image to numpy array
        import numpy as np
        image_array = np.array(image)

        # Create video clip from image
        clip = ImageClip(image_array, duration=duration)
        clip = clip.set_fps(30)

        # Write video file
        clip.write_videofile(
            video_path,
            codec='libx264',
            audio_codec='aac',
            fps=30,
            verbose=False,
            logger=None
        )

        # Cleanup
        clip.close()

        # Verify the video was created successfully
        if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
            try:
                # Quick validation
                test_clip = VideoFileClip(video_path)
                test_duration = test_clip.duration
                test_fps = test_clip.fps
                test_clip.close()

                if test_duration > 0 and test_fps > 0:
                    logger.info(f"AI video created successfully: {video_path}")
                    return video_path
            except Exception as e:
                logger.error(f"AI video validation failed: {e}")
                try:
                    os.remove(video_path)
                except Exception:
                    pass

        return ""

    except Exception as e:
        logger.error(f"Error saving AI image as video: {e}")
        return ""


def save_video(video_url: str, save_dir: str = "") -> str:
    if not save_dir:
        save_dir = utils.storage_dir("cache_videos")

    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    url_without_query = video_url.split("?")[0]
    url_hash = utils.md5(url_without_query)
    video_id = f"vid-{url_hash}"
    video_path = f"{save_dir}/{video_id}.mp4"

    # if video already exists, return the path
    if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
        logger.info(f"video already exists: {video_path}")
        return video_path

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }

    # if video does not exist, download it
    with open(video_path, "wb") as f:
        f.write(
            requests.get(
                video_url,
                headers=headers,
                proxies=config.proxy,
                verify=False,
                timeout=(60, 240),
            ).content
        )

    if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
        try:
            clip = VideoFileClip(video_path)
            duration = clip.duration
            fps = clip.fps
            clip.close()
            if duration > 0 and fps > 0:
                return video_path
        except Exception as e:
            try:
                os.remove(video_path)
            except Exception:
                pass
            logger.warning(f"invalid video file: {video_path} => {str(e)}")
    return ""


def download_videos(
    task_id: str,
    search_terms: List[str],
    source: str = "pexels",
    video_aspect: VideoAspect = VideoAspect.portrait,
    video_contact_mode: VideoConcatMode = VideoConcatMode.random,
    audio_duration: float = 0.0,
    max_clip_duration: int = 5,
    script_context: str = "",
    ai_style: str = "realistic",
    ai_provider: str = "auto"
) -> List[str]:
    valid_video_items = []
    valid_video_urls = []
    found_duration = 0.0

    # Handle AI image generation
    if source.startswith("ai_"):
        logger.info(f"\n\n## generating AI images for video materials")
        try:
            # Extract AI provider from source (e.g., "ai_perchance", "ai_openai", "ai_auto")
            ai_provider_name = source.replace("ai_", "") if source != "ai" else ai_provider

            # Generate AI images
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Calculate required images to cover full audio duration
            clip_duration = 3  # Use 3 seconds per clip for better coverage
            import math
            required_images = max(5, math.ceil(audio_duration / clip_duration) + 2)

            logger.info(f"🎬 Generating {required_images} AI images for {audio_duration:.1f}s audio (clip duration: {clip_duration}s)")

            ai_materials = loop.run_until_complete(
                generate_ai_images(
                    search_terms=search_terms,
                    video_aspect=video_aspect,
                    style=ai_style,
                    provider=ai_provider_name,
                    script_context=script_context,
                    count=required_images,
                    audio_duration=audio_duration
                )
            )

            loop.close()

            if ai_materials:
                valid_video_items.extend(ai_materials)
                logger.success(f"Generated {len(ai_materials)} AI image materials")
                # AI generation successful, skip traditional video search
            else:
                logger.error("No AI images generated, falling back to traditional sources")
                # Fallback to Pexels if AI generation fails
                source = "pexels"

        except Exception as e:
            logger.error(f"AI image generation failed: {e}, falling back to traditional sources")
            source = "pexels"

    # Handle traditional video sources (only if not AI or AI failed)
    if not source.startswith("ai_"):
        # Set default search function
        search_videos = search_videos_pexels
        if source == "pixabay":
            search_videos = search_videos_pixabay

        for search_term in search_terms:
            video_items = search_videos(
                search_term=search_term,
                minimum_duration=max_clip_duration,
                video_aspect=video_aspect,
            )
            logger.info(f"found {len(video_items)} videos for '{search_term}'")

            for item in video_items:
                if item.url not in valid_video_urls:
                    valid_video_items.append(item)
                    valid_video_urls.append(item.url)
                    found_duration += item.duration

    logger.info(
        f"found total videos: {len(valid_video_items)}, required duration: {audio_duration} seconds, found duration: {found_duration} seconds"
    )
    video_paths = []

    material_directory = config.app.get("material_directory", "").strip()
    if material_directory == "task":
        material_directory = utils.task_dir(task_id)
    elif material_directory and not os.path.isdir(material_directory):
        material_directory = ""

    if video_contact_mode.value == VideoConcatMode.random.value:
        random.shuffle(valid_video_items)

    total_duration = 0.0
    for item in valid_video_items:
        try:
            # Check if this is an AI-generated video (local file path)
            if hasattr(item, 'provider') and item.provider and item.provider.startswith('ai_'):
                # AI-generated video is already a local file, no need to download
                if os.path.exists(item.url):
                    logger.info(f"using AI-generated video: {item.url}")
                    video_paths.append(item.url)
                    seconds = min(max_clip_duration, item.duration)
                    total_duration += seconds
                else:
                    logger.warning(f"AI-generated video file not found: {item.url}")
            else:
                # Traditional video source, needs to be downloaded
                logger.info(f"downloading video: {item.url}")
                saved_video_path = save_video(
                    video_url=item.url, save_dir=material_directory
                )
                if saved_video_path:
                    logger.info(f"video saved: {saved_video_path}")
                    video_paths.append(saved_video_path)
                    seconds = min(max_clip_duration, item.duration)
                    total_duration += seconds

            if total_duration > audio_duration:
                logger.info(
                    f"total duration of downloaded videos: {total_duration} seconds, skip downloading more"
                )
                break
        except Exception as e:
            logger.error(f"failed to process video: {utils.to_json(item)} => {str(e)}")
    logger.success(f"downloaded {len(video_paths)} videos")
    return video_paths


if __name__ == "__main__":
    download_videos(
        "test123", ["Money Exchange Medium"], audio_duration=100, source="pixabay"
    )
