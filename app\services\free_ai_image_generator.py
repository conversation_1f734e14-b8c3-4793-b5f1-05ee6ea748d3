"""
Free AI Image Generator using multiple free services
Provides real AI image generation using free APIs and services
"""

import asyncio
import aiohttp
import logging
import time
import json
import base64
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class FreeAIConfig:
    """Configuration for free AI image generation"""
    width: int = 512
    height: int = 512
    style: str = "realistic"
    quality: str = "standard"
    steps: int = 20

class FreeAIImageGenerator:
    """Free AI image generator using multiple free services"""
    
    def __init__(self):
        self.session = None
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        self.services = [
            self._try_pollinations_ai,
            self._try_huggingface_spaces,
            self._try_craiyon,
            self._try_segmind_api,
        ]
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=60)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={"User-Agent": self.user_agent}
            )
        return self.session
    
    async def generate_image(self, prompt: str, width: int = 512, height: int = 512) -> Optional[bytes]:
        """Generate image using free AI services"""
        try:
            # Try each service in order
            for service in self.services:
                try:
                    result = await service(prompt, width, height)
                    if result:
                        logger.info(f"Successfully generated image with {service.__name__}")
                        return result
                except Exception as e:
                    logger.debug(f"Service {service.__name__} failed: {e}")
                    continue
            
            logger.warning("All free AI services failed")
            return None
            
        except Exception as e:
            logger.error(f"Free AI generation error: {e}")
            return None
    
    async def _try_pollinations_ai(self, prompt: str, width: int, height: int) -> Optional[bytes]:
        """Try Pollinations.ai - free AI image generation with multiple models"""
        try:
            session = await self._get_session()

            # Try different models available on Pollinations.ai
            models = ["flux", "turbo", "flux-realism", "flux-anime"]

            for model in models:
                try:
                    # Clean and encode prompt
                    clean_prompt = prompt.replace(" ", "%20").replace(",", "%2C")
                    url = f"https://image.pollinations.ai/prompt/{clean_prompt}"

                    # Add parameters
                    params = {
                        "width": min(width, 1024),  # Pollinations limit
                        "height": min(height, 1024),
                        "seed": -1,
                        "model": model,
                        "enhance": "true",
                        "nologo": "true"
                    }

                    async with session.get(url, params=params, timeout=30) as response:
                        if response.status == 200:
                            content_type = response.headers.get('content-type', '')
                            if 'image' in content_type:
                                image_data = await response.read()
                                if len(image_data) > 5000:  # Valid image size
                                    logger.info(f"Generated image using Pollinations.ai model: {model}")
                                    return image_data

                except Exception as e:
                    logger.debug(f"Pollinations.ai model {model} error: {e}")
                    continue

            return None

        except Exception as e:
            logger.debug(f"Pollinations.ai error: {e}")
            return None
    
    async def _try_craiyon(self, prompt: str, width: int, height: int) -> Optional[bytes]:
        """Try Craiyon (formerly DALL-E mini) API"""
        try:
            session = await self._get_session()
            
            # Craiyon API endpoint
            url = "https://api.craiyon.com/v3"
            
            payload = {
                "prompt": prompt,
                "model": "art",
                "negative_prompt": "blurry, low quality, distorted",
                "version": "35s5hfwn9n78gb06"
            }
            
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    if "images" in result and result["images"]:
                        # Get first image (base64 encoded)
                        image_b64 = result["images"][0]
                        image_data = base64.b64decode(image_b64)
                        return image_data
            
            return None
            
        except Exception as e:
            logger.debug(f"Craiyon error: {e}")
            return None
    
    async def _try_huggingface_spaces(self, prompt: str, width: int, height: int) -> Optional[bytes]:
        """Try Hugging Face Inference API with Stable Diffusion models"""
        try:
            session = await self._get_session()

            # Updated model list based on current availability
            models = [
                "stable-diffusion-v1-5/stable-diffusion-v1-5",  # The model you found
                "stabilityai/stable-diffusion-2-1",
                "runwayml/stable-diffusion-v1-5",
                "CompVis/stable-diffusion-v1-4",
                "stabilityai/stable-diffusion-xl-base-1.0"
            ]

            for model in models:
                try:
                    url = f"https://api-inference.huggingface.co/models/{model}"

                    # Enhanced payload with better parameters
                    payload = {
                        "inputs": prompt,
                        "parameters": {
                            "num_inference_steps": 25,
                            "guidance_scale": 7.5,
                            "width": min(width, 512),  # HF API limits
                            "height": min(height, 512),
                            "negative_prompt": "blurry, low quality, distorted, deformed, ugly, bad anatomy"
                        }
                    }

                    headers = {
                        "Content-Type": "application/json",
                        "User-Agent": self.user_agent
                    }

                    async with session.post(url, json=payload, headers=headers) as response:
                        if response.status == 200:
                            content_type = response.headers.get('content-type', '')
                            if 'image' in content_type:
                                image_data = await response.read()
                                if len(image_data) > 5000:  # Valid image size
                                    logger.info(f"Generated image using HuggingFace model: {model}")
                                    return image_data
                        elif response.status == 503:
                            # Model is loading, try next one
                            logger.debug(f"HuggingFace model {model} is loading, trying next...")
                            continue
                        else:
                            logger.debug(f"HuggingFace model {model} returned status: {response.status}")

                except Exception as e:
                    logger.debug(f"HuggingFace model {model} error: {e}")
                    continue

            return None

        except Exception as e:
            logger.debug(f"HuggingFace Inference API error: {e}")
            return None

    async def _try_segmind_api(self, prompt: str, width: int, height: int) -> Optional[bytes]:
        """Try Segmind API - another free AI image generation service"""
        try:
            session = await self._get_session()

            # Segmind offers free tier for Stable Diffusion
            url = "https://api.segmind.com/v1/sd1.5-txt2img"

            payload = {
                "prompt": prompt,
                "negative_prompt": "blurry, low quality, distorted, deformed, ugly",
                "width": min(width, 512),
                "height": min(height, 512),
                "samples": 1,
                "num_inference_steps": 20,
                "guidance_scale": 7.5,
                "seed": -1
            }

            headers = {
                "Content-Type": "application/json",
                "User-Agent": self.user_agent
            }

            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if "image" in result:
                        # Decode base64 image
                        image_b64 = result["image"]
                        if image_b64.startswith("data:image"):
                            image_b64 = image_b64.split(",")[1]
                        image_data = base64.b64decode(image_b64)
                        if len(image_data) > 5000:
                            logger.info("Generated image using Segmind API")
                            return image_data

            return None

        except Exception as e:
            logger.debug(f"Segmind API error: {e}")
            return None

    async def cleanup(self):
        """Clean up resources"""
        if self.session and not self.session.closed:
            await self.session.close()

class HybridAIImageGenerator:
    """Hybrid generator that tries free services first, then fallback to enhanced placeholders"""
    
    def __init__(self):
        self.free_generator = FreeAIImageGenerator()
        
    async def generate_image(self, prompt: str, width: int = 512, height: int = 512) -> Optional[bytes]:
        """Generate image with free AI first, then enhanced placeholder fallback"""
        try:
            # Try free AI services first
            result = await self.free_generator.generate_image(prompt, width, height)
            if result:
                logger.info("Generated real AI image using free services")
                return result
            
            # Fallback to enhanced placeholder
            logger.info("Free AI services unavailable, using enhanced placeholder")
            from app.services.perchance_ai import PerchanceImageGenerator
            
            placeholder_gen = PerchanceImageGenerator()
            placeholder_result = await placeholder_gen._generate_placeholder_image(prompt, width, height)
            await placeholder_gen.cleanup()
            
            return placeholder_result
            
        except Exception as e:
            logger.error(f"Hybrid AI generation error: {e}")
            return None
    
    async def cleanup(self):
        """Clean up resources"""
        await self.free_generator.cleanup()

# Factory function for easy integration
async def create_ai_image_generator(prefer_free: bool = True):
    """Create the best available AI image generator"""
    if prefer_free:
        return HybridAIImageGenerator()
    else:
        from app.services.perchance_ai import PerchanceImageGenerator
        return PerchanceImageGenerator()
