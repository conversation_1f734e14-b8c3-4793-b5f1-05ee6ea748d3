#!/usr/bin/env python3
"""
Test script for Contextual Image Generation System

This script tests the complete contextual image generation pipeline:
1. Script analysis and scene extraction
2. Contextual image generation
3. Timing synchronization
4. Style consistency management
"""

import asyncio
import os
import sys
import tempfile
from pathlib import Path

# Add the project root to the path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

from app.services.script_analyzer import ScriptAnalyzer
from app.services.contextual_image_generator import ContextualImageGenerator, ImageGenerationConfig, ContextualImage
from app.services.timing_synchronizer import TimingSynchronizer
from app.services.style_consistency_manager import StyleConsistencyManager
from app.models.schema import VideoParams, VideoAspect


def create_test_script():
    """Create a test script similar to the Romanian example"""
    return '''
"<PERSON><PERSON><PERSON> cel <PERSON> vs. Skibidi Toileții"

Era anul 1476, iar <PERSON><PERSON>fan cel <PERSON> stătea în cortul său de comand<PERSON>, studiind harta <PERSON>, când deodată un soltăr speriat năvăli înăuntru:

— "Măria Ta! O armată de... toalete cu capete ne invadează țara! Se numesc Skibidi Toileții și transformă oamenii în roboți de wc!"

Ștefan tresări, trăgându-și sabia:
— "Ce dracu' mănâncă ăștia pe la muntele Neamț să facă așa ceva?! Hai, în numele Sfântului Lazăr, să-i trimitem la canalizarea istoriei!"

Bătălia de la Podul Înalt
Pe câmpia de lângă Războieni, oastea moldovenească se întâlni față în față cu armata Skibidi Toileților – sute de veceuri umblătoare cu capete de TikTokeri, care urlau:

— "SKIBIDI DOP DOP YES YES!"

— "Bine-ați venit în Moldova, nemernicilor!" răcni Ștefan. "Aici nu vă cacă pe voi toți!"

Lupta a început:

Arcașii moldoveni trăgeau săgeți în butoanele de flush.

Cavalerii loveau cu topoare în capac, spărgându-le chiștoacele.

Ștefan în persoană sări pe un Skibidi Toilet VIP și-i trânti capacul în cap, zbierând: "ASTA E PENTRU TZARA NOASTRĂ!"

Momentul decisiv
Când totul părea pierdut, Skibidi Toiletul Rege (un wc aurit cu coroana de diamante) începu să cânte:
— "SKIBIDI DAB DAB, I'M THE KING OF THE SEWER!"

Ștefan, neabătut, scoase o oală de borș acru pe care o aruncă în el. Aciditatea borșului făcu toaleta să se topească în spume, iar restul armatei Skibidi fugiră speriați, trăgând apa după ei.

Victoria Moldovei
Ștefan cel Mare stătu pe un morman de wc-uri sparte și rosti:
— "Așa se face istorie, fraților! Nu cu cântece idioate, ci cu sabia și borșul!"

Oastea izbucni în urale, iar din acel moment, niciun Skibidi Toilet nu mai îndrăzni să calce pe pământ moldovenesc.

Morală: Dacă vii cu prostii pe la noi, te trimitem la fundul istoriei!
'''


def create_test_subtitle_file():
    """Create a test subtitle file"""
    subtitle_content = '''1
00:00:00,000 --> 00:00:03,000
Ștefan cel Mare vs. Skibidi Toileții

2
00:00:03,000 --> 00:00:08,000
Era anul 1476, iar Ștefan cel Mare stătea în cortul său de comandă

3
00:00:08,000 --> 00:00:12,000
când deodată un soltăr speriat năvăli înăuntru

4
00:00:12,000 --> 00:00:16,000
O armată de toalete cu capete ne invadează țara!

5
00:00:16,000 --> 00:00:20,000
Ștefan tresări, trăgându-și sabia

6
00:00:20,000 --> 00:00:25,000
Bătălia de la Podul Înalt

7
00:00:25,000 --> 00:00:30,000
Pe câmpia de lângă Războieni, oastea moldovenească se întâlni

8
00:00:30,000 --> 00:00:35,000
cu armata Skibidi Toileților

9
00:00:35,000 --> 00:00:40,000
Lupta a început! Arcașii moldoveni trăgeau săgeți

10
00:00:40,000 --> 00:00:45,000
Ștefan în persoană sări pe un Skibidi Toilet VIP

11
00:00:45,000 --> 00:00:50,000
Momentul decisiv - Skibidi Toiletul Rege

12
00:00:50,000 --> 00:00:55,000
Ștefan scoase o oală de borș acru

13
00:00:55,000 --> 00:01:00,000
Victoria Moldovei! Așa se face istorie!
'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
        f.write(subtitle_content)
        return f.name


async def test_script_analysis():
    """Test the script analysis functionality"""
    print("🔍 Testing Script Analysis...")
    
    script = create_test_script()
    analyzer = ScriptAnalyzer()
    
    # Test script analysis for image generation
    image_data = analyzer.analyze_for_image_generation(script, audio_duration=60.0, style="historical")
    
    print(f"✅ Analyzed script and found {len(image_data)} potential image generation points")
    
    for i, data in enumerate(image_data[:3]):  # Show first 3
        print(f"  {i+1}. {data['start_time']:.1f}s-{data['end_time']:.1f}s: {data['prompt'][:60]}...")
        print(f"     Scene: {data['scene_type']}, Tone: {data['emotional_tone']}")
    
    return image_data


async def test_style_consistency():
    """Test the style consistency manager"""
    print("\n🎨 Testing Style Consistency...")
    
    style_manager = StyleConsistencyManager()
    
    # Set historical style for our test
    style_manager.set_style_profile("historical")
    
    # Generate some test prompts
    test_scenes = [
        ("Ștefan cel Mare în cortul său", "dialogue", "dramatic"),
        ("bătălia de la Podul Înalt", "battle", "epic"),
        ("câmpia de lângă Războieni", "landscape", "peaceful"),
        ("Skibidi Toileții atacă", "action", "comedic")
    ]
    
    generated_prompts = []
    for scene_text, scene_type, tone in test_scenes:
        prompt = style_manager.generate_consistent_prompt(scene_text, scene_type, tone)
        generated_prompts.append(prompt)
        print(f"  ✅ {scene_type}: {prompt[:80]}...")
    
    # Analyze consistency
    consistency_analysis = style_manager.analyze_consistency()
    print(f"✅ Style consistency score: {consistency_analysis.get('consistency_score', 0):.2f}")
    
    return style_manager


async def test_timing_synchronization():
    """Test the timing synchronization system"""
    print("\n⏱️ Testing Timing Synchronization...")
    
    # Create test subtitle file
    subtitle_file = create_test_subtitle_file()
    
    try:
        synchronizer = TimingSynchronizer()
        
        # Load subtitles
        subtitle_segments = synchronizer.load_subtitles(subtitle_file)
        print(f"✅ Loaded {len(subtitle_segments)} subtitle segments")
        
        # Create mock contextual images
        mock_images = [
            ContextualImage(
                image_path="/mock/stefan_command_tent.png",
                start_time=3.0,
                end_time=8.0,
                duration=5.0,
                prompt="medieval Moldovan warrior Ștefan cel Mare in command tent",
                script_text="Ștefan cel Mare stătea în cortul său de comandă",
                scene_type="dialogue",
                emotional_tone="dramatic",
                priority=0.9,
                generation_time=2.5,
                provider_used="perchance",
                file_size=1024000
            ),
            ContextualImage(
                image_path="/mock/battle_scene.png",
                start_time=25.0,
                end_time=35.0,
                duration=10.0,
                prompt="epic medieval battle scene with Moldovan warriors",
                script_text="oastea moldovenească se întâlni cu armata Skibidi Toileților",
                scene_type="battle",
                emotional_tone="epic",
                priority=0.95,
                generation_time=3.2,
                provider_used="perchance",
                file_size=1536000
            )
        ]
        
        # Test synchronization
        synchronized_segments = synchronizer.synchronize_with_images(mock_images)
        print(f"✅ Created {len(synchronized_segments)} synchronized segments")
        
        # Show timeline
        timeline = synchronizer.get_timeline_data()
        print("📅 Timeline preview:")
        for i, segment in enumerate(timeline[:5]):  # Show first 5
            has_image = "🖼️" if segment['image_path'] else "📝"
            print(f"  {has_image} {segment['start_time']:.1f}s-{segment['end_time']:.1f}s")
        
        return synchronized_segments
        
    finally:
        # Clean up
        try:
            os.unlink(subtitle_file)
        except:
            pass


async def test_contextual_image_generation():
    """Test the contextual image generation (without actual AI calls)"""
    print("\n🖼️ Testing Contextual Image Generation...")
    
    # Create test parameters
    params = VideoParams(
        video_subject="Test",
        video_aspect=VideoAspect.portrait,
        ai_style="historical",
        ai_provider="perchance",
        contextual_images_enabled=True,
        contextual_images_max=5,
        contextual_images_duration=3.0,
        contextual_images_opacity=0.7
    )
    
    # Initialize generator
    generator = ContextualImageGenerator()
    
    # Update config from params
    generator._update_config_from_params(params)
    
    print(f"✅ Generator configured:")
    print(f"  - Style: {generator.config.style}")
    print(f"  - Max images: {generator.config.max_images}")
    print(f"  - Dimensions: {generator.config.width}x{generator.config.height}")
    print(f"  - Duration: {generator.config.image_duration}s")
    
    # Test image generation timeline creation
    mock_images = [
        ContextualImage(
            image_path="/mock/image1.png",
            start_time=5.0,
            end_time=10.0,
            duration=5.0,
            prompt="test prompt 1",
            script_text="test script 1",
            scene_type="dialogue",
            emotional_tone="dramatic",
            priority=0.8,
            generation_time=2.0,
            provider_used="perchance",
            file_size=1024000
        )
    ]
    
    timeline = generator.create_image_timeline(mock_images, total_duration=60.0)
    print(f"✅ Created timeline with {len(timeline)} entries")
    
    # Test statistics
    stats = generator.get_generation_stats(mock_images)
    print(f"✅ Generation stats: {stats}")
    
    return generator


async def main():
    """Run all tests"""
    print("🚀 Starting Contextual Image Generation System Tests")
    print("=" * 60)
    
    try:
        # Test 1: Script Analysis
        image_data = await test_script_analysis()
        
        # Test 2: Style Consistency
        style_manager = await test_style_consistency()
        
        # Test 3: Timing Synchronization
        synchronized_segments = await test_timing_synchronization()
        
        # Test 4: Contextual Image Generation
        generator = await test_contextual_image_generation()
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        print("\n📊 Summary:")
        print(f"  ✅ Script analysis: {len(image_data)} scenes identified")
        print(f"  ✅ Style consistency: Historical style configured")
        print(f"  ✅ Timing synchronization: {len(synchronized_segments)} segments")
        print(f"  ✅ Image generation: System configured and ready")
        
        print("\n🎯 The Contextual Image Generation System is ready for use!")
        print("   Enable it in the web interface under AI Image Generation settings.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
