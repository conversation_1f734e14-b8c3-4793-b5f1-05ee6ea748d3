#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Romanian language support in MoneyPrinterTurbo
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the root directory to the path
root_dir = Path(__file__).parent
sys.path.append(str(root_dir))

from app.services import llm, voice
from app.utils import utils

def test_romanian_translation():
    """Test if Romanian translation file loads correctly"""
    print("🧪 Testing Romanian translation file...")
    
    i18n_dir = root_dir / "webui" / "i18n"
    locales = utils.load_locales(str(i18n_dir))
    
    if "ro" in locales:
        print("✅ Romanian translation file loaded successfully")
        print(f"   Language name: {locales['ro'].get('Language')}")
        
        # Test a few key translations
        translations = locales['ro'].get('Translation', {})
        test_keys = [
            "Video Script Settings",
            "Generate Video",
            "Speech Synthesis",
            "Background Music"
        ]
        
        for key in test_keys:
            if key in translations:
                print(f"   ✅ '{key}' -> '{translations[key]}'")
            else:
                print(f"   ❌ Missing translation for '{key}'")
    else:
        print("❌ Romanian translation file not found")

def test_romanian_voices():
    """Test Romanian voice availability"""
    print("\n🎤 Testing Romanian voice availability...")
    
    all_voices = voice.get_all_azure_voices()
    romanian_voices = [v for v in all_voices if v.startswith("ro-RO")]
    
    if romanian_voices:
        print(f"✅ Found {len(romanian_voices)} Romanian voices:")
        for voice_name in romanian_voices:
            print(f"   - {voice_name}")
    else:
        print("❌ No Romanian voices found")

async def test_romanian_tts():
    """Test Romanian text-to-speech synthesis"""
    print("\n🗣️ Testing Romanian TTS synthesis...")
    
    test_text = "Acesta este un test pentru sinteza vocală în limba română. Folosim diacritice precum ă, â, î, ș și ț."
    voice_name = "ro-RO-AlinaNeural"
    
    # Create temp directory
    temp_dir = root_dir / "storage" / "temp"
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    voice_file = temp_dir / "test_romanian_voice.mp3"
    
    try:
        print(f"   Synthesizing: '{test_text[:50]}...'")
        print(f"   Voice: {voice_name}")
        
        sub_maker = voice.azure_tts_v1(
            text=test_text,
            voice_name=voice_name,
            voice_rate=1.0,
            voice_file=str(voice_file)
        )
        
        if sub_maker and voice_file.exists():
            print(f"✅ Romanian TTS synthesis successful")
            print(f"   Output file: {voice_file}")
            print(f"   File size: {voice_file.stat().st_size} bytes")
        else:
            print("❌ Romanian TTS synthesis failed")
            
    except Exception as e:
        print(f"❌ Romanian TTS synthesis error: {e}")

def test_romanian_script_generation():
    """Test Romanian script generation"""
    print("\n📝 Testing Romanian script generation...")
    
    test_subject = "Frumusețile României"
    
    try:
        print(f"   Subject: '{test_subject}'")
        print("   Generating script...")
        
        script = llm.generate_script(
            video_subject=test_subject,
            language="ro-RO",
            paragraph_number=1
        )
        
        if script and len(script.strip()) > 0:
            print("✅ Romanian script generation successful")
            print(f"   Generated script: '{script[:100]}...'")
            
            # Check for Romanian diacritics
            romanian_chars = ['ă', 'â', 'î', 'ș', 'ț']
            found_diacritics = [char for char in romanian_chars if char in script.lower()]
            
            if found_diacritics:
                print(f"   ✅ Romanian diacritics found: {found_diacritics}")
            else:
                print("   ⚠️ No Romanian diacritics detected (may be normal)")
                
        else:
            print("❌ Romanian script generation failed - empty result")
            
    except Exception as e:
        print(f"❌ Romanian script generation error: {e}")

def main():
    """Run all Romanian language tests"""
    print("🇷🇴 MoneyPrinterTurbo Romanian Language Support Test")
    print("=" * 60)
    
    # Test translation file
    test_romanian_translation()
    
    # Test voice availability
    test_romanian_voices()
    
    # Test TTS (async)
    try:
        asyncio.run(test_romanian_tts())
    except Exception as e:
        print(f"❌ TTS test failed: {e}")
    
    # Test script generation
    test_romanian_script_generation()
    
    print("\n" + "=" * 60)
    print("🏁 Romanian language support test completed!")
    print("\n💡 Tips for using Romanian language:")
    print("   1. Set UI language to 'ro - Română' in the interface")
    print("   2. Use Romanian voices: ro-RO-AlinaNeural or ro-RO-EmilNeural")
    print("   3. Set script language to 'ro-RO' for Romanian content")
    print("   4. Configure your LLM API key for script generation")

if __name__ == "__main__":
    main()
