"""
Modul pentru detectarea și configurarea hardware-ului disponibil pentru procesarea video.
Suportă detectarea plăcilor grafice NVIDIA CUDA, AMD și Intel.
"""

import os
import platform
import subprocess
import sys
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from loguru import logger


@dataclass
class HardwareInfo:
    """Informații despre hardware-ul detectat."""
    name: str
    type: str  # 'cpu', 'nvidia', 'amd', 'intel'
    available: bool
    memory_gb: Optional[float] = None
    compute_capability: Optional[str] = None
    performance_score: int = 0  # 1-10, unde 10 = cel mai performant
    estimated_speed: str = "Unknown"  # "Foarte rapid", "Rapid", "Mediu", "Lent"


class HardwareDetector:
    """Detectează și configurează hardware-ul disponibil pentru procesarea video."""
    
    def __init__(self):
        self.detected_hardware: List[HardwareInfo] = []
        self._detect_all_hardware()
    
    def _detect_all_hardware(self) -> None:
        """Detectează tot hardware-ul disponibil."""
        logger.info("🔍 Detectare hardware disponibil...")
        
        # Detectează CPU
        self._detect_cpu()
        
        # Detectează GPU-uri
        self._detect_nvidia_gpu()
        self._detect_amd_gpu()
        self._detect_intel_gpu()
        
        # Sortează după scorul de performanță
        self.detected_hardware.sort(key=lambda x: x.performance_score, reverse=True)
        
        logger.info(f"✅ Detectat {len(self.detected_hardware)} dispozitive hardware")
    
    def _detect_cpu(self) -> None:
        """Detectează informații despre CPU."""
        try:
            import psutil
            cpu_count = psutil.cpu_count(logical=True)
            cpu_freq = psutil.cpu_freq()
            
            # Estimează performanța CPU-ului
            performance_score = min(10, max(1, cpu_count // 2))
            if cpu_freq and cpu_freq.max > 3000:  # > 3GHz
                performance_score += 1
            
            speed_map = {
                1: "Foarte lent",
                2: "Lent", 
                3: "Lent",
                4: "Mediu",
                5: "Mediu",
                6: "Rapid",
                7: "Rapid",
                8: "Foarte rapid",
                9: "Foarte rapid",
                10: "Extrem de rapid"
            }
            
            cpu_info = HardwareInfo(
                name=f"CPU ({cpu_count} nuclee)",
                type="cpu",
                available=True,
                performance_score=performance_score,
                estimated_speed=speed_map.get(performance_score, "Unknown")
            )
            
            self.detected_hardware.append(cpu_info)
            logger.info(f"✅ CPU detectat: {cpu_info.name} - {cpu_info.estimated_speed}")
            
        except Exception as e:
            logger.warning(f"⚠️ Nu s-a putut detecta CPU-ul: {e}")
    
    def _detect_nvidia_gpu(self) -> None:
        """Detectează plăci grafice NVIDIA cu suport CUDA."""
        try:
            # Verifică dacă CUDA este disponibil
            result = subprocess.run(
                ["nvidia-smi", "--query-gpu=name,memory.total,compute_cap", "--format=csv,noheader,nounits"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for i, line in enumerate(lines):
                    if line.strip():
                        parts = [p.strip() for p in line.split(',')]
                        if len(parts) >= 3:
                            name = parts[0]
                            memory_gb = float(parts[1]) / 1024  # MB to GB
                            compute_cap = parts[2]
                            
                            # Calculează scorul de performanță bazat pe memoria și compute capability
                            performance_score = 7  # Baza pentru GPU NVIDIA
                            if memory_gb >= 8:
                                performance_score += 2
                            elif memory_gb >= 4:
                                performance_score += 1
                            
                            if compute_cap and float(compute_cap) >= 7.0:
                                performance_score += 1
                            
                            gpu_info = HardwareInfo(
                                name=f"NVIDIA {name}",
                                type="nvidia",
                                available=True,
                                memory_gb=memory_gb,
                                compute_capability=compute_cap,
                                performance_score=min(10, performance_score),
                                estimated_speed="Foarte rapid" if performance_score >= 9 else "Rapid"
                            )
                            
                            self.detected_hardware.append(gpu_info)
                            logger.info(f"✅ GPU NVIDIA detectat: {gpu_info.name} ({memory_gb:.1f}GB)")
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            logger.info("ℹ️ NVIDIA GPU nu este disponibil sau nvidia-smi nu este instalat")
        except Exception as e:
            logger.warning(f"⚠️ Eroare la detectarea GPU NVIDIA: {e}")
    
    def _detect_amd_gpu(self) -> None:
        """Detectează plăci grafice AMD."""
        try:
            # Pe Windows, încearcă să detecteze prin WMI
            if platform.system() == "Windows":
                result = subprocess.run(
                    ["wmic", "path", "win32_VideoController", "get", "name,AdapterRAM", "/format:csv"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')[1:]  # Skip header
                    for line in lines:
                        if line.strip() and "AMD" in line.upper() or "RADEON" in line.upper():
                            parts = [p.strip() for p in line.split(',')]
                            if len(parts) >= 3:
                                name = parts[2] if parts[2] else "AMD GPU"
                                memory_bytes = parts[1] if parts[1] and parts[1].isdigit() else "0"
                                memory_gb = int(memory_bytes) / (1024**3) if memory_bytes.isdigit() else 0
                                
                                performance_score = 6  # Baza pentru GPU AMD
                                if memory_gb >= 8:
                                    performance_score += 1
                                
                                gpu_info = HardwareInfo(
                                    name=f"AMD {name}",
                                    type="amd",
                                    available=True,
                                    memory_gb=memory_gb if memory_gb > 0 else None,
                                    performance_score=performance_score,
                                    estimated_speed="Rapid" if performance_score >= 7 else "Mediu"
                                )
                                
                                self.detected_hardware.append(gpu_info)
                                logger.info(f"✅ GPU AMD detectat: {gpu_info.name}")
            
        except Exception as e:
            logger.warning(f"⚠️ Eroare la detectarea GPU AMD: {e}")
    
    def _detect_intel_gpu(self) -> None:
        """Detectează plăci grafice Intel."""
        try:
            # Pe Windows, încearcă să detecteze prin WMI
            if platform.system() == "Windows":
                result = subprocess.run(
                    ["wmic", "path", "win32_VideoController", "get", "name", "/format:csv"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')[1:]  # Skip header
                    for line in lines:
                        if line.strip() and "INTEL" in line.upper():
                            parts = [p.strip() for p in line.split(',')]
                            if len(parts) >= 2:
                                name = parts[1] if parts[1] else "Intel GPU"
                                
                                performance_score = 4  # Baza pentru GPU Intel (mai puțin performant)
                                if "ARC" in name.upper() or "XE" in name.upper():
                                    performance_score = 6  # GPU-uri Intel mai noi
                                
                                gpu_info = HardwareInfo(
                                    name=f"Intel {name}",
                                    type="intel",
                                    available=True,
                                    performance_score=performance_score,
                                    estimated_speed="Mediu" if performance_score >= 5 else "Lent"
                                )
                                
                                self.detected_hardware.append(gpu_info)
                                logger.info(f"✅ GPU Intel detectat: {gpu_info.name}")
            
        except Exception as e:
            logger.warning(f"⚠️ Eroare la detectarea GPU Intel: {e}")
    
    def get_available_hardware(self) -> List[HardwareInfo]:
        """Returnează lista hardware-ului disponibil."""
        return [hw for hw in self.detected_hardware if hw.available]
    
    def get_recommended_hardware(self) -> Optional[HardwareInfo]:
        """Returnează hardware-ul recomandat (cel cu cel mai mare scor de performanță)."""
        available = self.get_available_hardware()
        return available[0] if available else None
    
    def get_hardware_by_type(self, hardware_type: str) -> List[HardwareInfo]:
        """Returnează hardware-ul de un anumit tip."""
        return [hw for hw in self.detected_hardware if hw.type == hardware_type and hw.available]
    
    def format_hardware_info(self, hardware: HardwareInfo) -> str:
        """Formatează informațiile hardware pentru afișare."""
        info_parts = [hardware.name, f"Viteză: {hardware.estimated_speed}"]
        
        if hardware.memory_gb:
            info_parts.append(f"Memorie: {hardware.memory_gb:.1f}GB")
        
        if hardware.compute_capability:
            info_parts.append(f"Compute: {hardware.compute_capability}")
        
        return " | ".join(info_parts)


# Instanță globală pentru detectarea hardware-ului
_hardware_detector = None

def get_hardware_detector() -> HardwareDetector:
    """Returnează instanța globală a detectorului de hardware."""
    global _hardware_detector
    if _hardware_detector is None:
        _hardware_detector = HardwareDetector()
    return _hardware_detector
