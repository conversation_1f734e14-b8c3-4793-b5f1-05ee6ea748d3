# Podcast Clipper Dependencies
# Dependințe pentru funcționalitatea de extragere clipuri din podcast-uri

# Computer Vision pentru detectarea persoanelor
opencv-python>=4.8.0
opencv-contrib-python>=4.8.0

# Audio processing și transcripție
openai-whisper>=20231117
whisperx>=3.1.0
pyannote.audio>=3.1.0
librosa>=0.10.0
soundfile>=0.12.0

# Video processing
moviepy>=1.0.3
imageio>=2.31.0
imageio-ffmpeg>=0.4.8

# Machine Learning și AI
torch>=2.0.0
torchaudio>=2.0.0
transformers>=4.30.0
numpy>=1.24.0
scipy>=1.10.0

# YOLO pentru detectarea persoanelor
ultralytics>=8.0.0

# Audio analysis și speaker diarization
speechbrain>=0.5.0
pyannote.core>=5.0.0
pyannote.database>=5.0.0
pyannote.metrics>=3.2.0
pyannote.pipeline>=3.0.0

# Utilities
tqdm>=4.65.0
matplotlib>=3.7.0
seaborn>=0.12.0
pandas>=2.0.0
scikit-learn>=1.3.0

# GUI și interfață (opțional pentru versiunea standalone)
tkinter-tooltip>=2.0.0

# Audio enhancement
noisereduce>=3.0.0
pydub>=0.25.0

# Face detection (alternativă la YOLO)
mediapipe>=0.10.0
dlib>=19.24.0

# Optimizare performanță
numba>=0.57.0
joblib>=1.3.0

# Logging și debugging
loguru>=0.7.0
rich>=13.0.0

# File handling
pathlib2>=2.3.0
