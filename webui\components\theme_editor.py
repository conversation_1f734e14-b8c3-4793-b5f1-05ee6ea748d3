"""
Editor de teme personalizabile pentru interfața MoneyPrinterTurbo.
Inspirat din designul Bandcamp.
"""

import streamlit as st
import base64
from typing import Optional, Dict, Any
from loguru import logger
import io
from PIL import Image

import sys
import os

# Adaugă calea către modulele aplicației
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from app.utils.theme_manager import (
    Theme, ThemeColors, BackgroundSettings, get_theme_manager
)


def render_theme_editor() -> None:
    """Renderează editorul de teme."""
    st.subheader("🎨 Editor Teme Personalizabile")
    
    theme_manager = get_theme_manager()
    
    # Tabs pentru diferite secțiuni
    tab1, tab2, tab3, tab4 = st.tabs([
        "🎨 Selectare Temă", 
        "✏️ Editor Culori", 
        "🖼️ Fundal", 
        "💾 Salvare/Gestionare"
    ])
    
    with tab1:
        _render_theme_selector(theme_manager)
    
    with tab2:
        _render_color_editor(theme_manager)
    
    with tab3:
        _render_background_editor(theme_manager)
    
    with tab4:
        _render_theme_management(theme_manager)


def _render_theme_selector(theme_manager) -> None:
    """Renderează selectorul de teme."""
    st.markdown("### 📚 Selectare Temă")
    
    available_themes = theme_manager.get_available_themes()
    current_theme = theme_manager.get_current_theme()
    
    if not available_themes:
        st.error("❌ Nu sunt disponibile teme!")
        return
    
    # Determină indexul temei curente
    current_index = 0
    if current_theme:
        try:
            current_index = available_themes.index(current_theme.name)
        except ValueError:
            pass
    
    # Selector de temă
    selected_theme_name = st.selectbox(
        "Alege o temă:",
        options=available_themes,
        index=current_index,
        help="Selectează o temă pentru a o aplica sau edita"
    )
    
    if selected_theme_name:
        selected_theme = theme_manager.get_theme(selected_theme_name)
        if selected_theme:
            # Afișează informații despre temă
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**Descriere:** {selected_theme.description}")
                st.write(f"**Creat de:** {selected_theme.created_by}")
                st.write(f"**Versiune:** {selected_theme.version}")
            
            with col2:
                # Preview culori
                _render_color_preview(selected_theme.colors)
            
            # Butoane de acțiune
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("🚀 Aplică Tema", type="primary"):
                    if theme_manager.set_current_theme(selected_theme_name):
                        st.success(f"✅ Tema '{selected_theme_name}' a fost aplicată!")
                        st.rerun()
                    else:
                        st.error("❌ Eroare la aplicarea temei!")
            
            with col2:
                if st.button("✏️ Editează Tema"):
                    st.session_state["editing_theme"] = selected_theme
                    st.success(f"📝 Editezi tema: {selected_theme_name}")
            
            with col3:
                if selected_theme.created_by != "MoneyPrinterTurbo":
                    if st.button("🗑️ Șterge Tema", type="secondary"):
                        if theme_manager.delete_theme(selected_theme_name):
                            st.success(f"✅ Tema '{selected_theme_name}' a fost ștearsă!")
                            st.rerun()
                        else:
                            st.error("❌ Eroare la ștergerea temei!")


def _render_color_preview(colors: ThemeColors) -> None:
    """Renderează un preview al culorilor."""
    st.markdown("**Preview Culori:**")
    
    color_items = [
        ("Principală", colors.primary),
        ("Secundară", colors.secondary),
        ("Fundal", colors.background),
        ("Suprafață", colors.surface),
        ("Text Principal", colors.text_primary),
        ("Accent", colors.accent)
    ]
    
    for name, color in color_items:
        st.markdown(
            f"""
            <div style="
                background-color: {color};
                color: {'white' if color in [colors.text_primary, colors.primary] else 'black'};
                padding: 5px 10px;
                margin: 2px 0;
                border-radius: 4px;
                font-size: 12px;
                text-align: center;
            ">
                {name}<br><small>{color}</small>
            </div>
            """,
            unsafe_allow_html=True
        )


def _render_color_editor(theme_manager) -> None:
    """Renderează editorul de culori."""
    st.markdown("### 🎨 Editor Culori")
    
    # Verifică dacă există o temă în editare
    editing_theme = st.session_state.get("editing_theme")
    if not editing_theme:
        st.info("💡 Selectează o temă din tab-ul 'Selectare Temă' și apasă 'Editează Tema' pentru a începe editarea.")
        return
    
    st.write(f"**Editezi:** {editing_theme.name}")
    
    # Editoare de culori
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### Culori Principale")
        
        primary = st.color_picker(
            "Culoare Principală",
            value=editing_theme.colors.primary,
            help="Culoarea principală a aplicației"
        )
        
        secondary = st.color_picker(
            "Culoare Secundară", 
            value=editing_theme.colors.secondary,
            help="Culoarea secundară pentru accente"
        )
        
        accent = st.color_picker(
            "Culoare Accent",
            value=editing_theme.colors.accent,
            help="Culoarea pentru elemente de accent"
        )
    
    with col2:
        st.markdown("#### Culori Fundal și Text")
        
        background = st.color_picker(
            "Culoare Fundal",
            value=editing_theme.colors.background,
            help="Culoarea de fundal principală"
        )
        
        surface = st.color_picker(
            "Culoare Suprafață",
            value=editing_theme.colors.surface,
            help="Culoarea pentru carduri și panouri"
        )
        
        text_primary = st.color_picker(
            "Text Principal",
            value=editing_theme.colors.text_primary,
            help="Culoarea textului principal"
        )
        
        text_secondary = st.color_picker(
            "Text Secundar",
            value=editing_theme.colors.text_secondary,
            help="Culoarea textului secundar"
        )
    
    # Culori pentru stări
    st.markdown("#### Culori Stări")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        success = st.color_picker(
            "Succes",
            value=editing_theme.colors.success,
            help="Culoarea pentru mesaje de succes"
        )
    
    with col2:
        warning = st.color_picker(
            "Avertisment",
            value=editing_theme.colors.warning,
            help="Culoarea pentru avertismente"
        )
    
    with col3:
        error = st.color_picker(
            "Eroare",
            value=editing_theme.colors.error,
            help="Culoarea pentru erori"
        )
    
    # Actualizează culorile în tema editată
    editing_theme.colors = ThemeColors(
        primary=primary,
        secondary=secondary,
        background=background,
        surface=surface,
        text_primary=text_primary,
        text_secondary=text_secondary,
        accent=accent,
        success=success,
        warning=warning,
        error=error
    )
    
    # Salvează în session state
    st.session_state["editing_theme"] = editing_theme
    
    # Preview în timp real
    st.markdown("#### 🔍 Preview Culori")
    _render_color_preview(editing_theme.colors)
    
    # Buton pentru aplicare temporară
    if st.button("👁️ Preview Temă", help="Aplică temporar tema pentru preview"):
        # Generează CSS-ul și îl aplică
        css = theme_manager.generate_css(editing_theme)
        st.markdown(css, unsafe_allow_html=True)
        st.success("✅ Preview aplicat! Reîmprospătează pagina pentru a reveni la tema originală.")


def _render_background_editor(theme_manager) -> None:
    """Renderează editorul de fundal."""
    st.markdown("### 🖼️ Editor Fundal")
    
    editing_theme = st.session_state.get("editing_theme")
    if not editing_theme:
        st.info("💡 Selectează o temă pentru editare din tab-ul 'Selectare Temă'.")
        return
    
    st.write(f"**Editezi:** {editing_theme.name}")
    
    # Upload imagine
    uploaded_file = st.file_uploader(
        "Încarcă Imagine de Fundal",
        type=['png', 'jpg', 'jpeg', 'gif'],
        help="Încarcă o imagine pentru fundalul aplicației"
    )
    
    if uploaded_file is not None:
        try:
            # Procesează imaginea
            image = Image.open(uploaded_file)
            
            # Redimensionează dacă este prea mare
            max_size = (1920, 1080)
            if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
                st.info(f"📏 Imaginea a fost redimensionată la {image.size}")
            
            # Convertește în base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            img_data = base64.b64encode(buffer.getvalue()).decode()
            
            # Salvează în tema editată
            editing_theme.background.image_data = img_data
            editing_theme.background.image_path = None
            
            st.success("✅ Imaginea a fost încărcată cu succes!")
            
            # Afișează preview
            st.image(image, caption="Preview Imagine", use_column_width=True)
            
        except Exception as e:
            st.error(f"❌ Eroare la procesarea imaginii: {e}")
    
    # Setări pentru imagine
    if editing_theme.background.image_data or editing_theme.background.image_path:
        st.markdown("#### ⚙️ Setări Imagine")
        
        col1, col2 = st.columns(2)
        
        with col1:
            position = st.selectbox(
                "Poziționare Imagine",
                options=["cover", "contain", "repeat", "center"],
                index=["cover", "contain", "repeat", "center"].index(editing_theme.background.position),
                help="Cum să fie poziționată imaginea"
            )
            
            opacity = st.slider(
                "Opacitate",
                min_value=0.0,
                max_value=1.0,
                value=editing_theme.background.opacity,
                step=0.1,
                help="Opacitatea imaginii de fundal"
            )
        
        with col2:
            blur = st.slider(
                "Blur (px)",
                min_value=0,
                max_value=20,
                value=editing_theme.background.blur,
                help="Efectul de blur aplicat imaginii"
            )
        
        # Actualizează setările
        editing_theme.background.position = position
        editing_theme.background.opacity = opacity
        editing_theme.background.blur = blur
        
        # Buton pentru ștergerea imaginii
        if st.button("🗑️ Șterge Imaginea de Fundal"):
            editing_theme.background.image_data = None
            editing_theme.background.image_path = None
            st.success("✅ Imaginea de fundal a fost ștearsă!")
            st.rerun()
    
    # Salvează în session state
    st.session_state["editing_theme"] = editing_theme


def _render_theme_management(theme_manager) -> None:
    """Renderează secțiunea de gestionare a temelor."""
    st.markdown("### 💾 Salvare și Gestionare Teme")
    
    editing_theme = st.session_state.get("editing_theme")
    
    if editing_theme:
        st.write(f"**Tema în editare:** {editing_theme.name}")
        
        # Formular pentru salvarea temei
        with st.form("save_theme_form"):
            st.markdown("#### 💾 Salvare Temă")
            
            theme_name = st.text_input(
                "Nume Temă",
                value=editing_theme.name,
                help="Numele temei (va fi folosit pentru salvare)"
            )
            
            theme_description = st.text_area(
                "Descriere",
                value=editing_theme.description,
                help="Descrierea temei"
            )
            
            save_as_new = st.checkbox(
                "Salvează ca temă nouă",
                help="Bifează pentru a crea o temă nouă în loc să suprascrii cea existentă"
            )
            
            submitted = st.form_submit_button("💾 Salvează Tema", type="primary")
            
            if submitted:
                if not theme_name.strip():
                    st.error("❌ Numele temei nu poate fi gol!")
                else:
                    # Creează tema pentru salvare
                    save_theme = Theme(
                        name=theme_name.strip(),
                        description=theme_description.strip(),
                        colors=editing_theme.colors,
                        background=editing_theme.background,
                        created_by="User",
                        version="1.0"
                    )
                    
                    if theme_manager.save_theme(save_theme):
                        st.success(f"✅ Tema '{theme_name}' a fost salvată cu succes!")
                        
                        # Actualizează tema în editare
                        st.session_state["editing_theme"] = save_theme
                        
                        # Aplică tema dacă este dorită
                        if st.button("🚀 Aplică Tema Salvată"):
                            theme_manager.set_current_theme(theme_name)
                            st.success("✅ Tema a fost aplicată!")
                            st.rerun()
                    else:
                        st.error("❌ Eroare la salvarea temei!")
    
    # Export/Import teme
    st.markdown("#### 📤 Export/Import Teme")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Export Temă**")
        available_themes = theme_manager.get_available_themes()
        
        if available_themes:
            export_theme_name = st.selectbox(
                "Selectează tema pentru export:",
                options=available_themes
            )
            
            if st.button("📤 Export Temă"):
                theme_to_export = theme_manager.get_theme(export_theme_name)
                if theme_to_export:
                    import json
                    theme_json = json.dumps(theme_to_export.to_dict(), indent=2, ensure_ascii=False)
                    
                    st.download_button(
                        label="⬇️ Descarcă Tema",
                        data=theme_json,
                        file_name=f"{export_theme_name.replace(' ', '_').lower()}.json",
                        mime="application/json"
                    )
    
    with col2:
        st.markdown("**Import Temă**")
        
        uploaded_theme = st.file_uploader(
            "Încarcă fișier temă (.json)",
            type=['json'],
            help="Încarcă un fișier JSON cu o temă exportată"
        )
        
        if uploaded_theme is not None:
            try:
                import json
                theme_data = json.load(uploaded_theme)
                imported_theme = Theme.from_dict(theme_data)
                
                if st.button("📥 Importă Tema"):
                    if theme_manager.save_theme(imported_theme):
                        st.success(f"✅ Tema '{imported_theme.name}' a fost importată cu succes!")
                        st.rerun()
                    else:
                        st.error("❌ Eroare la importarea temei!")
                        
            except Exception as e:
                st.error(f"❌ Eroare la citirea fișierului: {e}")


def apply_current_theme() -> None:
    """Aplică tema curentă la interfața Streamlit."""
    theme_manager = get_theme_manager()
    current_theme = theme_manager.get_current_theme()
    
    if current_theme:
        css = theme_manager.generate_css(current_theme)
        st.markdown(css, unsafe_allow_html=True)
