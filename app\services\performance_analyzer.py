#!/usr/bin/env python3
"""
Performance Analyzer & Continuous Improvement System

Acest serviciu analizează performanța videoclipurilor generate și oferă
recomandări pentru îmbunătățirea conținutului viitor folosind GPT4Free.
"""

import asyncio
import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from loguru import logger

try:
    from app.services.gpt4free_service import gpt4free_service
    GPT4FREE_AVAILABLE = True
except ImportError:
    GPT4FREE_AVAILABLE = False
    logger.warning("GPT4Free service not available for performance analysis")


@dataclass
class VideoPerformanceData:
    """Date de performanță pentru un videoclip"""
    video_id: str
    title: str
    category: str
    platform: str
    views: int
    likes: int
    comments: int
    shares: int
    watch_time_avg: float  # în secunde
    engagement_rate: float  # procent
    click_through_rate: float  # procent
    retention_rate: float  # procent
    upload_date: datetime
    hashtags: List[str]
    script_length: int
    thumbnail_style: str


@dataclass
class PerformanceAnalysis:
    """Analiza performanței unui videoclip"""
    video_data: VideoPerformanceData
    performance_score: float  # 0-10
    success_factors: List[str]
    improvement_areas: List[str]
    audience_insights: Dict[str, Any]
    content_recommendations: List[str]
    optimization_suggestions: List[str]


@dataclass
class TrendAnalysis:
    """Analiza trend-urilor de performanță"""
    time_period: str
    top_performing_categories: List[str]
    best_hashtags: List[str]
    optimal_posting_times: List[str]
    audience_preferences: Dict[str, Any]
    content_patterns: List[str]
    improvement_recommendations: List[str]


class PerformanceAnalyzer:
    """Analizator de performanță și sistem de îmbunătățire continuă"""
    
    def __init__(self):
        self.gpt4free_available = GPT4FREE_AVAILABLE and gpt4free_service.is_available()
        
        # Metrici de performanță și praguri
        self.performance_thresholds = {
            "excellent": {"views": 100000, "engagement_rate": 8.0, "retention_rate": 70.0},
            "good": {"views": 50000, "engagement_rate": 5.0, "retention_rate": 50.0},
            "average": {"views": 10000, "engagement_rate": 3.0, "retention_rate": 30.0},
            "poor": {"views": 1000, "engagement_rate": 1.0, "retention_rate": 15.0}
        }
        
        # Factori de succes cunoscuți
        self.known_success_factors = {
            "hook_strength": "Hook puternic în primele 3 secunde",
            "cultural_relevance": "Relevanță culturală românească",
            "trending_hashtags": "Folosirea hashtag-urilor trending",
            "optimal_timing": "Postare la ora optimă",
            "visual_appeal": "Apel vizual puternic",
            "call_to_action": "Call-to-action clar și engaging",
            "story_structure": "Structură narativă captivantă",
            "emotional_connection": "Conexiune emotională cu audiența"
        }
    
    def is_available(self) -> bool:
        """Verifică dacă analizatorul este disponibil"""
        return self.gpt4free_available
    
    def calculate_performance_score(self, video_data: VideoPerformanceData) -> float:
        """Calculează scorul de performanță al unui videoclip"""
        
        score = 0.0
        max_score = 10.0
        
        # Scor bazat pe views (30% din total)
        if video_data.views >= self.performance_thresholds["excellent"]["views"]:
            score += 3.0
        elif video_data.views >= self.performance_thresholds["good"]["views"]:
            score += 2.5
        elif video_data.views >= self.performance_thresholds["average"]["views"]:
            score += 2.0
        elif video_data.views >= self.performance_thresholds["poor"]["views"]:
            score += 1.0
        
        # Scor bazat pe engagement rate (40% din total)
        if video_data.engagement_rate >= self.performance_thresholds["excellent"]["engagement_rate"]:
            score += 4.0
        elif video_data.engagement_rate >= self.performance_thresholds["good"]["engagement_rate"]:
            score += 3.0
        elif video_data.engagement_rate >= self.performance_thresholds["average"]["engagement_rate"]:
            score += 2.0
        elif video_data.engagement_rate >= self.performance_thresholds["poor"]["engagement_rate"]:
            score += 1.0
        
        # Scor bazat pe retention rate (30% din total)
        if video_data.retention_rate >= self.performance_thresholds["excellent"]["retention_rate"]:
            score += 3.0
        elif video_data.retention_rate >= self.performance_thresholds["good"]["retention_rate"]:
            score += 2.5
        elif video_data.retention_rate >= self.performance_thresholds["average"]["retention_rate"]:
            score += 2.0
        elif video_data.retention_rate >= self.performance_thresholds["poor"]["retention_rate"]:
            score += 1.0
        
        return min(score, max_score)
    
    async def analyze_video_performance(self, video_data: VideoPerformanceData) -> PerformanceAnalysis:
        """Analizează performanța unui videoclip și oferă insights"""
        
        if not self.is_available():
            return PerformanceAnalysis(
                video_data=video_data,
                performance_score=5.0,
                success_factors=[],
                improvement_areas=[],
                audience_insights={},
                content_recommendations=[],
                optimization_suggestions=[]
            )
        
        performance_score = self.calculate_performance_score(video_data)
        
        prompt = f"""
        Analizează performanța acestui videoclip și oferă insights detaliate:
        
        DATE VIDEOCLIP:
        - Titlu: {video_data.title}
        - Categorie: {video_data.category}
        - Platformă: {video_data.platform}
        - Views: {video_data.views:,}
        - Likes: {video_data.likes:,}
        - Comments: {video_data.comments:,}
        - Shares: {video_data.shares:,}
        - Engagement Rate: {video_data.engagement_rate:.2f}%
        - Retention Rate: {video_data.retention_rate:.2f}%
        - Hashtags: {', '.join(video_data.hashtags)}
        - Scor performanță calculat: {performance_score:.1f}/10
        
        Analizează și returnează în format JSON:
        1. Factorii de succes identificați
        2. Zonele de îmbunătățire
        3. Insights despre audiență
        4. Recomandări pentru conținut viitor
        5. Sugestii de optimizare
        
        Format JSON:
        {{
            "success_factors": ["factor1", "factor2"],
            "improvement_areas": ["zona1", "zona2"],
            "audience_insights": {{
                "preferred_content_type": "tip conținut",
                "engagement_patterns": "pattern-uri",
                "demographic_preferences": "preferințe demografice"
            }},
            "content_recommendations": ["recomandare1", "recomandare2"],
            "optimization_suggestions": ["sugestie1", "sugestie2"]
        }}
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o",
                max_tokens=1500,
                temperature=0.7
            )
            
            if response:
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group())
                    
                    analysis = PerformanceAnalysis(
                        video_data=video_data,
                        performance_score=performance_score,
                        success_factors=data.get('success_factors', []),
                        improvement_areas=data.get('improvement_areas', []),
                        audience_insights=data.get('audience_insights', {}),
                        content_recommendations=data.get('content_recommendations', []),
                        optimization_suggestions=data.get('optimization_suggestions', [])
                    )
                    
                    logger.info(f"✅ Analiză completă pentru videoclip: {video_data.title}")
                    return analysis
            
            logger.warning("Nu s-a putut analiza performanța videoclipului")
            return PerformanceAnalysis(
                video_data=video_data,
                performance_score=performance_score,
                success_factors=[],
                improvement_areas=[],
                audience_insights={},
                content_recommendations=[],
                optimization_suggestions=[]
            )
            
        except Exception as e:
            logger.error(f"❌ Eroare la analiza performanței: {e}")
            return PerformanceAnalysis(
                video_data=video_data,
                performance_score=performance_score,
                success_factors=[],
                improvement_areas=[],
                audience_insights={},
                content_recommendations=[],
                optimization_suggestions=[]
            )
    
    async def analyze_performance_trends(self, 
                                       video_performances: List[PerformanceAnalysis],
                                       time_period: str = "last_30_days") -> TrendAnalysis:
        """Analizează trend-urile de performanță pe o perioadă"""
        
        if not self.is_available() or not video_performances:
            return TrendAnalysis(
                time_period=time_period,
                top_performing_categories=[],
                best_hashtags=[],
                optimal_posting_times=[],
                audience_preferences={},
                content_patterns=[],
                improvement_recommendations=[]
            )
        
        # Pregătește datele pentru analiză
        performance_summary = {
            "total_videos": len(video_performances),
            "avg_performance_score": sum(p.performance_score for p in video_performances) / len(video_performances),
            "categories": [p.video_data.category for p in video_performances],
            "platforms": [p.video_data.platform for p in video_performances],
            "top_performers": [p for p in video_performances if p.performance_score >= 8.0],
            "poor_performers": [p for p in video_performances if p.performance_score < 5.0]
        }
        
        prompt = f"""
        Analizează trend-urile de performanță pentru perioada {time_period}:
        
        SUMAR PERFORMANȚĂ:
        - Total videoclipuri: {performance_summary['total_videos']}
        - Scor mediu performanță: {performance_summary['avg_performance_score']:.2f}
        - Categorii: {', '.join(set(performance_summary['categories']))}
        - Platforme: {', '.join(set(performance_summary['platforms']))}
        - Videoclipuri top: {len(performance_summary['top_performers'])}
        - Videoclipuri slabe: {len(performance_summary['poor_performers'])}
        
        FACTORI DE SUCCES IDENTIFICAȚI:
        {[factor for p in performance_summary['top_performers'] for factor in p.success_factors]}
        
        Analizează și identifică:
        1. Categoriile cu cea mai bună performanță
        2. Hashtag-urile cele mai eficiente
        3. Orele optime de postare
        4. Preferințele audienței
        5. Pattern-urile de conținut de succes
        6. Recomandări pentru îmbunătățire
        
        Returnează în format JSON:
        {{
            "top_performing_categories": ["categorie1", "categorie2"],
            "best_hashtags": ["#hashtag1", "#hashtag2"],
            "optimal_posting_times": ["18:00-20:00", "12:00-14:00"],
            "audience_preferences": {{
                "content_style": "stil preferat",
                "video_length": "lungime optimă",
                "topics": "subiecte populare"
            }},
            "content_patterns": ["pattern1", "pattern2"],
            "improvement_recommendations": ["recomandare1", "recomandare2"]
        }}
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o",
                max_tokens=1500,
                temperature=0.6
            )
            
            if response:
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group())
                    
                    trend_analysis = TrendAnalysis(
                        time_period=time_period,
                        top_performing_categories=data.get('top_performing_categories', []),
                        best_hashtags=data.get('best_hashtags', []),
                        optimal_posting_times=data.get('optimal_posting_times', []),
                        audience_preferences=data.get('audience_preferences', {}),
                        content_patterns=data.get('content_patterns', []),
                        improvement_recommendations=data.get('improvement_recommendations', [])
                    )
                    
                    logger.info(f"✅ Analiză trend-uri completă pentru {time_period}")
                    return trend_analysis
            
            logger.warning("Nu s-a putut analiza trend-urile de performanță")
            return TrendAnalysis(
                time_period=time_period,
                top_performing_categories=[],
                best_hashtags=[],
                optimal_posting_times=[],
                audience_preferences={},
                content_patterns=[],
                improvement_recommendations=[]
            )
            
        except Exception as e:
            logger.error(f"❌ Eroare la analiza trend-urilor: {e}")
            return TrendAnalysis(
                time_period=time_period,
                top_performing_categories=[],
                best_hashtags=[],
                optimal_posting_times=[],
                audience_preferences={},
                content_patterns=[],
                improvement_recommendations=[]
            )


# Global service instance
performance_analyzer = PerformanceAnalyzer()


async def test_performance_analyzer():
    """Test pentru analizatorul de performanță"""
    print("🧪 Testing Performance Analyzer...")
    
    if not performance_analyzer.is_available():
        print("❌ Performance Analyzer not available")
        return False
    
    # Date de test pentru un videoclip
    test_video_data = VideoPerformanceData(
        video_id="test_001",
        title="Rețete românești rapide și delicioase",
        category="food_romania",
        platform="tiktok",
        views=75000,
        likes=5200,
        comments=340,
        shares=890,
        watch_time_avg=45.2,
        engagement_rate=6.8,
        click_through_rate=4.2,
        retention_rate=62.5,
        upload_date=datetime.now() - timedelta(days=7),
        hashtags=["#retete", "#mancare", "#romania", "#cooking", "#viral"],
        script_length=180,
        thumbnail_style="colorful_food"
    )
    
    # Test analiza performanței
    print("📊 Testing video performance analysis...")
    analysis = await performance_analyzer.analyze_video_performance(test_video_data)
    
    print(f"✅ Performance analysis completed!")
    print(f"📈 Performance score: {analysis.performance_score:.1f}/10")
    print(f"🎯 Success factors: {len(analysis.success_factors)}")
    print(f"🔧 Improvement areas: {len(analysis.improvement_areas)}")
    print(f"💡 Content recommendations: {len(analysis.content_recommendations)}")
    
    return True


if __name__ == "__main__":
    asyncio.run(test_performance_analyzer())
