"""
Interfața UI pentru instrumentele de dezvoltatori și mentenanță.
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
from typing import Dict, List
from loguru import logger
import pandas as pd

import sys
import os

# Adaugă calea către modulele aplicației
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from app.utils.dev_tools import get_dev_tools_manager


def render_dev_tools() -> None:
    """Renderează interfața pentru instrumentele de dezvoltatori."""
    st.subheader("🛠️ Instrumente Dezvoltatori")
    
    dev_tools = get_dev_tools_manager()
    
    # Tabs pentru diferite secțiuni
    tab1, tab2, tab3, tab4 = st.tabs([
        "🧹 Curățare", 
        "📊 Monitorizare Disk", 
        "📋 Informații Sistem",
        "📝 Logs Viewer"
    ])
    
    with tab1:
        _render_cleanup_tools(dev_tools)
    
    with tab2:
        _render_disk_monitor(dev_tools)
    
    with tab3:
        _render_system_info(dev_tools)
    
    with tab4:
        _render_logs_viewer()


def _render_cleanup_tools(dev_tools) -> None:
    """Renderează instrumentele de curățare."""
    st.markdown("### 🧹 Instrumente Curățare")
    
    # Informații despre directoarele de storage
    storage_summary = dev_tools.get_storage_summary()
    
    # Afișează rezumatul storage-ului
    st.markdown("#### 📁 Rezumat Storage")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        temp_info = storage_summary.get("temp")
        if temp_info:
            st.metric(
                label="📂 Temp",
                value=f"{temp_info.size_mb:.1f} MB",
                delta=f"{temp_info.file_count} fișiere"
            )
    
    with col2:
        cache_info = storage_summary.get("cache")
        if cache_info:
            st.metric(
                label="💾 Cache",
                value=f"{cache_info.size_mb:.1f} MB",
                delta=f"{cache_info.file_count} fișiere"
            )
    
    with col3:
        videos_info = storage_summary.get("videos")
        if videos_info:
            st.metric(
                label="🎬 Videos",
                value=f"{videos_info.size_mb:.1f} MB",
                delta=f"{videos_info.file_count} fișiere"
            )
    
    with col4:
        tasks_info = storage_summary.get("tasks")
        if tasks_info:
            st.metric(
                label="📋 Tasks",
                value=f"{tasks_info.size_mb:.1f} MB",
                delta=f"{tasks_info.file_count} fișiere"
            )
    
    st.markdown("---")
    
    # Butoane pentru curățare individuală
    st.markdown("#### 🎯 Curățare Selectivă")

    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🗑️ Curăță Fișiere Temporare", type="secondary"):
            with st.spinner("Curățare fișiere temporare..."):
                success, message, stats = dev_tools.clear_temp_files()
                if success:
                    st.success(message)
                else:
                    st.error(message)
                st.rerun()
    
    with col2:
        if st.button("💾 Curăță Cache Video", type="secondary"):
            with st.spinner("Curățare cache video..."):
                success, message, stats = dev_tools.clear_cache_videos()
                if success:
                    st.success(message)
                else:
                    st.error(message)
                st.rerun()

    with col3:
        if st.button("🔓 Curăță Fișiere Blocate", type="secondary"):
            with st.spinner("Curățare fișiere blocate și eliberare memorie..."):
                success, message, stats = dev_tools.cleanup_locked_files()
                if success:
                    st.success(message)
                    if stats:
                        st.info(f"Fișiere blocate găsite: {stats.get('locked_files', 0)}, Șterse: {stats.get('files_deleted', 0)}")
                else:
                    st.error(message)
                st.rerun()

    with col4:
        # Selector pentru numărul de videoclipuri de păstrat
        keep_videos = st.number_input(
            "Păstrează ultimele N videoclipuri:",
            min_value=1,
            max_value=100,
            value=10,
            help="Numărul de videoclipuri recente de păstrat"
        )

        if st.button("🎬 Curăță Videoclipuri Vechi", type="secondary"):
            with st.spinner(f"Curățare videoclipuri vechi (păstrează {keep_videos})..."):
                success, message, stats = dev_tools.cleanup_old_videos(keep_videos)
                if success:
                    st.success(message)
                else:
                    st.error(message)
                st.rerun()
    
    st.markdown("---")
    
    # Curățare completă
    st.markdown("#### 🧹 Curățare Completă")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.warning("⚠️ **Atenție:** Curățarea completă va șterge toate fișierele temporare, cache-ul și videoclipurile vechi!")
        
        keep_videos_complete = st.number_input(
            "Păstrează ultimele N videoclipuri (curățare completă):",
            min_value=1,
            max_value=100,
            value=10,
            key="keep_videos_complete"
        )
    
    with col2:
        if st.button("🧹 CURĂȚARE COMPLETĂ", type="primary"):
            with st.spinner("Efectuare curățare completă..."):
                success, message, stats = dev_tools.cleanup_all(keep_videos_complete)
                
                if success:
                    st.success("✅ Curățare completă finalizată cu succes!")
                else:
                    st.error("❌ Curățarea completă a întâmpinat erori!")
                
                # Afișează detaliile
                st.text(message)
                
                # Afișează statisticile detaliate
                if stats:
                    st.markdown("**Statistici detaliate:**")
                    for category, category_stats in stats.items():
                        files_deleted = category_stats.get("files_deleted", 0)
                        size_freed = category_stats.get("size_freed_mb", 0)
                        st.write(f"• {category.title()}: {files_deleted} fișiere, {size_freed:.2f} MB")
                
                st.rerun()


def _render_disk_monitor(dev_tools) -> None:
    """Renderează monitorul de utilizare disk."""
    st.markdown("### 📊 Monitorizare Spațiu Disk")
    
    # Informații generale despre disk
    disk_info = dev_tools.get_disk_usage()
    
    # Metrici principale
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="💾 Spațiu Total",
            value=f"{disk_info.total_gb:.1f} GB"
        )
    
    with col2:
        st.metric(
            label="📊 Spațiu Folosit",
            value=f"{disk_info.used_gb:.1f} GB",
            delta=f"{disk_info.usage_percent:.1f}%"
        )
    
    with col3:
        st.metric(
            label="🆓 Spațiu Liber",
            value=f"{disk_info.free_gb:.1f} GB"
        )
    
    with col4:
        # Indicator de stare bazat pe utilizare
        if disk_info.usage_percent < 70:
            status = "🟢 Bun"
        elif disk_info.usage_percent < 85:
            status = "🟡 Atenție"
        else:
            status = "🔴 Critic"
        
        st.metric(
            label="📈 Status Disk",
            value=status
        )
    
    # Grafic circular pentru utilizarea diskului
    st.markdown("#### 🥧 Utilizare Disk")
    
    fig_pie = go.Figure(data=[go.Pie(
        labels=['Folosit', 'Liber'],
        values=[disk_info.used_gb, disk_info.free_gb],
        hole=0.4,
        marker_colors=['#ff6b6b', '#51cf66']
    )])
    
    fig_pie.update_layout(
        title="Distribuția Spațiului pe Disk",
        showlegend=True,
        height=400
    )
    
    st.plotly_chart(fig_pie, use_container_width=True)
    
    # Grafic cu barele pentru directoarele de storage
    st.markdown("#### 📁 Utilizare Storage pe Directoare")
    
    storage_summary = dev_tools.get_storage_summary()
    
    if storage_summary:
        # Pregătește datele pentru grafic
        directories = []
        sizes = []
        file_counts = []
        
        for name, info in storage_summary.items():
            directories.append(name.title())
            sizes.append(info.size_mb)
            file_counts.append(info.file_count)
        
        # Grafic cu bare pentru dimensiuni
        fig_bar = go.Figure()
        
        fig_bar.add_trace(go.Bar(
            x=directories,
            y=sizes,
            name='Dimensiune (MB)',
            marker_color='#4dabf7',
            text=[f"{size:.1f} MB" for size in sizes],
            textposition='auto'
        ))
        
        fig_bar.update_layout(
            title="Dimensiunea Directoarelor de Storage",
            xaxis_title="Directoare",
            yaxis_title="Dimensiune (MB)",
            height=400
        )
        
        st.plotly_chart(fig_bar, use_container_width=True)
        
        # Tabel cu detalii
        st.markdown("#### 📋 Detalii Directoare")
        
        df_data = []
        for name, info in storage_summary.items():
            df_data.append({
                "Director": name.title(),
                "Dimensiune (MB)": f"{info.size_mb:.2f}",
                "Număr Fișiere": info.file_count,
                "Ultima Modificare": info.last_modified.strftime("%Y-%m-%d %H:%M:%S")
            })
        
        df = pd.DataFrame(df_data)
        st.dataframe(df, use_container_width=True)


def _render_system_info(dev_tools) -> None:
    """Renderează informațiile despre sistem."""
    st.markdown("### 📋 Informații Sistem")
    
    # Obține informațiile despre sistem
    system_info = dev_tools.get_system_info()
    
    # Afișează informațiile în format card
    for key, value in system_info.items():
        col1, col2 = st.columns([1, 2])
        with col1:
            st.write(f"**{key}:**")
        with col2:
            st.write(value)
    
    st.markdown("---")
    
    # Buton pentru refresh
    if st.button("🔄 Actualizează Informații", type="secondary"):
        st.rerun()
    
    # Informații despre aplicație
    st.markdown("#### 📱 Informații Aplicație")
    
    app_info = {
        "Nume Aplicație": "MoneyPrinterTurbo - Aparat de scos masele la fraieri",
        "Versiune": "1.0.0",
        "Python Version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        "Directorul de Lucru": os.getcwd(),
        "Data/Ora Curentă": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    for key, value in app_info.items():
        col1, col2 = st.columns([1, 2])
        with col1:
            st.write(f"**{key}:**")
        with col2:
            st.write(value)


def _render_logs_viewer() -> None:
    """Renderează viewer-ul pentru logs."""
    st.markdown("### 📝 Logs Viewer")
    
    # Caută fișierele de log
    log_files = []
    possible_log_dirs = ["logs", ".", "storage/logs"]
    
    for log_dir in possible_log_dirs:
        log_path = os.path.join(log_dir)
        if os.path.exists(log_path):
            for file in os.listdir(log_path):
                if file.endswith(('.log', '.txt')) and 'log' in file.lower():
                    log_files.append(os.path.join(log_path, file))
    
    if not log_files:
        st.info("📄 Nu s-au găsit fișiere de log în directoarele standard.")
        
        # Opțiune pentru specificarea manuală a unui fișier de log
        custom_log_path = st.text_input(
            "Specifică calea către un fișier de log:",
            placeholder="ex: logs/app.log"
        )
        
        if custom_log_path and os.path.exists(custom_log_path):
            log_files = [custom_log_path]
    
    if log_files:
        # Selector pentru fișierul de log
        selected_log = st.selectbox(
            "Selectează fișierul de log:",
            options=log_files,
            format_func=lambda x: os.path.basename(x)
        )
        
        if selected_log:
            # Opțiuni pentru afișare
            col1, col2, col3 = st.columns(3)
            
            with col1:
                max_lines = st.number_input(
                    "Numărul maxim de linii:",
                    min_value=10,
                    max_value=1000,
                    value=100,
                    help="Numărul maxim de linii de afișat"
                )
            
            with col2:
                show_from_end = st.checkbox(
                    "Afișează de la sfârșitul fișierului",
                    value=True,
                    help="Afișează ultimele linii din fișier"
                )
            
            with col3:
                filter_text = st.text_input(
                    "Filtrează după text:",
                    placeholder="ex: ERROR, WARNING",
                    help="Afișează doar liniile care conțin acest text"
                )
            
            # Citește și afișează log-ul
            try:
                with open(selected_log, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                
                # Aplică filtrul dacă este specificat
                if filter_text:
                    lines = [line for line in lines if filter_text.lower() in line.lower()]
                
                # Limitează numărul de linii
                if show_from_end:
                    lines = lines[-max_lines:]
                else:
                    lines = lines[:max_lines]
                
                # Afișează log-ul
                if lines:
                    log_content = ''.join(lines)
                    st.text_area(
                        f"Conținut log ({len(lines)} linii):",
                        value=log_content,
                        height=400,
                        help="Conținutul fișierului de log"
                    )
                    
                    # Buton pentru descărcare
                    st.download_button(
                        label="⬇️ Descarcă Log",
                        data=log_content,
                        file_name=f"{os.path.basename(selected_log)}",
                        mime="text/plain"
                    )
                else:
                    st.info("📄 Nu s-au găsit linii care să corespundă filtrului.")
                
            except Exception as e:
                st.error(f"❌ Eroare la citirea fișierului de log: {e}")
            
            # Buton pentru refresh
            if st.button("🔄 Actualizează Log", type="secondary"):
                st.rerun()
    
    # Informații despre logging
    st.markdown("#### ℹ️ Informații Logging")
    st.info(
        "💡 **Sfat:** Pentru a vedea log-urile în timp real, poți folosi comanda `tail -f` "
        "în terminal pentru fișierele de log găsite."
    )
