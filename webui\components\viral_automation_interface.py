#!/usr/bin/env python3
"""
Viral Automation Interface

Interfața web completă pentru toate funcționalitățile de automatizare virală
folosind GPT4Free în MoneyPrinterTurbo.
"""

import streamlit as st
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from loguru import logger

try:
    from app.services.viral_content_generator import viral_content_generator
    from app.services.one_click_viral_generator import one_click_viral_generator, ViralVideoConfig
    from app.services.romanian_content_optimizer import romanian_content_optimizer
    from app.services.performance_analyzer import performance_analyzer, VideoPerformanceData
    from app.services.contextual_image_ai import contextual_image_ai
    VIRAL_SERVICES_AVAILABLE = True
except ImportError as e:
    VIRAL_SERVICES_AVAILABLE = False
    print(f"❌ Viral services import error: {e}")


def render_one_click_viral_generator():
    """Renderează interfața pentru One-Click Viral Video Generator"""
    
    st.markdown("## 🚀 One-Click Viral Video Generator")
    st.markdown("*Generează videoclipuri virale complete cu fișier video final*")
    
    if not VIRAL_SERVICES_AVAILABLE:
        st.error("❌ Serviciile de automatizare virală nu sunt disponibile")
        st.info("Verifică instalarea GPT4Free și dependințele")
        return
    
    # Configurare generare
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### ⚙️ Configurare Generare")
        
        category = st.selectbox(
            "📂 Categoria conținutului:",
            [
                "lifestyle_romania", "travel_romania", "food_romania", 
                "history_romania", "tech_trends", "motivation",
                "entertainment", "education", "current_events"
            ],
            format_func=lambda x: {
                "lifestyle_romania": "🏠 Lifestyle România",
                "travel_romania": "✈️ Călătorii România", 
                "food_romania": "🍽️ Mâncare România",
                "history_romania": "📚 Istorie România",
                "tech_trends": "💻 Tehnologie & Trends",
                "motivation": "💪 Motivație",
                "entertainment": "🎭 Divertisment",
                "education": "🎓 Educație",
                "current_events": "📰 Evenimente Actuale"
            }[x],
            help="Selectează categoria pentru conținutul viral"
        )
        
        target_audience = st.selectbox(
            "👥 Audiența țintă:",
            [
                "tineri români 18-25",
                "tineri români 18-35", 
                "adulți români 25-40",
                "părinți români 30-45",
                "profesionisti români 25-35"
            ],
            index=1,
            help="Definește audiența țintă pentru optimizarea conținutului"
        )
        
        platform = st.selectbox(
            "📱 Platforma principală:",
            ["tiktok", "instagram_reels", "youtube_shorts", "facebook"],
            format_func=lambda x: {
                "tiktok": "📱 TikTok",
                "instagram_reels": "📸 Instagram Reels",
                "youtube_shorts": "🎬 YouTube Shorts", 
                "facebook": "👥 Facebook"
            }[x],
            help="Selectează platforma principală pentru optimizare"
        )
    
    with col2:
        st.markdown("### 🎯 Opțiuni Avansate")
        
        duration = st.slider(
            "⏱️ Durata videoclip (secunde):",
            min_value=15,
            max_value=120,
            value=60,
            step=15,
            help="Durata dorită pentru videoclip"
        )
        
        use_contextual_images = st.checkbox(
            "🖼️ Imagini contextuale AI",
            value=True,
            help="Generează imagini contextuale folosind AI"
        )
        
        max_contextual_images = st.slider(
            "📊 Numărul maxim de imagini:",
            min_value=3,
            max_value=15,
            value=8,
            help="Numărul maxim de imagini contextuale"
        ) if use_contextual_images else 0
        
        include_subtitles = st.checkbox(
            "📝 Include subtitrări",
            value=True,
            help="Adaugă subtitrări automate"
        )

    # Video generation options
    st.markdown("### 🎬 Opțiuni Generare Video")
    col3, col4 = st.columns(2)

    with col3:
        generate_video_file = st.checkbox(
            "🎥 Generează fișier video complet",
            value=True,
            help="Creează fișierul video final gata pentru upload"
        )

        voice_name = st.selectbox(
            "🎤 Voce TTS:",
            ["ro-RO-AlinaNeural", "ro-RO-EmilNeural", "ro-RO-IoanNeural"],
            format_func=lambda x: {
                "ro-RO-AlinaNeural": "🎤 Alina (Femeie)",
                "ro-RO-EmilNeural": "🎤 Emil (Bărbat)",
                "ro-RO-IoanNeural": "🎤 Ioan (Bărbat)"
            }[x],
            help="Vocea pentru text-to-speech"
        ) if generate_video_file else "ro-RO-AlinaNeural"

    with col4:
        voice_rate = st.selectbox(
            "⚡ Viteza vorbirii:",
            ["-20%", "-10%", "+0%", "+10%", "+20%"],
            index=2,
            help="Viteza de vorbire pentru TTS"
        ) if generate_video_file else "+0%"

        bgm_volume = st.slider(
            "🎵 Volum muzică de fundal:",
            min_value=0.0,
            max_value=0.5,
            value=0.2,
            step=0.1,
            help="Volumul muzicii de fundal"
        ) if generate_video_file else 0.2
        
        optimize_for_seo = st.checkbox(
            "🔍 Optimizare SEO",
            value=True,
            help="Optimizează pentru motoarele de căutare"
        )
    
    # Buton de generare
    st.markdown("---")

    if st.button("🚀 GENEREAZĂ VIDEOCLIP VIRAL", type="primary", use_container_width=True):

        # Creează configurația
        config = ViralVideoConfig(
            category=category,
            target_audience=target_audience,
            platform=platform,
            duration=duration,
            use_contextual_images=use_contextual_images,
            max_contextual_images=max_contextual_images,
            include_subtitles=include_subtitles,
            optimize_for_seo=optimize_for_seo
        )

        # Adaugă opțiunile de generare video
        config.generate_video_file = generate_video_file
        config.voice_name = voice_name if generate_video_file else "ro-RO-AlinaNeural"
        config.voice_rate = voice_rate if generate_video_file else "+0%"
        config.bgm_volume = bgm_volume if generate_video_file else 0.2

        # Enhanced progress tracking containers
        progress_container = st.container()
        with progress_container:
            st.markdown("### 🔄 Progres Generare Virală")

            # Main progress bar
            main_progress = st.progress(0)
            main_status = st.empty()

            # Detailed progress sections
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### 📊 Status Detaliat")
                step_status = st.empty()
                model_info = st.empty()
                timing_info = st.empty()

            with col2:
                st.markdown("#### 🎯 Rezultate Intermediare")
                intermediate_results = st.empty()

            # Error/Success messages
            messages_container = st.empty()

        # Start the enhanced generation process
        try:
            # Simulează procesul async în sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Run enhanced generation with real-time feedback
            if config.generate_video_file:
                result = loop.run_until_complete(
                    generate_complete_viral_video_with_feedback(
                        config,
                        main_progress,
                        main_status,
                        step_status,
                        model_info,
                        timing_info,
                        intermediate_results,
                        messages_container
                    )
                )
            else:
                result = loop.run_until_complete(
                    generate_viral_video_with_feedback(
                        config,
                        main_progress,
                        main_status,
                        step_status,
                        model_info,
                        timing_info,
                        intermediate_results,
                        messages_container
                    )
                )

            loop.close()

            if result and result.success:
                st.success("🎉 Videoclip viral generat cu succes!")

                # Afișează rezultatele
                display_viral_video_results(result)

                # Salvează în session state pentru utilizare ulterioară
                st.session_state.viral_video_result = result

            else:
                error_msg = result.error_message if result else "Eroare necunoscută"
                st.error(f"❌ Generarea a eșuat: {error_msg}")

        except Exception as e:
            st.error(f"❌ Eroare la generarea videoclipului viral: {e}")
            import traceback
            st.code(traceback.format_exc())


async def generate_complete_viral_video_with_feedback(
    config,
    main_progress,
    main_status,
    step_status,
    model_info,
    timing_info,
    intermediate_results,
    messages_container
):
    """Generează un videoclip viral complet cu feedback în timp real"""

    start_time = time.time()
    step_times = {}

    try:
        # Pasul 1-8: Generează pachetul de conținut (folosește funcția existentă)
        content_result = await generate_viral_video_with_feedback(
            config,
            main_progress,
            main_status,
            step_status,
            model_info,
            timing_info,
            intermediate_results,
            messages_container
        )

        if not content_result or not content_result.success:
            return content_result

        # Pasul 9: Generează fișierul video complet
        step_start = time.time()
        main_progress.progress(90)
        main_status.text("🎬 Generez fișierul video complet...")
        step_status.markdown("**Pasul 9/10:** Creare fișier video cu audio, imagini și subtitluri")

        intermediate_results.markdown("🎬 **Creez videoclipul final...**")

        # Folosește serviciul one-click viral generator pentru video complet
        from app.services.one_click_viral_generator import OneClickViralGenerator

        generator = OneClickViralGenerator()

        # Creează fișierul video folosind pachetul de conținut generat
        video_result = await generator._create_video_file(
            content_result.video_package,
            content_result.contextual_image_plan,
            config
        )

        # Actualizează rezultatul cu informațiile video
        content_result.video_files = video_result.get("video_files", [])
        content_result.task_id = video_result.get("task_id")
        content_result.audio_file = video_result.get("audio_file")
        content_result.subtitle_file = video_result.get("subtitle_file")

        step_times["video"] = time.time() - step_start
        timing_info.text(f"⏱️ Video generation: {step_times['video']:.2f}s")

        if content_result.video_files:
            intermediate_results.markdown(f"""
            ✅ **Videoclip generat:**
            - **Fișier:** {content_result.video_files[0]}
            - **Task ID:** {content_result.task_id}
            - **Audio:** {'✅' if content_result.audio_file else '❌'}
            - **Subtitluri:** {'✅' if content_result.subtitle_file else '❌'}
            """)
            messages_container.success(f"🎬 Videoclip complet generat: {content_result.video_files[0]}")
        else:
            messages_container.warning("⚠️ Pachetul de conținut generat, dar fără fișier video")

        # Pasul 10: Finalizare
        step_start = time.time()
        main_progress.progress(100)
        main_status.text("🎉 Finalizare generare completă...")
        step_status.markdown("**Pasul 10/10:** Finalizare și optimizare finală")

        content_result.generation_time = time.time() - start_time
        step_times["finalization"] = time.time() - step_start
        timing_info.text(f"⏱️ Finalizare: {step_times['finalization']:.2f}s")

        # Mesaj final de succes
        total_time = time.time() - start_time
        if content_result.video_files:
            messages_container.success(f"🎉 Videoclip viral complet generat în {total_time:.2f} secunde!")
            intermediate_results.markdown(f"""
            ## 🎬 **VIDEOCLIP VIRAL COMPLET GENERAT!**

            **📁 Fișier video:** `{content_result.video_files[0]}`
            **⏱️ Timp total:** {total_time:.2f} secunde
            **🎯 Platformă:** {config.platform}
            **📊 Durată:** {config.duration}s

            **✅ Componente incluse:**
            - 🎤 Audio TTS în română
            - 🖼️ Imagini contextuale
            - 📝 Subtitluri automate
            - 🎵 Muzică de fundal
            - 🎨 Optimizare pentru {config.platform}
            """)
        else:
            messages_container.success(f"🎉 Pachet viral complet generat în {total_time:.2f} secunde!")

        return content_result

    except Exception as e:
        messages_container.error(f"❌ Eroare în procesul de generare completă: {str(e)}")
        logger.error(f"❌ Eroare la generarea completă: {e}")

        # Returnează rezultatul parțial dacă există
        if 'content_result' in locals() and content_result:
            content_result.error_message = str(e)
            return content_result

        # Altfel returnează un rezultat de eroare
        from app.services.one_click_viral_generator import ViralVideoResult
        return ViralVideoResult(
            success=False,
            video_package=None,
            contextual_image_plan=None,
            generation_time=time.time() - start_time,
            video_files=[],
            error_message=str(e)
        )


def display_viral_video_results(result):
    """Afișează rezultatele generării videoclipului viral"""
    
    if not result.success or not result.video_package:
        return
    
    package = result.video_package
    
    st.markdown("## 📋 Rezultate Generare Virală")
    
    # Tab-uri pentru organizarea rezultatelor
    tabs = ["📰 Conținut Principal", "🖼️ Imagini Contextuale", "📈 Predicții Performanță", "🔧 Detalii Tehnice"]

    # Adaugă tab pentru video dacă există fișiere video
    if result.video_files:
        tabs.insert(0, "🎬 Video Generat")

    if len(tabs) == 5:
        tab_video, tab1, tab2, tab3, tab4 = st.tabs(tabs)
    else:
        tab1, tab2, tab3, tab4 = st.tabs(tabs)

    # Tab pentru video generat (dacă există)
    if result.video_files:
        with tab_video:
            st.markdown("### 🎬 Videoclip Viral Generat")

            for i, video_file in enumerate(result.video_files):
                st.success(f"✅ **Videoclip {i+1}:** `{video_file}`")

                # Informații despre video
                col1, col2 = st.columns(2)
                with col1:
                    if result.task_id:
                        st.info(f"🆔 **Task ID:** {result.task_id}")
                    if result.audio_file:
                        st.info(f"🎤 **Audio:** {result.audio_file}")

                with col2:
                    if result.subtitle_file:
                        st.info(f"📝 **Subtitluri:** {result.subtitle_file}")
                    st.info(f"⏱️ **Timp generare:** {result.generation_time:.2f}s")

                # Buton pentru download (dacă fișierul există)
                import os
                if os.path.exists(video_file):
                    with open(video_file, "rb") as file:
                        st.download_button(
                            label=f"📥 Descarcă Videoclip {i+1}",
                            data=file.read(),
                            file_name=os.path.basename(video_file),
                            mime="video/mp4",
                            use_container_width=True
                        )
                else:
                    st.warning(f"⚠️ Fișierul video nu a fost găsit: {video_file}")

            # Instrucțiuni pentru upload
            st.markdown("### 📱 Instrucțiuni Upload")
            st.info("""
            **🚀 Videoclipul tău viral este gata pentru upload!**

            **📱 TikTok:** Deschide aplicația → Apasă "+" → Selectează videoclipul → Adaugă hashtag-urile generate
            **📸 Instagram Reels:** Deschide Instagram → Apasă "+" → Reels → Selectează videoclipul
            **🎬 YouTube Shorts:** Deschide YouTube → Apasă "+" → Creează un Short → Selectează videoclipul

            **💡 Sfat:** Folosește titlul și hashtag-urile generate pentru maximizarea vizibilității!
            """)
    
    with tab1:
        st.markdown("### 🎯 Subiect Viral")
        st.info(f"**{package.topic.title}**")
        st.write(f"📝 {package.topic.description}")
        
        col1, col2 = st.columns(2)
        with col1:
            st.metric("🔥 Potențial Viral", f"{package.topic.viral_potential:.1f}/10")
            st.metric("🇷🇴 Relevanță România", f"{package.topic.romanian_relevance:.1f}/10")
        with col2:
            st.write(f"👥 **Audiența țintă:** {package.topic.target_audience}")
            st.write(f"📂 **Categorie:** {package.topic.category}")
        
        st.markdown("### 📝 Script Generat")
        st.text_area(
            "Script complet:",
            value=package.script.script_text,
            height=200,
            help="Scriptul generat pentru videoclip"
        )
        
        col1, col2 = st.columns(2)
        with col1:
            st.markdown("**🎣 Hook (primele secunde):**")
            st.write(package.script.hook)
        with col2:
            st.markdown("**📢 Call-to-Action:**")
            st.write(package.script.call_to_action)
        
        st.markdown("### 📰 Titlu și Descriere")
        st.text_input("📰 Titlu:", value=package.title, disabled=True)
        st.text_area("📝 Descriere:", value=package.description, height=100, disabled=True)
        
        st.markdown("### 🏷️ Hashtag-uri și Keywords")
        col1, col2 = st.columns(2)
        with col1:
            st.markdown("**Hashtag-uri:**")
            for hashtag in package.hashtags:
                st.write(f"• {hashtag}")
        with col2:
            st.markdown("**Termeni de căutare:**")
            for term in package.search_terms:
                st.write(f"• {term}")
    
    with tab2:
        if result.contextual_image_plan:
            st.markdown("### 🖼️ Plan Imagini Contextuale")
            
            plan = result.contextual_image_plan
            
            st.info(f"**Tema vizuală:** {plan.visual_theme}")
            st.write(f"**Mood general:** {plan.overall_mood}")
            
            st.markdown("#### 🎨 Prompt-uri pentru Imagini")
            for i, prompt in enumerate(plan.image_prompts, 1):
                with st.expander(f"Imagine {i}: {prompt.timing[0]:.1f}s - {prompt.timing[1]:.1f}s"):
                    st.write(f"**Segment text:** {prompt.segment_text}")
                    st.write(f"**Prompt:** {prompt.prompt}")
                    st.write(f"**Stil:** {prompt.style}")
                    st.write(f"**Mood:** {prompt.mood}")
                    if prompt.negative_prompt:
                        st.write(f"**Negative prompt:** {prompt.negative_prompt}")
            
            st.markdown("#### 🖼️ Thumbnail")
            st.text_area(
                "Prompt pentru thumbnail:",
                value=plan.thumbnail_prompt,
                height=100,
                disabled=True
            )
        else:
            st.info("Nu au fost generate imagini contextuale pentru acest videoclip.")
    
    with tab3:
        if result.performance_predictions:
            st.markdown("### 📈 Predicții de Performanță")
            
            pred = result.performance_predictions
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("👀 Views Estimate", pred.get('estimated_views', 'N/A'))
            with col2:
                st.metric("💝 Engagement Rate", pred.get('estimated_engagement_rate', 'N/A'))
            with col3:
                viral_prob = pred.get('viral_probability', 'medium')
                st.metric("🔥 Probabilitate Virală", viral_prob.title())
            
            st.markdown("#### 🎯 Factori de Succes")
            for factor in pred.get('success_factors', []):
                st.write(f"✅ {factor}")
            
            st.markdown("#### 💡 Sugestii de Îmbunătățire")
            for suggestion in pred.get('improvement_suggestions', []):
                st.write(f"💡 {suggestion}")
            
            st.info(f"⏰ **Ora optimă de postare:** {pred.get('best_posting_time', '18:00-21:00')}")
        else:
            st.info("Nu sunt disponibile predicții de performanță.")
    
    with tab4:
        st.markdown("### 🔧 Detalii Tehnice")
        
        col1, col2 = st.columns(2)
        with col1:
            st.write(f"⏱️ **Timp de generare:** {result.generation_time:.2f} secunde")
            st.write(f"📱 **Platforme țintă:** {', '.join(package.target_platforms)}")
            st.write(f"⏰ **Durată estimată:** {package.script.estimated_duration} secunde")
        with col2:
            st.write(f"🎭 **Tip conținut:** {package.topic.content_type}")
            st.write(f"📊 **Engagement estimat:** {package.topic.estimated_engagement}")
            if package.script.romanian_cultural_refs:
                st.write(f"🇷🇴 **Referințe culturale:** {len(package.script.romanian_cultural_refs)}")
        
        if package.seo_keywords:
            st.markdown("#### 🔍 Keywords SEO")
            keywords_text = ", ".join(package.seo_keywords)
            st.text_area("Keywords pentru SEO:", value=keywords_text, height=80, disabled=True)


async def generate_title_and_description_fallback(topic, script, platform):
    """Fallback method for generating title and description"""
    try:
        from app.services.gpt4free_service import gpt4free_service

        prompt = f"""
        Generează un titlu și o descriere captivante pentru un videoclip viral pe {platform}:

        SUBIECT: {topic.title}
        SCRIPT: {script.script_text[:200]}...

        Returnează în format JSON:
        {{
            "title": "titlu captivant",
            "description": "descriere optimizată pentru platformă"
        }}
        """

        response = await gpt4free_service.generate_text(
            prompt=prompt,
            model="gpt-4o",
            max_tokens=500,
            temperature=0.8
        )

        if response:
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                return data.get('title', topic.title), data.get('description', topic.description)

        return topic.title, topic.description

    except Exception:
        return topic.title, topic.description


async def generate_hashtags_fallback(topic, script, platform):
    """Fallback method for generating hashtags"""
    try:
        from app.services.gpt4free_service import gpt4free_service

        prompt = f"""
        Generează hashtag-uri virale pentru {platform} pentru acest conținut:

        SUBIECT: {topic.title}
        CATEGORIE: {topic.category}

        Returnează 10-15 hashtag-uri relevante, separate prin virgulă.
        Include hashtag-uri în română și engleză.
        """

        response = await gpt4free_service.generate_text(
            prompt=prompt,
            model="gpt-4o",
            max_tokens=300,
            temperature=0.7
        )

        if response:
            # Extract hashtags from response
            hashtags = []
            for line in response.split('\n'):
                if '#' in line:
                    tags = [tag.strip() for tag in line.split() if tag.startswith('#')]
                    hashtags.extend(tags)

            if hashtags:
                return hashtags[:15]  # Limit to 15 hashtags

        # Default hashtags if generation fails
        return [f"#{topic.category}", f"#{platform}", "#viral", "#romania", "#trending"]

    except Exception:
        return [f"#{topic.category}", f"#{platform}", "#viral", "#romania"]


def create_fallback_topic(config):
    """Create a fallback topic when AI generation fails"""
    from app.services.viral_content_generator import ViralTopic

    # Predefined fallback topics by category
    fallback_topics = {
        "motivation": {
            "title": "5 Secrete pentru Succes în România 2024",
            "description": "Descoperă strategiile care funcționează cu adevărat pentru tinerii români care vor să-și schimbe viața.",
            "keywords": ["succes", "motivație", "românia", "tineri", "dezvoltare"]
        },
        "lifestyle_romania": {
            "title": "Viața de Tânăr în România: Realitate vs Așteptări",
            "description": "O privire onestă asupra provocărilor și oportunităților pentru generația tânără din România.",
            "keywords": ["lifestyle", "românia", "tineri", "viață", "realitate"]
        },
        "food_romania": {
            "title": "Mâncarea Românească pe care o Iubesc Străinii",
            "description": "Descoperă preparatele românești care fac furori în străinătate și de ce sunt atât de speciale.",
            "keywords": ["mâncare", "românia", "tradiție", "bucătărie", "străini"]
        },
        "travel_romania": {
            "title": "Locuri Secrete din România pe care Trebuie să le Vezi",
            "description": "Destinații ascunse și spectaculoase din România pe care puțini le cunosc.",
            "keywords": ["călătorie", "românia", "locuri", "secrete", "turism"]
        }
    }

    # Get fallback for category or use motivation as default
    fallback = fallback_topics.get(config.category, fallback_topics["motivation"])

    return ViralTopic(
        title=fallback["title"],
        description=fallback["description"],
        category=config.category,
        viral_potential=7.5,
        target_audience=config.target_audience,
        trending_keywords=fallback["keywords"],
        romanian_relevance=9.0,
        estimated_engagement="high",
        content_type="motivational"
    )


def create_emergency_fallback_topic(config):
    """Create an emergency fallback topic when all else fails"""
    from app.services.viral_content_generator import ViralTopic

    return ViralTopic(
        title="Secretele Succesului în România 2024",
        description="Descoperă strategiile care funcționează cu adevărat pentru tinerii români care vor să-și schimbe viața.",
        category=config.category,
        viral_potential=7.0,
        target_audience=config.target_audience,
        trending_keywords=["succes", "românia", "tineri", "motivație", "2024"],
        romanian_relevance=9.0,
        estimated_engagement="high",
        content_type="motivational"
    )


def create_fallback_script(topic, config):
    """Create a fallback script when AI generation fails"""
    from app.services.viral_content_generator import ViralScript

    # Create a basic script based on the topic
    script_text = f"""
    Salut! Astăzi vreau să vorbesc despre {topic.title.lower()}.

    Știi care e problema cu majoritatea oamenilor? Că nu înțeleg cu adevărat ce înseamnă {topic.trending_keywords[0] if topic.trending_keywords else 'succesul'}.

    Să îți spun eu care sunt cele mai importante lucruri pe care trebuie să le știi:

    Primul lucru: {topic.description[:100]}...

    Al doilea lucru: În România, lucrurile stau puțin diferit. Noi avem propriile noastre provocări și oportunități.

    Al treilea lucru: Nu te compara cu alții. Fiecare are propriul său drum.

    Și cel mai important: Începe de astăzi! Nu mai aștepta momentul perfect.

    Dacă ți-a plăcut acest conținut, lasă un like și urmărește-mă pentru mai multe sfaturi!
    """

    return ViralScript(
        topic=topic,
        script_text=script_text.strip(),
        hook="Salut! Astăzi vreau să vorbesc despre ceva foarte important...",
        main_content=script_text.strip(),
        call_to_action="Dacă ți-a plăcut acest conținut, lasă un like și urmărește-mă pentru mai multe sfaturi!",
        estimated_duration=config.duration,
        engagement_elements=["întrebări retorice", "sfaturi practice", "call-to-action"],
        romanian_cultural_refs=["România", "românii", "în țara noastră"]
    )


async def generate_viral_video_with_feedback(
    config: ViralVideoConfig,
    main_progress,
    main_status,
    step_status,
    model_info,
    timing_info,
    intermediate_results,
    messages_container
):
    """Enhanced viral video generation with detailed real-time feedback"""

    start_time = time.time()
    step_times = {}

    try:
        # Step 1: Initialize and check services
        step_start = time.time()
        main_progress.progress(5)
        main_status.text("🔧 Inițializez serviciile AI...")
        step_status.markdown("**Pasul 1/8:** Verificare servicii disponibile")

        # Check GPT4Free availability
        if not one_click_viral_generator.is_available():
            messages_container.error("❌ Serviciile GPT4Free nu sunt disponibile")
            return None

        model_info.info("🤖 **Model activ:** GPT-4o via GPT4Free")
        step_times["init"] = time.time() - step_start
        timing_info.text(f"⏱️ Inițializare: {step_times['init']:.2f}s")

        # Step 2: Analyze trending topics
        step_start = time.time()
        main_progress.progress(15)
        main_status.text("📊 Analizez trending topics...")
        step_status.markdown("**Pasul 2/8:** Analiză trending topics pentru categoria selectată")

        # Show intermediate progress
        intermediate_results.markdown("🔍 **Analizez:** Trending topics pentru motivație...")

        # Import required services
        from app.services.viral_content_generator import viral_content_generator
        from app.services.contextual_image_ai import contextual_image_ai

        topic = None
        try:
            # Add timeout and progress updates
            intermediate_results.markdown("🤖 **Conectez la GPT4Free...**")

            # Try to generate topic with timeout
            topic = await asyncio.wait_for(
                viral_content_generator.generate_viral_topic(
                    category=config.category,
                    target_audience=config.target_audience,
                    platform=config.platform
                ),
                timeout=60.0  # 60 second timeout
            )

            # Check if topic generation was successful
            if not topic:
                messages_container.warning("⚠️ Generarea AI a returnat None - folosesc fallback")
                topic = create_fallback_topic(config)

        except asyncio.TimeoutError:
            messages_container.warning("⚠️ Timeout la generarea subiectului - folosesc fallback")
            topic = create_fallback_topic(config)

        except Exception as e:
            messages_container.warning(f"⚠️ Eroare la generarea subiectului: {str(e)} - folosesc fallback")
            topic = create_fallback_topic(config)

        # Ensure we always have a topic
        if not topic:
            messages_container.warning("⚠️ Fallback topic creation failed - creez topic de urgență")
            topic = create_emergency_fallback_topic(config)

        step_times["topic"] = time.time() - step_start
        timing_info.text(f"⏱️ Trending analysis: {step_times['topic']:.2f}s")

        # Display topic information
        intermediate_results.markdown(f"""
        ✅ **Subiect generat:**
        - **Titlu:** {topic.title}
        - **Potențial viral:** {topic.viral_potential:.1f}/10
        - **Relevanță România:** {topic.romanian_relevance:.1f}/10
        """)
        messages_container.success(f"✅ Subiect viral generat: {topic.title}")

        # Step 3: Generate viral script
        step_start = time.time()
        main_progress.progress(30)
        main_status.text("📝 Generez script viral optimizat...")
        step_status.markdown("**Pasul 3/8:** Creare script captivant cu hook puternic")

        intermediate_results.markdown("✍️ **Generez script** pentru subiectul viral...")

        script = None
        try:
            # Add timeout for script generation
            script = await asyncio.wait_for(
                viral_content_generator.generate_viral_script(
                    topic=topic,
                    duration=config.duration,
                    platform=config.platform,
                    target_audience=config.target_audience
                ),
                timeout=90.0  # 90 second timeout for script generation
            )

            # Check if script generation was successful
            if not script:
                messages_container.warning("⚠️ Generarea AI a returnat None - folosesc fallback")
                script = create_fallback_script(topic, config)

        except asyncio.TimeoutError:
            messages_container.warning("⚠️ Timeout la generarea scriptului - folosesc fallback")
            script = create_fallback_script(topic, config)
        except Exception as e:
            messages_container.warning(f"⚠️ Eroare la generarea scriptului: {str(e)} - folosesc fallback")
            script = create_fallback_script(topic, config)

        # Ensure we always have a script
        if not script:
            messages_container.warning("⚠️ Fallback script creation failed - creez script de urgență")
            script = create_fallback_script(topic, config)

        step_times["script"] = time.time() - step_start
        timing_info.text(f"⏱️ Script generation: {step_times['script']:.2f}s")

        # Display script information (script is guaranteed to exist at this point)
        script_preview = script.script_text[:150] + "..." if len(script.script_text) > 150 else script.script_text
        intermediate_results.markdown(f"""
        ✅ **Script generat:**
        - **Lungime:** {len(script.script_text)} caractere
        - **Durată estimată:** {script.estimated_duration}s
        - **Hook:** {script.hook}
        - **Preview:** {script_preview}
        """)
        messages_container.success(f"✅ Script generat ({len(script.script_text)} caractere)")

        # Step 4: Generate search terms
        step_start = time.time()
        main_progress.progress(45)
        main_status.text("🔍 Generez termeni de căutare optimizați...")
        step_status.markdown("**Pasul 4/8:** Optimizare termeni pentru platformele sociale")

        # Generate search terms using GPT4Free service
        from app.services.gpt4free_service import gpt4free_service

        try:
            search_terms = await asyncio.wait_for(
                gpt4free_service.generate_terms(
                    subject=topic.title,
                    count=8
                ),
                timeout=30.0  # 30 second timeout
            )

            # Fallback to topic keywords if generation fails
            if not search_terms:
                search_terms = topic.trending_keywords[:8]

        except asyncio.TimeoutError:
            messages_container.warning("⚠️ Timeout la generarea termenilor - folosesc keywords din topic")
            search_terms = topic.trending_keywords[:8]
        except Exception as e:
            messages_container.warning(f"⚠️ Eroare la generarea termenilor: {str(e)} - folosesc keywords din topic")
            search_terms = topic.trending_keywords[:8]

        step_times["search"] = time.time() - step_start
        timing_info.text(f"⏱️ Search terms: {step_times['search']:.2f}s")

        if search_terms:
            intermediate_results.markdown(f"""
            ✅ **Termeni de căutare:**
            {', '.join(search_terms[:5])}{'...' if len(search_terms) > 5 else ''}
            """)
            messages_container.success(f"✅ Generați {len(search_terms)} termeni de căutare")

        # Step 5: Generate contextual images (if enabled)
        contextual_image_plan = None
        if config.use_contextual_images:
            step_start = time.time()
            main_progress.progress(60)
            main_status.text("🖼️ Generez plan pentru imagini contextuale...")
            step_status.markdown("**Pasul 5/8:** Creare prompt-uri AI pentru imagini contextuale")

            intermediate_results.markdown("🎨 **Analizez script** pentru elemente vizuale...")

            try:
                contextual_image_plan = await asyncio.wait_for(
                    contextual_image_ai.generate_contextual_image_plan(
                        script=script.script_text,
                        duration=script.estimated_duration,
                        max_images=config.max_contextual_images,
                        style="viral_content"
                    ),
                    timeout=60.0  # 60 second timeout
                )
            except asyncio.TimeoutError:
                messages_container.warning("⚠️ Timeout la generarea planului de imagini - continuez fără imagini")
                contextual_image_plan = None
            except Exception as e:
                messages_container.warning(f"⚠️ Eroare la generarea planului de imagini: {str(e)} - continuez fără imagini")
                contextual_image_plan = None

            step_times["images"] = time.time() - step_start
            timing_info.text(f"⏱️ Image planning: {step_times['images']:.2f}s")

            if contextual_image_plan:
                intermediate_results.markdown(f"""
                ✅ **Plan imagini contextuale:**
                - **Numărul de imagini:** {len(contextual_image_plan.image_prompts)}
                - **Stil vizual:** {contextual_image_plan.visual_theme}
                - **Mood general:** {contextual_image_plan.overall_mood}
                """)
                messages_container.success(f"✅ Plan generat pentru {len(contextual_image_plan.image_prompts)} imagini")

        # Step 6: Generate title and description
        step_start = time.time()
        main_progress.progress(75)
        main_status.text("📰 Generez titlu și descriere optimizate...")
        step_status.markdown("**Pasul 6/8:** Optimizare titlu și descriere pentru platformă")

        # Generate title and description using available methods
        try:
            title, description = await generate_title_and_description_fallback(
                topic, script, config.platform
            )
        except Exception as e:
            title = topic.title
            description = topic.description
            messages_container.warning(f"⚠️ Folosesc titlu/descriere din topic: {str(e)}")

        step_times["title"] = time.time() - step_start
        timing_info.text(f"⏱️ Title & description: {step_times['title']:.2f}s")

        if title and description:
            intermediate_results.markdown(f"""
            ✅ **Titlu și descriere:**
            - **Titlu:** {title}
            - **Descriere:** {description[:100]}{'...' if len(description) > 100 else ''}
            """)
            messages_container.success("✅ Titlu și descriere generate")

        # Step 7: Generate hashtags
        step_start = time.time()
        main_progress.progress(85)
        main_status.text("🏷️ Generez hashtag-uri virale...")
        step_status.markdown("**Pasul 7/8:** Creare hashtag-uri optimizate pentru reach")

        # Generate hashtags using available methods
        try:
            hashtags = await generate_hashtags_fallback(
                topic, script, config.platform
            )
        except Exception as e:
            hashtags = [f"#{config.category}", f"#{config.platform}", "#viral", "#romania"]
            messages_container.warning(f"⚠️ Folosesc hashtag-uri default: {str(e)}")

        step_times["hashtags"] = time.time() - step_start
        timing_info.text(f"⏱️ Hashtags: {step_times['hashtags']:.2f}s")

        if hashtags:
            intermediate_results.markdown(f"""
            ✅ **Hashtag-uri generate:**
            {' '.join(hashtags[:8])}
            """)
            messages_container.success(f"✅ Generate {len(hashtags)} hashtag-uri")

        # Step 8: Finalize package
        step_start = time.time()
        main_progress.progress(95)
        main_status.text("📦 Finalizez pachetul viral...")
        step_status.markdown("**Pasul 8/8:** Asamblare finală și predicții performanță")

        # Create final package
        from app.services.viral_content_generator import ViralVideoPackage
        from app.services.one_click_viral_generator import ViralVideoResult

        # Extract contextual image prompts if available
        contextual_image_prompts = []
        thumbnail_prompt = ""

        if contextual_image_plan:
            contextual_image_prompts = [prompt.prompt for prompt in contextual_image_plan.image_prompts]
            thumbnail_prompt = contextual_image_plan.thumbnail_prompt

        package = ViralVideoPackage(
            topic=topic,
            script=script,
            title=title or topic.title,
            description=description or topic.description,
            hashtags=hashtags or [],
            search_terms=search_terms or [],
            contextual_image_prompts=contextual_image_prompts,
            thumbnail_prompt=thumbnail_prompt,
            seo_keywords=search_terms[:10] if search_terms else [],
            target_platforms=[config.platform]
        )

        result = ViralVideoResult(
            success=True,
            video_package=package,
            contextual_image_plan=contextual_image_plan,
            generation_time=time.time() - start_time,
            video_files=[],  # No actual video files generated yet
            performance_predictions={
                "estimated_views": "10K-50K",
                "estimated_engagement_rate": "5-8%",
                "viral_probability": "high",
                "best_posting_time": "18:00-21:00"
            }
        )

        step_times["finalize"] = time.time() - step_start
        timing_info.text(f"⏱️ Finalizare: {step_times['finalize']:.2f}s")

        # Final status
        main_progress.progress(100)
        main_status.text("✅ Generare completă!")
        step_status.markdown("**✅ COMPLET:** Pachet viral generat cu succes!")

        total_time = time.time() - start_time
        intermediate_results.markdown(f"""
        🎉 **Generare finalizată!**
        - **Timp total:** {total_time:.2f}s
        - **Toate componentele:** ✅ Generate cu succes
        - **Gata pentru producție:** 🚀
        """)

        messages_container.success(f"🎉 Pachet viral complet generat în {total_time:.2f} secunde!")

        return result

    except Exception as e:
        messages_container.error(f"❌ Eroare în procesul de generare: {str(e)}")
        step_status.markdown(f"**❌ EROARE:** {str(e)}")
        return None


def render_content_optimizer():
    """Renderează interfața pentru optimizatorul de conținut românesc"""
    
    st.markdown("## 🇷🇴 Optimizer Conținut Românesc")
    st.markdown("*Adaptează conținutul pentru audiența română*")
    
    if not VIRAL_SERVICES_AVAILABLE:
        st.error("❌ Serviciile de optimizare nu sunt disponibile")
        return
    
    # Input pentru conținut
    content_to_optimize = st.text_area(
        "📝 Conținut de optimizat:",
        placeholder="Introdu conținutul pe care vrei să-l optimizezi pentru audiența română...",
        height=150,
        help="Poate fi un script, titlu, descriere sau orice alt conținut"
    )
    
    col1, col2 = st.columns(2)
    with col1:
        content_type = st.selectbox(
            "📂 Tipul conținutului:",
            ["script", "title", "description", "hashtags", "general"],
            format_func=lambda x: {
                "script": "📝 Script videoclip",
                "title": "📰 Titlu",
                "description": "📄 Descriere",
                "hashtags": "🏷️ Hashtag-uri",
                "general": "📋 General"
            }[x]
        )
    
    with col2:
        global_trend = st.text_input(
            "🌍 Trend global (opțional):",
            placeholder="Ex: Morning productivity routine",
            help="Trend global pe care vrei să-l adaptezi pentru România"
        )
    
    if st.button("🇷🇴 Optimizează pentru România", type="primary"):
        if content_to_optimize or global_trend:
            with st.spinner("🤖 Optimizez conținutul pentru audiența română..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    if global_trend:
                        # Adaptează trend global
                        adapted_trend = loop.run_until_complete(
                            romanian_content_optimizer.adapt_global_trend_to_romanian(global_trend)
                        )
                        
                        if adapted_trend:
                            st.success("✅ Trend adaptat pentru România!")
                            st.markdown("#### 🌍➡️🇷🇴 Adaptare Trend Global")
                            
                            col1, col2 = st.columns(2)
                            with col1:
                                st.markdown("**Original (Global):**")
                                st.info(global_trend)
                            with col2:
                                st.markdown("**Adaptat (România):**")
                                st.success(adapted_trend)
                    
                    if content_to_optimize:
                        # Optimizează conținutul
                        optimization_result = loop.run_until_complete(
                            romanian_content_optimizer.optimize_content_for_romanian_audience(
                                content=content_to_optimize,
                                content_type=content_type
                            )
                        )
                        
                        st.success("✅ Conținut optimizat pentru România!")
                        
                        # Afișează rezultatele optimizării
                        display_optimization_results(optimization_result)
                    
                    loop.close()
                    
                except Exception as e:
                    st.error(f"❌ Eroare la optimizare: {e}")
        else:
            st.warning("⚠️ Te rog să introduci conținut de optimizat sau un trend global!")


def display_optimization_results(result):
    """Afișează rezultatele optimizării conținutului"""
    
    st.markdown("### 📊 Rezultate Optimizare")
    
    # Scorul de relevanță
    st.metric(
        "🇷🇴 Scor Relevanță România", 
        f"{result.romanian_relevance_score:.1f}/10",
        help="Cât de relevant este conținutul pentru audiența română"
    )
    
    # Comparație înainte/după
    col1, col2 = st.columns(2)
    with col1:
        st.markdown("#### 📝 Original")
        st.text_area("Conținut original:", value=result.original_content, height=150, disabled=True)
    
    with col2:
        st.markdown("#### ✨ Optimizat")
        st.text_area("Conținut optimizat:", value=result.optimized_content, height=150)
    
    # Detalii optimizări
    if result.cultural_adaptations:
        st.markdown("#### 🎭 Adaptări Culturale")
        for adaptation in result.cultural_adaptations:
            st.write(f"• {adaptation}")
    
    if result.local_references_added:
        st.markdown("#### 🇷🇴 Referințe Locale Adăugate")
        for reference in result.local_references_added:
            st.write(f"• {reference}")
    
    if result.language_improvements:
        st.markdown("#### 📝 Îmbunătățiri Limbaj")
        for improvement in result.language_improvements:
            st.write(f"• {improvement}")
    
    if result.engagement_boost_factors:
        st.markdown("#### 🚀 Factori de Boost Engagement")
        for factor in result.engagement_boost_factors:
            st.write(f"• {factor}")


def render_viral_automation_main():
    """Renderează interfața principală pentru automatizarea virală"""
    
    st.markdown("# 🤖 Automatizare Virală Completă")
    st.markdown("*Transformă MoneyPrinterTurbo într-o mașină de conținut viral*")
    
    if not VIRAL_SERVICES_AVAILABLE:
        st.error("❌ Serviciile de automatizare virală nu sunt disponibile")
        st.markdown("""
        ### 📥 Pentru a activa automatizarea virală:
        
        1. **Instalează GPT4Free:**
        ```bash
        pip install -U g4f[all]
        ```
        
        2. **Rulează scriptul de setup:**
        ```bash
        python install_gpt4free.py
        ```
        
        3. **Restartează aplicația**
        """)
        return
    
    # Tab-uri pentru diferite funcționalități
    tab1, tab2, tab3 = st.tabs([
        "🚀 One-Click Generator",
        "🇷🇴 Optimizer Românesc", 
        "📊 Analiză Performanță"
    ])
    
    with tab1:
        render_one_click_viral_generator()
    
    with tab2:
        render_content_optimizer()
    
    with tab3:
        render_performance_analyzer()


def render_performance_analyzer():
    """Renderează interfața pentru analizatorul de performanță"""
    
    st.markdown("## 📊 Analizator Performanță")
    st.markdown("*Analizează și îmbunătățește performanța videoclipurilor*")
    
    st.info("🚧 Funcționalitatea de analiză performanță va fi disponibilă în curând!")
    st.markdown("""
    ### 🔮 Funcționalități viitoare:
    - 📈 Analiză detaliată a performanței videoclipurilor
    - 🎯 Recomandări personalizate pentru îmbunătățire
    - 📊 Trend-uri și pattern-uri de succes
    - 🤖 Optimizare automată bazată pe feedback
    """)


# Funcție principală pentru export
def render_viral_automation_interface():
    """Funcția principală pentru interfața de automatizare virală"""
    render_viral_automation_main()
