#!/usr/bin/env python3
"""
Test AI Source Detection

This script tests if the AI source detection is working correctly
in the get_video_materials function.
"""

import sys
from pathlib import Path

# Add the project root to the path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

from app.services.task import get_video_materials
from app.models.schema import VideoParams, VideoAspect, VideoConcatMode


def test_ai_source_detection():
    """Test AI source detection in get_video_materials"""
    print("🔍 Testing AI Source Detection in get_video_materials...")
    print("=" * 60)
    
    # Test AI source
    print("\n1. Testing AI source ('ai'):")
    params_ai = VideoParams(
        video_subject="Test AI video",
        video_source="ai",  # This should trigger AI generation
        video_aspect=VideoAspect.portrait,
        video_concat_mode=VideoConcatMode.random,
        video_clip_duration=3.0,
        video_count=1,
        ai_style="realistic",
        ai_provider="perchance"
    )
    
    print(f"   - video_source: {params_ai.video_source}")
    print(f"   - ai_provider: {params_ai.ai_provider}")
    print(f"   - ai_style: {params_ai.ai_style}")
    
    # Check if it's detected as AI source
    is_ai_source = params_ai.video_source.startswith("ai")
    print(f"   - Detected as AI source: {is_ai_source}")
    
    if is_ai_source:
        print("   ✅ Should generate AI images instead of downloading videos")
    else:
        print("   ❌ Will download videos from Pexels/Pixabay")
    
    # Test non-AI source
    print("\n2. Testing non-AI source ('pexels'):")
    params_pexels = VideoParams(
        video_subject="Test Pexels video",
        video_source="pexels",  # This should download videos
        video_aspect=VideoAspect.portrait,
        video_concat_mode=VideoConcatMode.random,
        video_clip_duration=3.0,
        video_count=1
    )
    
    print(f"   - video_source: {params_pexels.video_source}")
    
    # Check if it's detected as AI source
    is_ai_source_pexels = params_pexels.video_source.startswith("ai")
    print(f"   - Detected as AI source: {is_ai_source_pexels}")
    
    if is_ai_source_pexels:
        print("   ❌ Should NOT be detected as AI source")
    else:
        print("   ✅ Will download videos from Pexels")
    
    # Test the actual function logic
    print("\n3. Testing function logic:")
    
    # Simulate the logic from get_video_materials
    def test_logic(params):
        if params.video_source == "local":
            return "LOCAL"
        elif params.video_source.startswith("ai"):
            return "AI_GENERATION"
        else:
            return "VIDEO_DOWNLOAD"
    
    result_ai = test_logic(params_ai)
    result_pexels = test_logic(params_pexels)
    
    print(f"   - AI source ('ai') -> {result_ai}")
    print(f"   - Pexels source ('pexels') -> {result_pexels}")
    
    # Test all AI sources
    print("\n4. Testing all AI sources:")
    ai_sources = [
        "ai",
        "ai_perchance", 
        "ai_openai",
        "ai_stable_diffusion",
        "ai_local_sd"
    ]
    
    for source in ai_sources:
        is_ai = source.startswith("ai")
        result = "AI_GENERATION" if is_ai else "VIDEO_DOWNLOAD"
        status = "✅" if is_ai else "❌"
        print(f"   {status} {source} -> {result}")
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    if result_ai == "AI_GENERATION" and result_pexels == "VIDEO_DOWNLOAD":
        print("🎉 AI source detection is working correctly!")
        print("✅ AI sources will generate images")
        print("✅ Non-AI sources will download videos")
        return True
    else:
        print("❌ AI source detection has issues")
        print(f"   - AI result: {result_ai} (should be AI_GENERATION)")
        print(f"   - Pexels result: {result_pexels} (should be VIDEO_DOWNLOAD)")
        return False


def test_current_config():
    """Test the current configuration from the web interface"""
    print("\n🔧 Testing Current Configuration...")
    print("=" * 40)
    
    try:
        from app.config import config
        
        current_source = config.app.get('video_source', 'NOT SET')
        current_provider = config.app.get('ai_provider', 'NOT SET')
        current_style = config.app.get('ai_style', 'NOT SET')
        
        print(f"Current video_source: {current_source}")
        print(f"Current ai_provider: {current_provider}")
        print(f"Current ai_style: {current_style}")
        
        is_ai_configured = current_source.startswith('ai')
        
        if is_ai_configured:
            print("✅ AI source is configured")
            print("🎯 Should generate AI images instead of downloading videos")
        else:
            print("⚠️ Non-AI source is configured")
            print("📹 Will download videos from external sources")
        
        return is_ai_configured
        
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 AI Source Detection Tests")
    print("=" * 60)
    
    # Test 1: Logic detection
    logic_test = test_ai_source_detection()
    
    # Test 2: Current config
    config_test = test_current_config()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS")
    print("=" * 60)
    
    if logic_test:
        print("✅ AI source detection logic is correct")
    else:
        print("❌ AI source detection logic has issues")
    
    if config_test:
        print("✅ Current configuration uses AI source")
        print("🎯 EXPECTED: AI image generation instead of video downloads")
    else:
        print("⚠️ Current configuration uses non-AI source")
        print("📹 EXPECTED: Video downloads from external sources")
    
    if logic_test and config_test:
        print("\n🎉 CONCLUSION: AI source detection should work!")
        print("💡 If videos are still being downloaded, there might be a cache issue.")
        print("🔄 Try restarting the web interface.")
    elif logic_test and not config_test:
        print("\n💡 CONCLUSION: Logic is correct, but AI source not selected.")
        print("🔧 Select an AI source in the web interface.")
    else:
        print("\n❌ CONCLUSION: There are issues with the AI source detection.")
    
    return logic_test and config_test


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
