#!/usr/bin/env python3
"""
Viral Content Generator Service

Acest serviciu folosește GPT4Free pentru generarea automată de conținut viral
optimizat pentru platformele sociale și audiența română.
"""

import asyncio
import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from loguru import logger

try:
    from app.services.gpt4free_service import gpt4free_service
    GPT4FREE_AVAILABLE = True
except ImportError:
    GPT4FREE_AVAILABLE = False
    logger.warning("GPT4Free service not available for viral content generation")


@dataclass
class ViralTopic:
    """Reprezentarea unui subiect viral"""
    title: str
    description: str
    category: str
    viral_potential: float  # 0.0 - 1.0
    target_audience: str
    trending_keywords: List[str]
    romanian_relevance: float  # 0.0 - 1.0
    estimated_engagement: str
    content_type: str  # "educational", "entertainment", "motivational", etc.
    
    
@dataclass
class ViralScript:
    """Reprezentarea unui script viral"""
    topic: ViralTopic
    script_text: str
    hook: str  # Primele 3-5 secunde captivante
    main_content: str
    call_to_action: str
    estimated_duration: int  # în secunde
    engagement_elements: List[str]
    romanian_cultural_refs: List[str]
    

@dataclass
class ViralVideoPackage:
    """Pachet complet pentru un videoclip viral"""
    topic: ViralTopic
    script: ViralScript
    title: str
    description: str
    hashtags: List[str]
    search_terms: List[str]
    contextual_image_prompts: List[str]
    thumbnail_prompt: str
    seo_keywords: List[str]
    target_platforms: List[str]


class ViralContentGenerator:
    """Generator principal pentru conținut viral"""
    
    def __init__(self):
        self.gpt4free_available = GPT4FREE_AVAILABLE and gpt4free_service.is_available()
        
        # Categorii de conținut viral pentru România
        self.viral_categories = {
            "lifestyle_romania": "Lifestyle și cultura românească",
            "travel_romania": "Călătorii și locuri din România", 
            "food_romania": "Mâncare și rețete românești",
            "history_romania": "Istorie și tradiții românești",
            "tech_trends": "Tehnologie și inovații",
            "motivation": "Motivație și dezvoltare personală",
            "entertainment": "Divertisment și umor",
            "education": "Educație și cunoștințe generale",
            "current_events": "Evenimente actuale și trending topics",
            "social_issues": "Probleme sociale și awareness"
        }
        
        # Trending topics românești (actualizate periodic)
        self.romanian_trending_topics = [
            "România digitală", "Startup-uri românești", "Tradiții românești moderne",
            "Influenceri români", "Tehnologie în România", "Turism intern",
            "Mâncare tradițională", "Muzică românească", "Film românesc",
            "Educație în România", "Mediu și ecologie", "Sport românesc"
        ]
        
        # Platforme sociale target
        self.target_platforms = {
            "tiktok": {"max_duration": 60, "style": "quick_engaging", "hashtags_count": 5},
            "instagram_reels": {"max_duration": 90, "style": "aesthetic_engaging", "hashtags_count": 10},
            "youtube_shorts": {"max_duration": 60, "style": "informative_engaging", "hashtags_count": 3},
            "facebook": {"max_duration": 120, "style": "story_driven", "hashtags_count": 5}
        }
    
    def is_available(self) -> bool:
        """Verifică dacă generatorul este disponibil"""
        return self.gpt4free_available
    
    async def analyze_trending_topics(self, region: str = "romania") -> List[Dict[str, Any]]:
        """Analizează trending topics și identifică oportunități virale"""
        
        if not self.is_available():
            logger.warning("Viral content generator not available")
            return []
        
        prompt = f"""
        Analizează trending topics actuale pentru regiunea {region} și identifică 10 subiecte 
        cu potențial viral pentru videoclipuri scurte pe social media.
        
        Pentru fiecare subiect, evaluează:
        - Potențialul viral (1-10)
        - Relevanța pentru audiența română
        - Tipul de conținut recomandat
        - Keywords trending asociate
        
        Concentrează-te pe subiecte care:
        - Sunt relevante pentru cultura română
        - Au potențial de engagement ridicat
        - Pot fi adaptate pentru videoclipuri de 30-60 secunde
        - Rezonează cu audiența tânără (18-35 ani)
        
        Returnează rezultatele în format JSON cu următoarea structură:
        {{
            "trending_topics": [
                {{
                    "title": "Titlul subiectului",
                    "description": "Descriere detaliată",
                    "viral_potential": 8.5,
                    "romanian_relevance": 9.0,
                    "category": "lifestyle_romania",
                    "keywords": ["keyword1", "keyword2"],
                    "target_audience": "tineri urbani 18-30",
                    "content_type": "educational",
                    "estimated_engagement": "high"
                }}
            ]
        }}
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o",
                max_tokens=2000,
                temperature=0.7
            )
            
            if response:
                # Extrage JSON din răspuns
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group())
                    logger.info(f"✅ Analizate {len(data.get('trending_topics', []))} trending topics")
                    return data.get('trending_topics', [])
            
            logger.warning("Nu s-au putut extrage trending topics din răspuns")
            return []
            
        except Exception as e:
            logger.error(f"❌ Eroare la analiza trending topics: {e}")
            return []
    
    async def generate_viral_topic(self, 
                                 category: str = None,
                                 target_audience: str = "tineri români 18-35",
                                 platform: str = "tiktok") -> Optional[ViralTopic]:
        """Generează un subiect viral specific"""
        
        if not self.is_available():
            return None
        
        category = category or "lifestyle_romania"
        platform_config = self.target_platforms.get(platform, self.target_platforms["tiktok"])
        
        prompt = f"""
        Generează un subiect viral captivant pentru categoria "{category}" 
        optimizat pentru platforma {platform}.
        
        Criterii:
        - Audiența țintă: {target_audience}
        - Durată maximă video: {platform_config['max_duration']} secunde
        - Stil: {platform_config['style']}
        - Trebuie să fie relevant pentru România și cultura română
        - Să aibă potențial viral ridicat
        - Să fie ușor de înțeles și captivant
        
        Returnează în format JSON:
        {{
            "title": "Titlu captivant și scurt",
            "description": "Descriere detaliată a subiectului",
            "category": "{category}",
            "viral_potential": 8.5,
            "target_audience": "{target_audience}",
            "trending_keywords": ["keyword1", "keyword2", "keyword3"],
            "romanian_relevance": 9.0,
            "estimated_engagement": "high/medium/low",
            "content_type": "educational/entertainment/motivational"
        }}
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o",
                max_tokens=800,
                temperature=0.8
            )
            
            if response:
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group())
                    
                    topic = ViralTopic(
                        title=data.get('title', ''),
                        description=data.get('description', ''),
                        category=data.get('category', category),
                        viral_potential=data.get('viral_potential', 7.0),
                        target_audience=data.get('target_audience', target_audience),
                        trending_keywords=data.get('trending_keywords', []),
                        romanian_relevance=data.get('romanian_relevance', 8.0),
                        estimated_engagement=data.get('estimated_engagement', 'medium'),
                        content_type=data.get('content_type', 'entertainment')
                    )
                    
                    logger.info(f"✅ Generat subiect viral: {topic.title}")
                    return topic
            
            logger.warning("Nu s-a putut genera subiect viral")
            return None
            
        except Exception as e:
            logger.error(f"❌ Eroare la generarea subiectului viral: {e}")
            return None
    
    async def generate_viral_script(self, 
                                  topic: ViralTopic,
                                  duration: int = 60,
                                  platform: str = "tiktok") -> Optional[ViralScript]:
        """Generează un script viral optimizat"""
        
        if not self.is_available():
            return None
        
        platform_config = self.target_platforms.get(platform, self.target_platforms["tiktok"])
        
        prompt = f"""
        Creează un script viral captivant pentru subiectul: "{topic.title}"
        
        Detalii subiect:
        - Descriere: {topic.description}
        - Categorie: {topic.category}
        - Audiența țintă: {topic.target_audience}
        - Platformă: {platform}
        - Durată: {duration} secunde
        - Stil: {platform_config['style']}
        
        Cerințe pentru script:
        1. HOOK puternic (primele 3-5 secunde) - trebuie să capteze atenția instant
        2. Conținut principal structurat și captivant
        3. Call-to-action natural și engaging
        4. Limbaj conversațional și autentic românesc
        5. Elemente de engagement (întrebări, suspans, revelații)
        6. Referințe culturale românești relevante
        
        Returnează în format JSON:
        {{
            "script_text": "Scriptul complet pentru voiceover",
            "hook": "Primele 3-5 secunde captivante",
            "main_content": "Conținutul principal",
            "call_to_action": "Call-to-action final",
            "estimated_duration": {duration},
            "engagement_elements": ["element1", "element2"],
            "romanian_cultural_refs": ["referinta1", "referinta2"]
        }}
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o",
                max_tokens=1500,
                temperature=0.8
            )
            
            if response:
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group())
                    
                    script = ViralScript(
                        topic=topic,
                        script_text=data.get('script_text', ''),
                        hook=data.get('hook', ''),
                        main_content=data.get('main_content', ''),
                        call_to_action=data.get('call_to_action', ''),
                        estimated_duration=data.get('estimated_duration', duration),
                        engagement_elements=data.get('engagement_elements', []),
                        romanian_cultural_refs=data.get('romanian_cultural_refs', [])
                    )
                    
                    logger.info(f"✅ Generat script viral pentru: {topic.title}")
                    return script
            
            logger.warning("Nu s-a putut genera script viral")
            return None
            
        except Exception as e:
            logger.error(f"❌ Eroare la generarea scriptului viral: {e}")
            return None


# Global service instance
viral_content_generator = ViralContentGenerator()


async def test_viral_content_generator():
    """Test pentru generatorul de conținut viral"""
    print("🧪 Testing Viral Content Generator...")
    
    if not viral_content_generator.is_available():
        print("❌ Viral content generator not available")
        return False
    
    # Test 1: Generare subiect viral
    print("🎯 Testing viral topic generation...")
    topic = await viral_content_generator.generate_viral_topic(
        category="lifestyle_romania",
        target_audience="tineri români 18-30",
        platform="tiktok"
    )
    
    if topic:
        print(f"✅ Generated viral topic: {topic.title}")
        print(f"📊 Viral potential: {topic.viral_potential}")
        print(f"🇷🇴 Romanian relevance: {topic.romanian_relevance}")
        
        # Test 2: Generare script viral
        print("📝 Testing viral script generation...")
        script = await viral_content_generator.generate_viral_script(
            topic=topic,
            duration=60,
            platform="tiktok"
        )
        
        if script:
            print(f"✅ Generated viral script!")
            print(f"🎣 Hook: {script.hook[:50]}...")
            print(f"📱 Call to action: {script.call_to_action}")
            return True
        else:
            print("❌ Script generation failed")
            return False
    else:
        print("❌ Topic generation failed")
        return False


if __name__ == "__main__":
    asyncio.run(test_viral_content_generator())
