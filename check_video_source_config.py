#!/usr/bin/env python3
"""
Video Source Configuration Checker

This script helps diagnose why the system is still downloading videos
instead of generating AI images.
"""

import sys
from pathlib import Path

# Add the project root to the path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

from app.config import config


def check_video_source_configuration():
    """Check the current video source configuration"""
    print("🔍 Checking Video Source Configuration...")
    print("=" * 50)
    
    # Load current configuration
    try:
        current_config = config.app
        
        print("📋 Current Configuration:")
        print(f"  - Video Source: {current_config.get('video_source', 'NOT SET')}")
        print(f"  - AI Style: {current_config.get('ai_style', 'NOT SET')}")
        print(f"  - AI Provider: {current_config.get('ai_provider', 'NOT SET')}")
        
        # Check if video source is AI
        video_source = current_config.get('video_source', '')
        
        print(f"\n🎯 Analysis:")
        if video_source.startswith('ai'):
            print(f"  ✅ AI Source Detected: {video_source}")
            print(f"  ✅ Should generate AI images instead of downloading videos")
        elif video_source in ['pexels', 'pixabay']:
            print(f"  ⚠️ Stock Video Source: {video_source}")
            print(f"  ⚠️ Will download videos from {video_source}")
            print(f"  💡 To use AI images, select an AI source in the web interface")
        elif video_source == 'local':
            print(f"  📁 Local Source: {video_source}")
            print(f"  📁 Will use uploaded local files")
        else:
            print(f"  ❓ Unknown Source: {video_source}")
        
        # Show available AI sources
        print(f"\n🤖 Available AI Sources:")
        ai_sources = [
            ("🤖 AI Generated Images", "ai"),
            ("🚀 Local Stable Diffusion", "ai_local_sd"),
            ("🎨 Perchance AI", "ai_perchance"),
            ("🖼️ OpenAI DALL-E", "ai_openai"),
            ("🎯 Stable Diffusion", "ai_stable_diffusion"),
        ]
        
        for name, value in ai_sources:
            status = "✅ SELECTED" if video_source == value else "⚪ Available"
            print(f"  {status} {name} ({value})")
        
        # Show stock video sources
        print(f"\n📹 Stock Video Sources:")
        stock_sources = [
            ("Pexels", "pexels"),
            ("Pixabay", "pixabay"),
            ("Local file", "local"),
        ]
        
        for name, value in stock_sources:
            status = "✅ SELECTED" if video_source == value else "⚪ Available"
            print(f"  {status} {name} ({value})")
        
        return video_source
        
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")
        return None


def provide_solution(video_source):
    """Provide solution based on current configuration"""
    print(f"\n" + "=" * 50)
    print("💡 SOLUTION")
    print("=" * 50)
    
    if not video_source:
        print("❌ Could not determine video source configuration")
        print("🔧 Please check your config.toml file")
        return
    
    if video_source.startswith('ai'):
        print("✅ AI source is correctly configured!")
        print("🎯 The system should generate AI images instead of downloading videos")
        print("📝 If you're still seeing Pexels downloads, there might be a caching issue")
        print("\n🔄 Try:")
        print("  1. Restart the web interface")
        print("  2. Clear browser cache")
        print("  3. Generate a new video")
        
    elif video_source in ['pexels', 'pixabay']:
        print(f"⚠️ Currently using stock video source: {video_source}")
        print("🎯 To use AI-generated images instead:")
        print("\n📝 Steps:")
        print("  1. Open the web interface: http://localhost:8502")
        print("  2. Go to 'Video Settings' section")
        print("  3. Change 'Video Source' from current selection to:")
        print("     - '🤖 AI Generated Images' (general AI)")
        print("     - '🎨 Perchance AI' (free, recommended)")
        print("     - '🖼️ OpenAI DALL-E' (paid, high quality)")
        print("     - '🎯 Stable Diffusion' (local/API)")
        print("  4. Configure AI settings (style, provider)")
        print("  5. Generate video")
        
        print(f"\n✅ After changing to AI source:")
        print("  - No more Pexels/Pixabay video downloads")
        print("  - AI images generated based on your script")
        print("  - Contextual images synchronized with audio")
        
    else:
        print(f"❓ Unknown video source: {video_source}")
        print("🔧 Please select a valid video source in the web interface")


def main():
    """Main function"""
    print("🚀 Video Source Configuration Checker")
    print("=" * 60)
    
    video_source = check_video_source_configuration()
    provide_solution(video_source)
    
    print(f"\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    if video_source and video_source.startswith('ai'):
        print("🎉 AI source is configured correctly!")
        print("🎯 You should see AI image generation instead of video downloads")
    else:
        print("⚠️ Stock video source is currently selected")
        print("💡 Change to an AI source to generate AI images instead")
    
    print(f"\n🔗 Web Interface: http://localhost:8502")
    print(f"📁 Config File: {Path('config.toml').absolute()}")


if __name__ == "__main__":
    main()
