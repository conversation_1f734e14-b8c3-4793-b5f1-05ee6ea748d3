#!/usr/bin/env python3
"""
AI Provider Diagnostic Script

This script diagnoses AI provider availability and tests the fallback system.
Use this to troubleshoot any AI image generation issues.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to the path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))


async def test_ai_providers():
    """Test all AI providers and report their status"""
    print("🔍 Diagnosing AI Provider Availability...")
    print("=" * 50)
    
    try:
        from app.services.ai_video_source_manager import AIVideoSourceManager, AIImageConfig, AIProvider
        
        manager = AIVideoSourceManager()
        
        # Initialize providers
        manager._initialize_providers()
        
        print(f"📊 Available providers: {list(manager.providers.keys())}")
        
        # Test each provider
        test_prompt = "beautiful landscape"
        config = AIImageConfig()
        config.width = 512
        config.height = 512
        
        for provider_name, provider in manager.providers.items():
            print(f"\n🧪 Testing {provider_name}...")
            
            try:
                # Test the provider directly
                if hasattr(provider, 'generate_image'):
                    result = await provider.generate_image(test_prompt, 512, 512)
                    if result:
                        print(f"  ✅ {provider_name}: Working ({len(result)} bytes)")
                    else:
                        print(f"  ❌ {provider_name}: No data returned")
                else:
                    print(f"  ⚠️ {provider_name}: No generate_image method")
                    
            except Exception as e:
                print(f"  ❌ {provider_name}: Error - {e}")
        
        # Test the main generate_image method
        print(f"\n🎯 Testing main AI manager...")
        try:
            result = await manager.generate_image(test_prompt, config)
            if result.success:
                print(f"  ✅ AI Manager: Success with {result.provider_used} ({len(result.image_data)} bytes)")
            else:
                print(f"  ❌ AI Manager: Failed - {result.error_message}")
        except Exception as e:
            print(f"  ❌ AI Manager: Exception - {e}")
        
        await manager.cleanup()
        
    except Exception as e:
        print(f"❌ Failed to test AI providers: {e}")
        import traceback
        traceback.print_exc()


async def test_fallback_system():
    """Test the fallback image generation system"""
    print("\n🎨 Testing Fallback System...")
    print("=" * 30)
    
    try:
        from app.services.fallback_image_generator import FallbackImageGenerator
        
        generator = FallbackImageGenerator()
        
        test_cases = [
            ("simple test", "abstract", "neutral"),
            ("medieval battle scene", "battle", "dramatic"),
            ("peaceful landscape", "landscape", "peaceful")
        ]
        
        for prompt, scene_type, emotional_tone in test_cases:
            print(f"\n🖼️ Testing: {prompt}")
            
            try:
                image_data = await generator.generate_contextual_image(
                    prompt, 512, 512, scene_type, emotional_tone
                )
                
                if image_data:
                    print(f"  ✅ Generated: {len(image_data)} bytes")
                else:
                    print(f"  ❌ No data returned")
                    
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        await generator.cleanup()
        
    except Exception as e:
        print(f"❌ Failed to test fallback system: {e}")
        import traceback
        traceback.print_exc()


def check_dependencies():
    """Check if required dependencies are available"""
    print("\n📦 Checking Dependencies...")
    print("=" * 25)
    
    dependencies = [
        ("PIL", "Pillow"),
        ("aiohttp", "aiohttp"),
        ("requests", "requests"),
        ("asyncio", "asyncio (built-in)"),
    ]
    
    for module_name, package_name in dependencies:
        try:
            __import__(module_name)
            print(f"  ✅ {package_name}: Available")
        except ImportError:
            print(f"  ❌ {package_name}: Missing")


def check_network_connectivity():
    """Check network connectivity to AI services"""
    print("\n🌐 Checking Network Connectivity...")
    print("=" * 35)
    
    import requests
    
    endpoints = [
        ("Perchance", "https://perchance.org"),
        ("OpenAI", "https://api.openai.com"),
        ("Google", "https://www.google.com")  # General connectivity test
    ]
    
    for service, url in endpoints:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"  ✅ {service}: Reachable")
            else:
                print(f"  ⚠️ {service}: Status {response.status_code}")
        except Exception as e:
            print(f"  ❌ {service}: {e}")


async def main():
    """Run all diagnostic tests"""
    print("🚀 AI Provider Diagnostic Tool")
    print("=" * 60)
    
    # Check dependencies
    check_dependencies()
    
    # Check network connectivity
    check_network_connectivity()
    
    # Test AI providers
    await test_ai_providers()
    
    # Test fallback system
    await test_fallback_system()
    
    print("\n" + "=" * 60)
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    print("\n🔧 TROUBLESHOOTING TIPS:")
    print("1. If AI providers fail but fallback works: Network/API issue")
    print("2. If fallback fails: Check PIL/Pillow installation")
    print("3. If all fail: Check Python environment and dependencies")
    print("4. For preview issues: Try the '🎨 Preview Fallback' button in the web UI")
    
    print("\n✅ SOLUTION:")
    print("- The fallback system ensures images are always generated")
    print("- Even if AI providers fail, contextual images will be created")
    print("- Use 'Preview Fallback' button to verify the system works")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Open the web interface (http://localhost:8502)")
    print("2. Go to AI Image Generation section")
    print("3. Click '🎨 Preview Fallback' to test")
    print("4. Enable contextual images and generate videos")


if __name__ == "__main__":
    asyncio.run(main())
