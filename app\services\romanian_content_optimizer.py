#!/usr/bin/env python3
"""
Romanian Content Optimizer

Ace<PERSON> servic<PERSON> optimizează conținutul pentru audiența română, adaptând
trending topics globale la cultura și interesele locale.
"""

import asyncio
import json
import re
from datetime import datetime
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from loguru import logger

try:
    from app.services.gpt4free_service import gpt4free_service
    GPT4FREE_AVAILABLE = True
except ImportError:
    GPT4FREE_AVAILABLE = False
    logger.warning("GPT4Free service not available for Romanian content optimization")


@dataclass
class RomanianCulturalContext:
    """Context cultural românesc pentru optimizarea conținutului"""
    cultural_references: List[str]
    local_trends: List[str]
    regional_interests: List[str]
    language_nuances: List[str]
    humor_style: str
    values_alignment: List[str]
    seasonal_relevance: List[str]


@dataclass
class ContentOptimizationResult:
    """Rezultatul optimizării conținutului pentru audiența română"""
    original_content: str
    optimized_content: str
    cultural_adaptations: List[str]
    local_references_added: List[str]
    language_improvements: List[str]
    engagement_boost_factors: List[str]
    romanian_relevance_score: float


class RomanianContentOptimizer:
    """Optimizer pentru conținut românesc viral"""
    
    def __init__(self):
        self.gpt4free_available = GPT4FREE_AVAILABLE and gpt4free_service.is_available()
        
        # Baza de cunoștințe culturale românești
        self.cultural_knowledge = {
            "celebrities": [
                "Smiley", "Delia", "Inna", "Alexandra Stan", "Antonia",
                "Florin Salam", "Gigi Becali", "Dan Bittman", "Loredana Groza"
            ],
            "places": [
                "Brașov", "Cluj-Napoca", "Constanța", "Timișoara", "Iași",
                "Transfăgărășan", "Castelul Bran", "Deltă Dunării", "Carpați"
            ],
            "food": [
                "mici", "mămăligă", "sarmale", "papanași", "cozonac",
                "ciorbă de burtă", "salată de icre", "șnițel", "gogoși"
            ],
            "traditions": [
                "Mărțișor", "Paște", "Crăciun", "Dragobete", "Sânzienele",
                "Hora", "Căluș", "Colinde", "Plugușorul"
            ],
            "expressions": [
                "Să trăiți!", "Noroc!", "Sănătate!", "La mulți ani!",
                "Ce faci, măi?", "Hai noroc!", "Să fie într-un ceas bun!"
            ],
            "humor_style": [
                "ironie fină", "umor autoironic", "glume cu dublu înțeles",
                "referințe la viața de zi cu zi", "umor despre politică"
            ],
            "current_trends": [
                "TikTok România", "influenceri români", "startup-uri românești",
                "digitalizare", "muncă de acasă", "livrări online"
            ]
        }
        
        # Interese regionale
        self.regional_interests = {
            "bucuresti": ["viața urbană", "trafic", "metrou", "mall-uri", "cluburi"],
            "transilvania": ["castele", "natură", "tradiții", "turism montan"],
            "moldova": ["vinuri", "mănăstiri", "tradiții", "gastronomie"],
            "oltenia": ["folclor", "Dunărea", "tradiții", "agricultura"],
            "dobrogea": ["mare", "pescuit", "multiculturalitate", "turism"],
            "banat": ["multiculturalitate", "arhitectură", "gastronomie", "muzică"]
        }
    
    def is_available(self) -> bool:
        """Verifică dacă optimizatorul este disponibil"""
        return self.gpt4free_available
    
    async def analyze_cultural_context(self, content: str) -> RomanianCulturalContext:
        """Analizează contextul cultural al conținutului"""
        
        if not self.is_available():
            return RomanianCulturalContext([], [], [], [], "general", [], [])
        
        prompt = f"""
        Analizează următorul conținut și identifică contextul cultural românesc:
        
        CONȚINUT:
        {content}
        
        Identifică și returnează în format JSON:
        1. Referințe culturale românești existente
        2. Trend-uri locale relevante
        3. Interese regionale aplicabile
        4. Nuanțe de limbă românească
        5. Stilul de umor potrivit
        6. Alinierea cu valorile românești
        7. Relevanța sezonieră
        
        Format JSON:
        {{
            "cultural_references": ["referință1", "referință2"],
            "local_trends": ["trend1", "trend2"],
            "regional_interests": ["interes1", "interes2"],
            "language_nuances": ["nuanță1", "nuanță2"],
            "humor_style": "ironic/autoironic/direct/subtil",
            "values_alignment": ["valoare1", "valoare2"],
            "seasonal_relevance": ["aspect1", "aspect2"]
        }}
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o",
                max_tokens=1000,
                temperature=0.7
            )
            
            if response:
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group())
                    
                    return RomanianCulturalContext(
                        cultural_references=data.get('cultural_references', []),
                        local_trends=data.get('local_trends', []),
                        regional_interests=data.get('regional_interests', []),
                        language_nuances=data.get('language_nuances', []),
                        humor_style=data.get('humor_style', 'general'),
                        values_alignment=data.get('values_alignment', []),
                        seasonal_relevance=data.get('seasonal_relevance', [])
                    )
            
            logger.warning("Nu s-a putut analiza contextul cultural")
            return RomanianCulturalContext([], [], [], [], "general", [], [])
            
        except Exception as e:
            logger.error(f"❌ Eroare la analiza contextului cultural: {e}")
            return RomanianCulturalContext([], [], [], [], "general", [], [])
    
    async def adapt_global_trend_to_romanian(self, 
                                           global_trend: str,
                                           target_audience: str = "tineri români 18-35") -> Optional[str]:
        """Adaptează un trend global pentru audiența română"""
        
        if not self.is_available():
            return None
        
        # Selectează cunoștințe culturale relevante
        cultural_refs = ", ".join(self.cultural_knowledge["celebrities"][:5])
        places = ", ".join(self.cultural_knowledge["places"][:5])
        food = ", ".join(self.cultural_knowledge["food"][:5])
        expressions = ", ".join(self.cultural_knowledge["expressions"][:3])
        
        prompt = f"""
        Adaptează următorul trend global pentru audiența română:
        
        TREND GLOBAL: {global_trend}
        AUDIENȚA ȚINTĂ: {target_audience}
        
        Folosește următoarele elemente culturale românești:
        - Celebrități: {cultural_refs}
        - Locuri: {places}
        - Mâncare: {food}
        - Expresii: {expressions}
        
        Adaptarea trebuie să:
        1. Păstreze esența trend-ului global
        2. Adauge elemente culturale românești relevante
        3. Folosească limbaj familiar pentru audiența țintă
        4. Fie autentică și naturală
        5. Aibă potențial viral în România
        
        Returnează doar versiunea adaptată, fără explicații.
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o",
                max_tokens=500,
                temperature=0.8
            )
            
            if response:
                adapted_trend = response.strip()
                logger.info(f"✅ Trend adaptat pentru România: {adapted_trend[:50]}...")
                return adapted_trend
            
            logger.warning("Nu s-a putut adapta trend-ul global")
            return None
            
        except Exception as e:
            logger.error(f"❌ Eroare la adaptarea trend-ului global: {e}")
            return None
    
    async def optimize_content_for_romanian_audience(self, 
                                                   content: str,
                                                   content_type: str = "script") -> ContentOptimizationResult:
        """Optimizează conținutul pentru audiența română"""
        
        if not self.is_available():
            return ContentOptimizationResult(
                original_content=content,
                optimized_content=content,
                cultural_adaptations=[],
                local_references_added=[],
                language_improvements=[],
                engagement_boost_factors=[],
                romanian_relevance_score=5.0
            )
        
        # Analizează contextul cultural
        cultural_context = await self.analyze_cultural_context(content)
        
        prompt = f"""
        Optimizează următorul conținut pentru audiența română:
        
        CONȚINUT ORIGINAL:
        {content}
        
        TIPUL CONȚINUTULUI: {content_type}
        
        CONTEXT CULTURAL IDENTIFICAT:
        - Referințe culturale: {cultural_context.cultural_references}
        - Stil umor: {cultural_context.humor_style}
        - Trend-uri locale: {cultural_context.local_trends}
        
        OPTIMIZĂRI NECESARE:
        1. Adaugă referințe culturale românești naturale
        2. Îmbunătățește limbajul pentru a fi mai autentic românesc
        3. Include expresii și termeni familiare
        4. Adaptează umorul pentru gustul românesc
        5. Adaugă elemente care cresc engagement-ul local
        
        Returnează în format JSON:
        {{
            "optimized_content": "conținutul optimizat",
            "cultural_adaptations": ["adaptare1", "adaptare2"],
            "local_references_added": ["referință1", "referință2"],
            "language_improvements": ["îmbunătățire1", "îmbunătățire2"],
            "engagement_boost_factors": ["factor1", "factor2"],
            "romanian_relevance_score": 8.5
        }}
        """
        
        try:
            response = await gpt4free_service.generate_text(
                prompt=prompt,
                model="gpt-4o",
                max_tokens=1500,
                temperature=0.8
            )
            
            if response:
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group())
                    
                    result = ContentOptimizationResult(
                        original_content=content,
                        optimized_content=data.get('optimized_content', content),
                        cultural_adaptations=data.get('cultural_adaptations', []),
                        local_references_added=data.get('local_references_added', []),
                        language_improvements=data.get('language_improvements', []),
                        engagement_boost_factors=data.get('engagement_boost_factors', []),
                        romanian_relevance_score=data.get('romanian_relevance_score', 7.0)
                    )
                    
                    logger.info(f"✅ Conținut optimizat pentru România (scor: {result.romanian_relevance_score})")
                    return result
            
            logger.warning("Nu s-a putut optimiza conținutul")
            return ContentOptimizationResult(
                original_content=content,
                optimized_content=content,
                cultural_adaptations=[],
                local_references_added=[],
                language_improvements=[],
                engagement_boost_factors=[],
                romanian_relevance_score=5.0
            )
            
        except Exception as e:
            logger.error(f"❌ Eroare la optimizarea conținutului: {e}")
            return ContentOptimizationResult(
                original_content=content,
                optimized_content=content,
                cultural_adaptations=[],
                local_references_added=[],
                language_improvements=[],
                engagement_boost_factors=[],
                romanian_relevance_score=5.0
            )


# Global service instance
romanian_content_optimizer = RomanianContentOptimizer()


async def test_romanian_content_optimizer():
    """Test pentru optimizatorul de conținut românesc"""
    print("🧪 Testing Romanian Content Optimizer...")
    
    if not romanian_content_optimizer.is_available():
        print("❌ Romanian Content Optimizer not available")
        return False
    
    # Test 1: Adaptarea unui trend global
    print("🌍 Testing global trend adaptation...")
    global_trend = "Quick morning routine for productivity"
    adapted_trend = await romanian_content_optimizer.adapt_global_trend_to_romanian(global_trend)
    
    if adapted_trend:
        print(f"✅ Global trend adapted: {adapted_trend[:100]}...")
        
        # Test 2: Optimizarea conținutului
        print("🇷🇴 Testing content optimization...")
        test_content = "Astăzi îți arăt cum să fii productiv dimineața cu o rutină simplă de 10 minute."
        
        optimization_result = await romanian_content_optimizer.optimize_content_for_romanian_audience(
            content=test_content,
            content_type="script"
        )
        
        print(f"✅ Content optimized!")
        print(f"📊 Romanian relevance score: {optimization_result.romanian_relevance_score}")
        print(f"🎯 Cultural adaptations: {len(optimization_result.cultural_adaptations)}")
        print(f"🇷🇴 Local references added: {len(optimization_result.local_references_added)}")
        
        return True
    else:
        print("❌ Global trend adaptation failed")
        return False


if __name__ == "__main__":
    asyncio.run(test_romanian_content_optimizer())
