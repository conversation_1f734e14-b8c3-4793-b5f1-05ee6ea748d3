"""
Modul pentru configurarea hardware-ului în MoviePy și alte biblioteci de procesare video.
"""

import os
import sys
from typing import Dict, Optional, Any
from loguru import logger

from .hardware_detector import HardwareInfo, get_hardware_detector


class HardwareConfigurator:
    """Configurează hardware-ul pentru procesarea video."""
    
    def __init__(self):
        self.current_config: Optional[HardwareInfo] = None
        self.moviepy_config: Dict[str, Any] = {}
        
    def configure_hardware(self, hardware: HardwareInfo) -> bool:
        """
        Configurează hardware-ul selectat pentru procesarea video.
        
        Args:
            hardware: Informațiile hardware-ului de configurat
            
        Returns:
            bool: True dacă configurarea a reușit
        """
        try:
            logger.info(f"🔧 Configurare hardware: {hardware.name}")
            
            # Resetează configurația anterioară
            self._reset_config()
            
            # Configurează în funcție de tipul hardware-ului
            if hardware.type == "nvidia":
                success = self._configure_nvidia(hardware)
            elif hardware.type == "amd":
                success = self._configure_amd(hardware)
            elif hardware.type == "intel":
                success = self._configure_intel(hardware)
            elif hardware.type == "cpu":
                success = self._configure_cpu(hardware)
            else:
                logger.warning(f"⚠️ Tip hardware necunoscut: {hardware.type}")
                return False
            
            if success:
                self.current_config = hardware
                logger.info(f"✅ Hardware configurat cu succes: {hardware.name}")
                return True
            else:
                logger.error(f"❌ Configurarea hardware-ului a eșuat: {hardware.name}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Eroare la configurarea hardware-ului: {e}")
            return False
    
    def _reset_config(self) -> None:
        """Resetează configurația hardware."""
        # Resetează variabilele de mediu
        env_vars_to_reset = [
            "CUDA_VISIBLE_DEVICES",
            "OPENCV_FFMPEG_CAPTURE_OPTIONS",
            "MOVIEPY_TEMP_DIR"
        ]
        
        for var in env_vars_to_reset:
            if var in os.environ:
                del os.environ[var]
        
        self.moviepy_config.clear()
    
    def _configure_nvidia(self, hardware: HardwareInfo) -> bool:
        """Configurează pentru GPU NVIDIA cu CUDA."""
        try:
            # Setează variabilele de mediu pentru CUDA
            os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # Folosește primul GPU
            
            # Configurează MoviePy pentru CUDA
            self.moviepy_config.update({
                "codec": "h264_nvenc",  # Encoder NVIDIA
                "ffmpeg_params": [
                    "-hwaccel", "cuda",
                    "-hwaccel_output_format", "cuda",
                    "-c:v", "h264_nvenc",
                    "-preset", "fast",
                    "-b:v", "5M"
                ],
                "threads": 4,
                "temp_audiofile_path": self._get_temp_dir()
            })
            
            # Configurează OpenCV pentru GPU
            os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = "hwaccel;cuda"
            
            logger.info("🚀 Configurare NVIDIA CUDA activată")
            return True
            
        except Exception as e:
            logger.error(f"❌ Eroare configurare NVIDIA: {e}")
            return False
    
    def _configure_amd(self, hardware: HardwareInfo) -> bool:
        """Configurează pentru GPU AMD."""
        try:
            # AMD nu are suport direct în MoviePy, folosim optimizări CPU
            self.moviepy_config.update({
                "codec": "libx264",
                "ffmpeg_params": [
                    "-c:v", "libx264",
                    "-preset", "medium",
                    "-crf", "23",
                    "-threads", str(min(8, os.cpu_count() or 4))
                ],
                "threads": min(8, os.cpu_count() or 4),
                "temp_audiofile_path": self._get_temp_dir()
            })
            
            logger.info("🔧 Configurare AMD (optimizat CPU) activată")
            return True
            
        except Exception as e:
            logger.error(f"❌ Eroare configurare AMD: {e}")
            return False
    
    def _configure_intel(self, hardware: HardwareInfo) -> bool:
        """Configurează pentru GPU Intel."""
        try:
            # Intel QuickSync pentru GPU-uri mai noi
            if "ARC" in hardware.name.upper() or "XE" in hardware.name.upper():
                self.moviepy_config.update({
                    "codec": "h264_qsv",
                    "ffmpeg_params": [
                        "-hwaccel", "qsv",
                        "-c:v", "h264_qsv",
                        "-preset", "medium",
                        "-b:v", "3M"
                    ],
                    "threads": 4,
                    "temp_audiofile_path": self._get_temp_dir()
                })
                logger.info("🔧 Configurare Intel QuickSync activată")
            else:
                # GPU Intel mai vechi, folosim CPU optimizat
                return self._configure_cpu(hardware)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Eroare configurare Intel: {e}")
            return False
    
    def _configure_cpu(self, hardware: HardwareInfo) -> bool:
        """Configurează pentru procesare CPU."""
        try:
            cpu_count = os.cpu_count() or 4
            threads = min(cpu_count, 8)  # Limitează la 8 thread-uri pentru stabilitate
            
            self.moviepy_config.update({
                "codec": "libx264",
                "ffmpeg_params": [
                    "-c:v", "libx264",
                    "-preset", "medium",
                    "-crf", "23",
                    "-threads", str(threads)
                ],
                "threads": threads,
                "temp_audiofile_path": self._get_temp_dir()
            })
            
            logger.info(f"🔧 Configurare CPU activată ({threads} thread-uri)")
            return True
            
        except Exception as e:
            logger.error(f"❌ Eroare configurare CPU: {e}")
            return False
    
    def _get_temp_dir(self) -> str:
        """Returnează directorul temporar pentru MoviePy."""
        temp_dir = os.path.join(os.getcwd(), "storage", "temp", "moviepy")
        os.makedirs(temp_dir, exist_ok=True)
        return temp_dir
    
    def get_moviepy_config(self) -> Dict[str, Any]:
        """Returnează configurația pentru MoviePy."""
        return self.moviepy_config.copy()
    
    def get_current_hardware(self) -> Optional[HardwareInfo]:
        """Returnează hardware-ul configurat curent."""
        return self.current_config
    
    def apply_to_moviepy_clip(self, clip, output_path: str, **kwargs) -> None:
        """
        Aplică configurația hardware la un clip MoviePy pentru export.
        
        Args:
            clip: Clip-ul MoviePy de exportat
            output_path: Calea de salvare
            **kwargs: Parametri suplimentari pentru write_videofile
        """
        try:
            # Combină configurația hardware cu parametrii furnizați
            export_params = self.moviepy_config.copy()
            export_params.update(kwargs)
            
            # Extrage parametrii specifici MoviePy
            codec = export_params.pop("codec", "libx264")
            threads = export_params.pop("threads", 4)
            ffmpeg_params = export_params.pop("ffmpeg_params", [])
            temp_audiofile = export_params.pop("temp_audiofile_path", None)
            
            logger.info(f"🎬 Export video cu {self.current_config.name if self.current_config else 'configurație implicită'}")
            
            # Exportă video-ul
            clip.write_videofile(
                output_path,
                codec=codec,
                threads=threads,
                ffmpeg_params=ffmpeg_params,
                temp_audiofile=temp_audiofile,
                **export_params
            )
            
        except Exception as e:
            logger.error(f"❌ Eroare la exportul video: {e}")
            raise
    
    def get_performance_info(self) -> Dict[str, str]:
        """Returnează informații despre performanța configurației curente."""
        if not self.current_config:
            return {"status": "Nicio configurație activă"}
        
        info = {
            "Hardware": self.current_config.name,
            "Tip": self.current_config.type.upper(),
            "Viteză estimată": self.current_config.estimated_speed,
            "Scor performanță": f"{self.current_config.performance_score}/10"
        }
        
        if self.current_config.memory_gb:
            info["Memorie"] = f"{self.current_config.memory_gb:.1f}GB"
        
        if self.current_config.compute_capability:
            info["Compute Capability"] = self.current_config.compute_capability
        
        return info


# Instanță globală pentru configurarea hardware-ului
_hardware_configurator = None

def get_hardware_configurator() -> HardwareConfigurator:
    """Returnează instanța globală a configuratorului de hardware."""
    global _hardware_configurator
    if _hardware_configurator is None:
        _hardware_configurator = HardwareConfigurator()
    return _hardware_configurator

def configure_hardware_for_video_processing(hardware_type: str = "auto") -> bool:
    """
    Configurează hardware-ul pentru procesarea video.
    
    Args:
        hardware_type: Tipul hardware-ului ("auto", "cpu", "nvidia", "amd", "intel")
        
    Returns:
        bool: True dacă configurarea a reușit
    """
    detector = get_hardware_detector()
    configurator = get_hardware_configurator()
    
    if hardware_type == "auto":
        # Alege automat cel mai performant hardware
        hardware = detector.get_recommended_hardware()
    else:
        # Alege hardware-ul de tipul specificat
        hardware_list = detector.get_hardware_by_type(hardware_type)
        hardware = hardware_list[0] if hardware_list else None
    
    if hardware:
        return configurator.configure_hardware(hardware)
    else:
        logger.warning(f"⚠️ Nu s-a găsit hardware de tipul: {hardware_type}")
        return False
