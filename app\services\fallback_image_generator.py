"""
Fallback Image Generator for Contextual Images

This service provides a reliable fallback when AI image generation services are unavailable.
It creates high-quality placeholder images with contextual information.
"""

import logging
import os
import tempfile
from typing import Optional, Dict, Any
from PIL import Image, ImageDraw, ImageFont
import io
import hashlib
import colorsys
import random

logger = logging.getLogger(__name__)


class FallbackImageGenerator:
    """Generates contextual placeholder images when AI services are unavailable"""
    
    def __init__(self):
        self.cache_dir = "storage/fallback_images"
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Color schemes for different scene types
        self.color_schemes = {
            'battle': {
                'primary': (139, 0, 0),      # Dark red
                'secondary': (255, 140, 0),   # Dark orange
                'accent': (255, 215, 0),      # Gold
                'background': (25, 25, 25)    # Dark gray
            },
            'dialogue': {
                'primary': (70, 130, 180),    # Steel blue
                'secondary': (176, 196, 222), # Light steel blue
                'accent': (255, 255, 255),    # White
                'background': (47, 79, 79)    # Dark slate gray
            },
            'landscape': {
                'primary': (34, 139, 34),     # <PERSON> green
                'secondary': (144, 238, 144), # Light green
                'accent': (255, 255, 224),    # Light yellow
                'background': (25, 25, 112)   # Midnight blue
            },
            'action': {
                'primary': (255, 69, 0),      # Red orange
                'secondary': (255, 140, 0),   # Dark orange
                'accent': (255, 255, 0),      # Yellow
                'background': (0, 0, 0)       # Black
            },
            'abstract': {
                'primary': (138, 43, 226),    # Blue violet
                'secondary': (147, 112, 219), # Medium purple
                'accent': (255, 182, 193),    # Light pink
                'background': (25, 25, 25)    # Dark gray
            }
        }
        
        # Icons/symbols for different scene types
        self.scene_symbols = {
            'battle': '⚔️',
            'dialogue': '💬',
            'landscape': '🏞️',
            'action': '⚡',
            'abstract': '🎨'
        }

    async def generate_contextual_image(self, prompt: str, width: int = 1080, height: int = 1920,
                                      scene_type: str = "abstract", emotional_tone: str = "neutral") -> Optional[bytes]:
        """Generate a contextual placeholder image based on the prompt"""
        try:
            # Create cache key
            cache_key = hashlib.md5(f"{prompt}_{width}_{height}_{scene_type}_{emotional_tone}".encode()).hexdigest()
            cache_path = os.path.join(self.cache_dir, f"{cache_key}.png")
            
            # Check cache first
            if os.path.exists(cache_path):
                with open(cache_path, 'rb') as f:
                    return f.read()
            
            # Generate new image
            image_data = self._create_contextual_image(prompt, width, height, scene_type, emotional_tone)
            
            # Cache the result
            if image_data:
                with open(cache_path, 'wb') as f:
                    f.write(image_data)
            
            return image_data
            
        except Exception as e:
            logger.error(f"Fallback image generation failed: {e}")
            return None

    def _create_contextual_image(self, prompt: str, width: int, height: int, 
                               scene_type: str, emotional_tone: str) -> bytes:
        """Create a contextual image with visual elements based on the prompt"""
        
        # Get color scheme
        colors = self.color_schemes.get(scene_type, self.color_schemes['abstract'])
        
        # Create image
        image = Image.new('RGB', (width, height), colors['background'])
        draw = ImageDraw.Draw(image)
        
        # Add gradient background
        self._add_gradient_background(image, colors, emotional_tone)
        
        # Add geometric patterns based on scene type
        self._add_scene_patterns(draw, width, height, scene_type, colors)
        
        # Add text elements
        self._add_text_elements(draw, width, height, prompt, scene_type, colors)
        
        # Add scene symbol
        self._add_scene_symbol(draw, width, height, scene_type, colors)
        
        # Convert to bytes
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='PNG', quality=95)
        img_buffer.seek(0)
        
        return img_buffer.getvalue()

    def _add_gradient_background(self, image: Image.Image, colors: Dict[str, tuple], emotional_tone: str):
        """Add a gradient background based on emotional tone"""
        width, height = image.size
        
        # Create gradient overlay
        gradient = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(gradient)
        
        # Adjust gradient based on emotional tone
        if emotional_tone == "dramatic":
            # Diagonal gradient
            for i in range(width + height):
                alpha = int(255 * (i / (width + height)) * 0.3)
                color = colors['primary'] + (alpha,)
                draw.line([(i, 0), (0, i)], fill=color, width=2)
        elif emotional_tone == "peaceful":
            # Soft radial gradient
            center_x, center_y = width // 2, height // 2
            max_radius = min(width, height) // 2
            for r in range(0, max_radius, 10):
                alpha = int(255 * (1 - r / max_radius) * 0.2)
                color = colors['secondary'] + (alpha,)
                draw.ellipse([center_x - r, center_y - r, center_x + r, center_y + r], 
                           outline=color, width=2)
        else:
            # Linear gradient
            for y in range(height):
                alpha = int(255 * (y / height) * 0.2)
                color = colors['primary'] + (alpha,)
                draw.line([(0, y), (width, y)], fill=color)
        
        # Composite gradient onto image
        image.paste(gradient, (0, 0), gradient)

    def _add_scene_patterns(self, draw: ImageDraw.Draw, width: int, height: int,
                          scene_type: str, colors: Dict[str, tuple]):
        """Add professional geometric patterns based on scene type"""

        if scene_type == "battle":
            # Structured angular patterns suggesting conflict
            center_x, center_y = width // 2, height // 2

            # Create crossed swords pattern
            sword_length = min(width, height) // 3
            draw.line([(center_x - sword_length//2, center_y - sword_length//2),
                      (center_x + sword_length//2, center_y + sword_length//2)],
                     fill=colors['accent'], width=6)
            draw.line([(center_x + sword_length//2, center_y - sword_length//2),
                      (center_x - sword_length//2, center_y + sword_length//2)],
                     fill=colors['accent'], width=6)

            # Add shield outline
            shield_size = sword_length // 2
            draw.ellipse([center_x - shield_size, center_y - shield_size,
                         center_x + shield_size, center_y + shield_size],
                        outline=colors['secondary'], width=4)

        elif scene_type == "dialogue":
            # Speech bubble and conversation elements
            center_x, center_y = width // 2, height // 2

            # Main speech bubble
            bubble_w, bubble_h = width // 3, height // 4
            draw.ellipse([center_x - bubble_w//2, center_y - bubble_h//2,
                         center_x + bubble_w//2, center_y + bubble_h//2],
                        outline=colors['secondary'], width=4)

            # Smaller bubbles
            for i in range(2):
                offset_x = (i - 0.5) * width // 3
                offset_y = height // 6
                bubble_size = 30 + i * 20
                draw.ellipse([center_x + offset_x - bubble_size, center_y + offset_y - bubble_size,
                             center_x + offset_x + bubble_size, center_y + offset_y + bubble_size],
                            outline=colors['accent'], width=2)

        elif scene_type == "landscape":
            # Mountain and horizon patterns
            # Draw mountain silhouettes
            mountain_points = []
            for x in range(0, width, width // 8):
                y = height // 2 + random.randint(-height//6, height//8)
                mountain_points.append((x, y))
            mountain_points.append((width, height // 2))

            if len(mountain_points) > 2:
                draw.polygon(mountain_points + [(width, height), (0, height)],
                           outline=colors['secondary'], width=3)

            # Add sun/moon
            sun_x, sun_y = width - width // 6, height // 6
            sun_radius = min(width, height) // 12
            draw.ellipse([sun_x - sun_radius, sun_y - sun_radius,
                         sun_x + sun_radius, sun_y + sun_radius],
                        outline=colors['accent'], width=3)

        elif scene_type == "action":
            # Dynamic motion lines and energy patterns
            center_x, center_y = width // 2, height // 2

            # Radiating energy lines
            for i in range(8):
                angle = i * 45  # 45 degree increments
                import math
                end_x = center_x + int(math.cos(math.radians(angle)) * width // 3)
                end_y = center_y + int(math.sin(math.radians(angle)) * height // 3)

                # Create tapered lines for motion effect
                for thickness in range(6, 1, -1):
                    alpha = 255 - (6 - thickness) * 40
                    line_color = colors['accent'][:3] + (alpha,) if len(colors['accent']) == 3 else colors['accent']
                    draw.line([(center_x, center_y), (end_x, end_y)],
                             fill=line_color, width=thickness)

        else:  # abstract
            # Elegant geometric composition
            center_x, center_y = width // 2, height // 2

            # Concentric shapes
            for i in range(3):
                size = (i + 1) * min(width, height) // 8
                draw.rectangle([center_x - size, center_y - size,
                               center_x + size, center_y + size],
                              outline=colors['secondary'], width=2)

            # Diagonal accent lines
            draw.line([(0, 0), (width // 3, height // 3)], fill=colors['accent'], width=3)
            draw.line([(width, 0), (2 * width // 3, height // 3)], fill=colors['accent'], width=3)

    def _add_text_elements(self, draw: ImageDraw.Draw, width: int, height: int,
                         prompt: str, scene_type: str, colors: Dict[str, tuple]):
        """Add professional text elements from the prompt"""

        # Extract key words from prompt, prioritizing meaningful terms
        words = prompt.split()

        # Filter for meaningful words (longer than 2 chars, not common words)
        common_words = {'the', 'and', 'with', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}
        key_words = [word for word in words if len(word) > 2 and word.lower() not in common_words and word.isalpha()][:2]

        if not key_words:
            # Use scene type as fallback
            scene_labels = {
                'battle': 'BATTLE',
                'dialogue': 'DIALOGUE',
                'landscape': 'LANDSCAPE',
                'action': 'ACTION',
                'abstract': 'ABSTRACT'
            }
            key_words = [scene_labels.get(scene_type, 'IMAGE')]

        # Try to load a professional font
        font = None
        font_size = min(width, height) // 15  # Larger, more readable

        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/calibri.ttf",
            "C:/Windows/Fonts/segoeui.ttf",
            "/System/Library/Fonts/Arial.ttf",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
        ]

        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, font_size)
                    break
            except:
                continue

        if not font:
            try:
                font = ImageFont.load_default()
            except:
                font = None

        # Add title text at top
        if key_words:
            title_text = ' '.join(key_words).upper()

            if font:
                bbox = draw.textbbox((0, 0), title_text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
            else:
                text_width = len(title_text) * 8
                text_height = 12

            # Center the title at top
            x = (width - text_width) // 2
            y = height // 8

            # Ensure text stays within bounds
            x = max(10, min(x, width - text_width - 10))
            y = max(10, y)

            # Add text with professional styling
            outline_color = colors['background']
            text_color = colors['accent']

            # Draw shadow/outline for better visibility
            shadow_offset = 2
            draw.text((x + shadow_offset, y + shadow_offset), title_text, font=font, fill=outline_color)

            # Draw main text
            draw.text((x, y), title_text, font=font, fill=text_color)

        # Add scene type label at bottom
        scene_label = f"Scene: {scene_type.title()}"

        if font:
            # Use smaller font for label
            try:
                label_font = ImageFont.truetype(font.path, font_size // 2) if hasattr(font, 'path') else font
            except:
                label_font = font

            bbox = draw.textbbox((0, 0), scene_label, font=label_font)
            text_width = bbox[2] - bbox[0]
        else:
            label_font = font
            text_width = len(scene_label) * 6

        # Position at bottom right
        x = width - text_width - 20
        y = height - 40

        # Draw label
        draw.text((x + 1, y + 1), scene_label, font=label_font, fill=colors['background'])
        draw.text((x, y), scene_label, font=label_font, fill=colors['secondary'])

    def _add_scene_symbol(self, draw: ImageDraw.Draw, width: int, height: int, 
                        scene_type: str, colors: Dict[str, tuple]):
        """Add a scene symbol/icon"""
        
        symbol = self.scene_symbols.get(scene_type, '🎨')
        
        # Try to add symbol as text
        try:
            font_size = min(width, height) // 8
            font = ImageFont.truetype("seguiemj.ttf", font_size)  # Emoji font
            
            # Position in top-right corner
            x = width - 100
            y = 50
            
            draw.text((x, y), symbol, font=font, fill=colors['accent'])
        except:
            # Fallback: draw a simple geometric shape
            x = width - 80
            y = 70
            size = 40
            
            if scene_type == "battle":
                # Draw crossed lines (swords)
                draw.line([(x, y), (x + size, y + size)], fill=colors['accent'], width=4)
                draw.line([(x + size, y), (x, y + size)], fill=colors['accent'], width=4)
            elif scene_type == "dialogue":
                # Draw speech bubble
                draw.ellipse([x, y, x + size, y + size], outline=colors['accent'], width=3)
            elif scene_type == "landscape":
                # Draw mountain shape
                points = [(x, y + size), (x + size//2, y), (x + size, y + size)]
                draw.polygon(points, outline=colors['accent'], width=3)
            elif scene_type == "action":
                # Draw lightning bolt shape
                points = [(x, y), (x + size//3, y + size//2), (x, y + size//2), (x + size, y + size)]
                draw.line(points, fill=colors['accent'], width=4)
            else:
                # Draw abstract circle
                draw.ellipse([x, y, x + size, y + size], outline=colors['accent'], width=3)

    async def cleanup(self):
        """Cleanup resources"""
        pass


# Factory function for easy integration
async def create_fallback_generator():
    """Create a fallback image generator"""
    return FallbackImageGenerator()
