import glob
import itertools
import os
import random
import gc
import shutil
import time
import threading
import signal
import numpy as np
from typing import List
from loguru import logger

# Import psutil with fallback
try:
    import psutil
except ImportError:
    psutil = None

# Apply PIL compatibility patch before importing MoviePy
try:
    import PIL.Image as Image
    # Check if ANTIALIAS exists, if not, patch it
    if not hasattr(Image, 'ANTIALIAS'):
        if hasattr(Image, 'Resampling') and hasattr(Image.Resampling, 'LANCZOS'):
            Image.ANTIALIAS = Image.Resampling.LANCZOS
            logger.info("✅ Applied PIL.Image.ANTIALIAS compatibility patch: ANTIALIAS -> Resampling.LANCZOS")
        elif hasattr(Image, 'LANCZOS'):
            Image.ANTIALIAS = Image.LANCZOS
            logger.info("✅ Applied PIL.Image.ANTIALIAS compatibility patch: ANTIALIAS -> LANCZOS")
        else:
            logger.warning("⚠️ Could not apply PIL.Image.ANTIALIAS compatibility patch")
    else:
        logger.info("✅ PIL.Image.ANTIALIAS already available")
except Exception as e:
    logger.error(f"❌ PIL compatibility patch failed in video service: {e}")
from moviepy.editor import (
    AudioFileClip,
    ColorClip,
    CompositeAudioClip,
    CompositeVideoClip,
    ImageClip,
    TextClip,
    VideoFileClip,
    concatenate_videoclips,
)
from moviepy.audio import fx as afx
from moviepy.video.tools.subtitles import SubtitlesClip
from PIL import ImageFont

from app.models import const
from app.models.schema import (
    MaterialInfo,
    VideoAspect,
    VideoConcatMode,
    VideoParams,
    VideoTransitionMode,
)
from app.services.utils import video_effects
from app.utils import utils

class SubClippedVideoClip:
    def __init__(self, file_path, start_time=None, end_time=None, width=None, height=None, duration=None):
        self.file_path = file_path
        self.start_time = start_time
        self.end_time = end_time
        self.width = width
        self.height = height
        if duration is None:
            self.duration = end_time - start_time
        else:
            self.duration = duration

    def __str__(self):
        return f"SubClippedVideoClip(file_path={self.file_path}, start_time={self.start_time}, end_time={self.end_time}, duration={self.duration}, width={self.width}, height={self.height})"


audio_codec = "aac"
video_codec = "libx264"
fps = 30

def close_clip(clip):
    if clip is None:
        return
        
    try:
        # close main resources
        if hasattr(clip, 'reader') and clip.reader is not None:
            if hasattr(clip.reader, 'close'):
                clip.reader.close()
            elif hasattr(clip.reader, 'close_proc'):
                clip.reader.close_proc()

        # close audio resources
        if hasattr(clip, 'audio') and clip.audio is not None:
            if hasattr(clip.audio, 'reader') and clip.audio.reader is not None:
                if hasattr(clip.audio.reader, 'close'):
                    clip.audio.reader.close()
                elif hasattr(clip.audio.reader, 'close_proc'):
                    clip.audio.reader.close_proc()
            del clip.audio

        # close mask resources
        if hasattr(clip, 'mask') and clip.mask is not None:
            if hasattr(clip.mask, 'reader') and clip.mask.reader is not None:
                if hasattr(clip.mask.reader, 'close'):
                    clip.mask.reader.close()
                elif hasattr(clip.mask.reader, 'close_proc'):
                    clip.mask.reader.close_proc()
            del clip.mask
            
        # handle child clips in composite clips
        if hasattr(clip, 'clips') and clip.clips:
            for child_clip in clip.clips:
                if child_clip is not clip:  # avoid possible circular references
                    close_clip(child_clip)
            
        # clear clip list
        if hasattr(clip, 'clips'):
            clip.clips = []
            
    except Exception as e:
        logger.error(f"failed to close clip: {str(e)}")
    
    del clip
    gc.collect()

def delete_files(files: List[str] | str):
    if isinstance(files, str):
        files = [files]
        
    for file in files:
        try:
            os.remove(file)
        except:
            pass

def get_bgm_file(bgm_type: str = "random", bgm_file: str = ""):
    if not bgm_type:
        return ""

    if bgm_file and os.path.exists(bgm_file):
        return bgm_file

    if bgm_type == "random":
        suffix = "*.mp3"
        song_dir = utils.song_dir()
        files = glob.glob(os.path.join(song_dir, suffix))
        return random.choice(files)

    return ""


def combine_videos(
    combined_video_path: str,
    video_paths: List[str],
    audio_file: str,
    video_aspect: VideoAspect = VideoAspect.portrait,
    video_concat_mode: VideoConcatMode = VideoConcatMode.random,
    video_transition_mode: VideoTransitionMode = None,
    max_clip_duration: int = 5,
    threads: int = 2,
) -> str:
    audio_clip = AudioFileClip(audio_file)
    audio_duration = audio_clip.duration
    logger.info(f"audio duration: {audio_duration} seconds")
    # Required duration of each clip
    req_dur = audio_duration / len(video_paths)
    req_dur = max_clip_duration
    logger.info(f"maximum clip duration: {req_dur} seconds")
    output_dir = os.path.dirname(combined_video_path)

    aspect = VideoAspect(video_aspect)
    video_width, video_height = aspect.to_resolution()

    processed_clips = []
    subclipped_items = []
    video_duration = 0
    for video_path in video_paths:
        clip = VideoFileClip(video_path)
        clip_duration = clip.duration
        clip_w, clip_h = clip.size
        close_clip(clip)
        
        start_time = 0

        while start_time < clip_duration:
            end_time = min(start_time + max_clip_duration, clip_duration)            
            if clip_duration - start_time >= max_clip_duration:
                subclipped_items.append(SubClippedVideoClip(file_path= video_path, start_time=start_time, end_time=end_time, width=clip_w, height=clip_h))
            start_time = end_time    
            if video_concat_mode.value == VideoConcatMode.sequential.value:
                break

    # random subclipped_items order
    if video_concat_mode.value == VideoConcatMode.random.value:
        random.shuffle(subclipped_items)
        
    logger.debug(f"total subclipped items: {len(subclipped_items)}")
    
    # Add downloaded clips over and over until the duration of the audio (max_duration) has been reached
    for i, subclipped_item in enumerate(subclipped_items):
        if video_duration > audio_duration:
            break
        
        logger.debug(f"processing clip {i+1}: {subclipped_item.width}x{subclipped_item.height}, current duration: {video_duration:.2f}s, remaining: {audio_duration - video_duration:.2f}s")
        
        try:
            clip = VideoFileClip(subclipped_item.file_path).subclip(subclipped_item.start_time, subclipped_item.end_time)
            clip_duration = clip.duration
            # Not all videos are same size, so we need to resize them
            clip_w, clip_h = clip.size
            if clip_w != video_width or clip_h != video_height:
                clip_ratio = clip.w / clip.h
                video_ratio = video_width / video_height
                logger.debug(f"resizing clip, source: {clip_w}x{clip_h}, ratio: {clip_ratio:.2f}, target: {video_width}x{video_height}, ratio: {video_ratio:.2f}")
                
                if clip_ratio == video_ratio:
                    clip = clip.resize((video_width, video_height))
                else:
                    if clip_ratio > video_ratio:
                        scale_factor = video_width / clip_w
                    else:
                        scale_factor = video_height / clip_h

                    new_width = int(clip_w * scale_factor)
                    new_height = int(clip_h * scale_factor)

                    background = ColorClip(size=(video_width, video_height), color=(0, 0, 0)).set_duration(clip_duration)
                    clip_resized = clip.resize((new_width, new_height)).set_position("center")
                    clip = CompositeVideoClip([background, clip_resized])
                    
            shuffle_side = random.choice(["left", "right", "top", "bottom"])
            if video_transition_mode.value == VideoTransitionMode.none.value:
                clip = clip
            elif video_transition_mode.value == VideoTransitionMode.fade_in.value:
                clip = video_effects.fadein_transition(clip, 1)
            elif video_transition_mode.value == VideoTransitionMode.fade_out.value:
                clip = video_effects.fadeout_transition(clip, 1)
            elif video_transition_mode.value == VideoTransitionMode.slide_in.value:
                clip = video_effects.slidein_transition(clip, 1, shuffle_side)
            elif video_transition_mode.value == VideoTransitionMode.slide_out.value:
                clip = video_effects.slideout_transition(clip, 1, shuffle_side)
            elif video_transition_mode.value == VideoTransitionMode.shuffle.value:
                transition_funcs = [
                    lambda c: video_effects.fadein_transition(c, 1),
                    lambda c: video_effects.fadeout_transition(c, 1),
                    lambda c: video_effects.slidein_transition(c, 1, shuffle_side),
                    lambda c: video_effects.slideout_transition(c, 1, shuffle_side),
                ]
                shuffle_transition = random.choice(transition_funcs)
                clip = shuffle_transition(clip)

            if clip.duration > max_clip_duration:
                clip = clip.subclip(0, max_clip_duration)
                
            # wirte clip to temp file
            clip_file = f"{output_dir}/temp-clip-{i+1}.mp4"
            clip.write_videofile(clip_file, logger=None, fps=fps, codec=video_codec)
            
            close_clip(clip)
        
            processed_clips.append(SubClippedVideoClip(file_path=clip_file, duration=clip.duration, width=clip_w, height=clip_h))
            video_duration += clip.duration
            
        except Exception as e:
            logger.error(f"failed to process clip: {str(e)}")
    
    # loop processed clips until the video duration matches or exceeds the audio duration.
    if video_duration < audio_duration:
        logger.warning(f"video duration ({video_duration:.2f}s) is shorter than audio duration ({audio_duration:.2f}s), looping clips to match audio length.")
        base_clips = processed_clips.copy()
        for clip in itertools.cycle(base_clips):
            if video_duration >= audio_duration:
                break
            processed_clips.append(clip)
            video_duration += clip.duration
        logger.info(f"video duration: {video_duration:.2f}s, audio duration: {audio_duration:.2f}s, looped {len(processed_clips)-len(base_clips)} clips")
     
    # merge video clips progressively, avoid loading all videos at once to avoid memory overflow
    logger.info("starting clip merging process")
    if not processed_clips:
        logger.warning("no clips available for merging")
        return combined_video_path
    
    # if there is only one clip, use it directly
    if len(processed_clips) == 1:
        logger.info("using single clip directly")
        shutil.copy(processed_clips[0].file_path, combined_video_path)
        delete_files(processed_clips)
        logger.info("video combining completed")
        return combined_video_path
    
    # create initial video file as base
    base_clip_path = processed_clips[0].file_path
    temp_merged_video = f"{output_dir}/temp-merged-video.mp4"
    temp_merged_next = f"{output_dir}/temp-merged-next.mp4"
    
    # copy first clip as initial merged video
    shutil.copy(base_clip_path, temp_merged_video)
    
    # merge remaining video clips one by one
    for i, clip in enumerate(processed_clips[1:], 1):
        logger.info(f"merging clip {i}/{len(processed_clips)-1}, duration: {clip.duration:.2f}s")
        
        try:
            # load current base video and next clip to merge
            base_clip = VideoFileClip(temp_merged_video)
            next_clip = VideoFileClip(clip.file_path)
            
            # merge these two clips
            merged_clip = concatenate_videoclips([base_clip, next_clip])

            # save merged result to temp file
            merged_clip.write_videofile(
                filename=temp_merged_next,
                threads=threads,
                logger=None,
                audio_codec=audio_codec,
                fps=fps,
            )
            close_clip(base_clip)
            close_clip(next_clip)
            close_clip(merged_clip)

            # Forțează garbage collection pentru a elibera memoria
            gc.collect()

            # Așteaptă puțin pentru ca fișierele să se închidă complet
            time.sleep(1)
            
            # replace base file with new merged file
            delete_files(temp_merged_video)
            os.rename(temp_merged_next, temp_merged_video)
            
        except Exception as e:
            logger.error(f"failed to merge clip: {str(e)}")
            continue
    
    # after merging, rename final result to target file name
    # Implementare robustă pentru gestionarea fișierelor în Windows

    # Forțează garbage collection pentru a elibera memoria
    gc.collect()

    # Așteaptă puțin pentru ca procesele să se închidă
    time.sleep(2)

    # Încearcă redenumirea cu retry logic
    max_retries = 5
    for attempt in range(max_retries):
        try:
            if os.path.exists(combined_video_path):
                os.remove(combined_video_path)
            os.rename(temp_merged_video, combined_video_path)
            break
        except PermissionError as e:
            if attempt < max_retries - 1:
                logger.warning(f"Încercare {attempt + 1}/{max_retries} eșuată pentru redenumire: {str(e)}")
                time.sleep(3)  # Așteaptă mai mult între încercări
                gc.collect()  # Forțează din nou garbage collection
            else:
                logger.error(f"Nu s-a putut redenumi fișierul după {max_retries} încercări: {str(e)}")
                # Ca ultimă soluție, copiază fișierul
                try:
                    shutil.copy2(temp_merged_video, combined_video_path)
                    os.remove(temp_merged_video)
                    logger.info("Fișierul a fost copiat cu succes ca alternativă")
                except Exception as copy_error:
                    logger.error(f"Eroare la copierea fișierului: {str(copy_error)}")
                    raise e
    
    # clean temp files
    clip_files = [clip.file_path for clip in processed_clips]
    delete_files(clip_files)
            
    logger.info("video combining completed")
    return combined_video_path


def wrap_text(text, max_width, font="Arial", fontsize=60):
    # Create ImageFont
    font = ImageFont.truetype(font, fontsize)

    def get_text_size(inner_text):
        inner_text = inner_text.strip()
        left, top, right, bottom = font.getbbox(inner_text)
        return right - left, bottom - top

    width, height = get_text_size(text)
    if width <= max_width:
        return text, height

    processed = True

    _wrapped_lines_ = []
    words = text.split(" ")
    _txt_ = ""
    for word in words:
        _before = _txt_
        _txt_ += f"{word} "
        _width, _height = get_text_size(_txt_)
        if _width <= max_width:
            continue
        else:
            if _txt_.strip() == word.strip():
                processed = False
                break
            _wrapped_lines_.append(_before)
            _txt_ = f"{word} "
    _wrapped_lines_.append(_txt_)
    if processed:
        _wrapped_lines_ = [line.strip() for line in _wrapped_lines_]
        result = "\n".join(_wrapped_lines_).strip()
        height = len(_wrapped_lines_) * height
        return result, height

    _wrapped_lines_ = []
    chars = list(text)
    _txt_ = ""
    for word in chars:
        _txt_ += word
        _width, _height = get_text_size(_txt_)
        if _width <= max_width:
            continue
        else:
            _wrapped_lines_.append(_txt_)
            _txt_ = ""
    _wrapped_lines_.append(_txt_)
    result = "\n".join(_wrapped_lines_).strip()
    height = len(_wrapped_lines_) * height
    return result, height


def generate_video(
    video_path: str,
    audio_path: str,
    subtitle_path: str,
    output_file: str,
    params: VideoParams,
    contextual_images: list = None,
):
    aspect = VideoAspect(params.video_aspect)
    video_width, video_height = aspect.to_resolution()

    logger.info(f"generating video: {video_width} x {video_height}")
    logger.info(f"  ① video: {video_path}")
    logger.info(f"  ② audio: {audio_path}")
    logger.info(f"  ③ subtitle: {subtitle_path}")
    logger.info(f"  ④ output: {output_file}")

    # https://github.com/harry0703/MoneyPrinterTurbo/issues/217
    # PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'final-1.mp4.tempTEMP_MPY_wvf_snd.mp3'
    # write into the same directory as the output file
    output_dir = os.path.dirname(output_file)

    font_path = ""
    if params.subtitle_enabled:
        if not params.font_name:
            params.font_name = "STHeitiMedium.ttc"
        font_path = os.path.join(utils.font_dir(), params.font_name)
        if os.name == "nt":
            font_path = font_path.replace("\\", "/")

        logger.info(f"  ⑤ font: {font_path}")

    def create_pil_text_clip(text, params, video_width, video_height):
        """Create text clip using PIL (ImageMagick-free alternative)"""
        from PIL import Image, ImageDraw, ImageFont
        from moviepy.editor import ImageClip

        try:
            # Validate and sanitize input
            if not text or not text.strip():
                text = "..."  # Fallback for empty text

            text = str(text).strip()  # Ensure it's a string

            # Text properties with safe defaults
            font_size = max(20, int(getattr(params, 'font_size', 50)))
            stroke_width = max(1, min(5, int(getattr(params, 'stroke_width', 2))))

            # Load font
            try:
                font = ImageFont.truetype(font_path, font_size)
            except:
                # Fallback to default font
                font = ImageFont.load_default()
                logger.warning("Using default font as fallback")

            # Calculate text dimensions
            lines = text.split('\n')
            line_heights = []
            line_widths = []

            for line in lines:
                bbox = font.getbbox(line)
                line_width = bbox[2] - bbox[0]
                line_height = bbox[3] - bbox[1]
                line_widths.append(line_width)
                line_heights.append(line_height)

            max_line_width = max(line_widths) if line_widths else 100
            total_height = sum(line_heights) + (len(lines) - 1) * int(font_size * 0.2)

            # Add padding
            padding = 20
            img_width = max_line_width + 2 * padding + 2 * stroke_width
            img_height = total_height + 2 * padding + 2 * stroke_width

            # Create image with transparent background
            img = Image.new('RGBA', (img_width, img_height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # Parse colors
            def parse_color(color_str):
                if color_str.startswith('#'):
                    color_str = color_str[1:]
                    return tuple(int(color_str[i:i+2], 16) for i in (0, 2, 4))
                return (255, 255, 255)  # Default white

            text_color = parse_color(params.text_fore_color)
            stroke_color = parse_color(params.stroke_color)

            # Draw text with stroke
            y_offset = padding + stroke_width
            for i, line in enumerate(lines):
                line_width = line_widths[i]
                x_offset = (img_width - line_width) // 2  # Center align

                # Draw stroke
                if stroke_width > 0:
                    for dx in range(-stroke_width, stroke_width + 1):
                        for dy in range(-stroke_width, stroke_width + 1):
                            if dx*dx + dy*dy <= stroke_width*stroke_width:
                                draw.text((x_offset + dx, y_offset + dy), line,
                                        font=font, fill=stroke_color)

                # Draw main text
                draw.text((x_offset, y_offset), line, font=font, fill=text_color)
                y_offset += line_heights[i] + int(font_size * 0.2)

            # Add background if enabled
            if params.text_background_color:
                # Create background rectangle
                bg_img = Image.new('RGBA', (img_width, img_height), (0, 0, 0, 128))
                bg_img.paste(img, (0, 0), img)
                img = bg_img

            # Convert PIL image to numpy array for MoviePy
            img_array = np.array(img)

            # Create MoviePy ImageClip
            clip = ImageClip(img_array, duration=1)  # Duration will be set later

            return clip

        except Exception as e:
            logger.error(f"PIL text rendering failed: {e}")
            # Return a simple colored rectangle as last resort
            fallback_img = np.zeros((100, 400, 3), dtype=np.uint8)
            fallback_img[:] = [255, 255, 255]  # White rectangle
            return ImageClip(fallback_img, duration=1)

    def create_text_clip(subtitle_item):
        params.font_size = int(params.font_size)
        params.stroke_width = int(params.stroke_width)
        phrase = subtitle_item[1]
        max_width = video_width * 0.9
        wrapped_txt, txt_height = wrap_text(
            phrase, max_width=max_width, font=font_path, fontsize=params.font_size
        )
        interline = int(params.font_size * 0.25)
        size=(int(max_width), int(txt_height + params.font_size * 0.25 + (interline * (wrapped_txt.count("\n") + 1))))

        # Convert background color boolean to proper value
        text_clip_params = {
            'txt': wrapped_txt,
            'font': font_path,
            'fontsize': params.font_size,
            'color': params.text_fore_color,
            'stroke_color': params.stroke_color,
            'stroke_width': params.stroke_width,
            # 'interline': interline,
            # 'size': size,
        }

        # Only add bg_color if background is enabled
        if params.text_background_color:
            text_clip_params['bg_color'] = "black"

        # Always use PIL-based text rendering (eliminates ImageMagick warnings completely)
        try:
            _clip = create_pil_text_clip(wrapped_txt, params, video_width, video_height)
            logger.debug("✅ PIL-based text clip created successfully")
        except Exception as pil_error:
            logger.error(f"PIL text rendering failed: {str(pil_error)}")
            # Create a simple fallback clip instead of trying MoviePy
            logger.info("Creating simple fallback text clip...")
            try:
                # Create a simple colored rectangle with basic text info
                fallback_img = np.zeros((100, min(400, video_width), 3), dtype=np.uint8)
                fallback_img[:] = [64, 64, 64]  # Dark gray background
                fallback_img[40:60, 10:-10] = [255, 255, 255]  # White stripe to indicate text
                _clip = ImageClip(fallback_img, duration=1)
                logger.info("✅ Fallback text clip created")
            except Exception as fallback_error:
                logger.error(f"Even fallback text clip failed: {fallback_error}")
                return None

        duration = subtitle_item[0][1] - subtitle_item[0][0]
        _clip = _clip.set_start(subtitle_item[0][0])
        _clip = _clip.set_end(subtitle_item[0][1])
        _clip = _clip.set_duration(duration)
        if params.subtitle_position == "bottom":
            _clip = _clip.set_position(("center", video_height * 0.95 - _clip.h))
        elif params.subtitle_position == "top":
            _clip = _clip.set_position(("center", video_height * 0.05))
        elif params.subtitle_position == "custom":
            # Ensure the subtitle is fully within the screen bounds
            margin = 10  # Additional margin, in pixels
            max_y = video_height - _clip.h - margin
            min_y = margin
            custom_y = (video_height - _clip.h) * (params.custom_position / 100)
            custom_y = max(
                min_y, min(custom_y, max_y)
            )  # Constrain the y value within the valid range
            _clip = _clip.set_position(("center", custom_y))
        else:  # center
            _clip = _clip.set_position(("center", "center"))
        return _clip

    video_clip = VideoFileClip(video_path).without_audio()
    audio_clip = AudioFileClip(audio_path)
    # Apply volume effect
    if params.voice_volume != 1.0:
        audio_clip = audio_clip.volumex(params.voice_volume)

    # Note: make_textclip function removed - using PIL-based text rendering only

    if subtitle_path and os.path.exists(subtitle_path):
        try:
            # Use our own UTF-8 compatible subtitle reading function
            from app.services import subtitle
            subtitle_items = subtitle.file_to_subtitles(subtitle_path)

            if subtitle_items:
                # Create text clips directly from our subtitle data
                text_clips = []
                for item in subtitle_items:
                    # item format: (index, time_string, text)
                    if len(item) >= 3:
                        index, time_string, text = item[0], item[1], item[2]

                        # Parse time string: "00:00:00,000 --> 00:00:02,000"
                        if " --> " in time_string:
                            start_str, end_str = time_string.split(" --> ")

                            # Convert time format from SRT to seconds
                            def srt_time_to_seconds(time_str):
                                # Format: "00:00:00,000"
                                time_str = time_str.replace(',', '.')
                                parts = time_str.split(':')
                                if len(parts) == 3:
                                    hours = float(parts[0])
                                    minutes = float(parts[1])
                                    seconds = float(parts[2])
                                    return hours * 3600 + minutes * 60 + seconds
                                return 0

                            start_time = srt_time_to_seconds(start_str.strip())
                            end_time = srt_time_to_seconds(end_str.strip())

                            # Create subtitle item tuple for create_text_clip
                            subtitle_item = ((start_time, end_time), text.strip())
                            try:
                                clip = create_text_clip(subtitle_item=subtitle_item)
                                if clip is not None:
                                    text_clips.append(clip)
                                else:
                                    logger.warning(f"Failed to create subtitle clip for: {text.strip()[:30]}...")
                            except Exception as e:
                                logger.warning(f"Error creating subtitle clip: {e}")
                                continue

                if text_clips:
                    video_clip = CompositeVideoClip([video_clip, *text_clips])
                    logger.info(f"Added {len(text_clips)} subtitle clips")
                else:
                    logger.warning("No valid subtitle clips created")
            else:
                logger.warning("No subtitle items found in file")

        except FileNotFoundError as e:
            logger.error(f"Subtitle file not found: {e}")
        except UnicodeDecodeError as e:
            logger.error(f"Subtitle file encoding error: {e}")
        except Exception as e:
            logger.error(f"Unexpected subtitle processing error: {e}")
            # Don't skip subtitles completely - let individual clip errors be handled above

    bgm_file = get_bgm_file(bgm_type=params.bgm_type, bgm_file=params.bgm_file)
    if bgm_file:
        try:
            bgm_clip = AudioFileClip(bgm_file)
            # Apply BGM effects
            if params.bgm_volume != 1.0:
                bgm_clip = bgm_clip.volumex(params.bgm_volume)
            bgm_clip = bgm_clip.audio_fadeout(3)
            bgm_clip = bgm_clip.audio_loop(duration=video_clip.duration)
            audio_clip = CompositeAudioClip([audio_clip, bgm_clip])
        except Exception as e:
            logger.error(f"failed to add bgm: {str(e)}")

    # Add contextual images if available (with safety checks)
    ENABLE_CONTEXTUAL_IMAGES = False  # TEMPORARILY DISABLED - Set to True to enable contextual images

    if contextual_images and ENABLE_CONTEXTUAL_IMAGES:
        try:
            logger.info(f"🖼️ Starting contextual image integration with {len(contextual_images)} images...")

            # Safety check: limit number of images to prevent memory issues
            max_images = 3
            if len(contextual_images) > max_images:
                logger.warning(f"⚠️ Too many contextual images ({len(contextual_images)}), limiting to {max_images}")
                contextual_images = contextual_images[:max_images]

            # Use timeout for the entire contextual image process
            def add_images_with_timeout():
                return _add_contextual_images(video_clip, contextual_images, subtitle_path)

            video_clip = run_with_timeout(add_images_with_timeout, timeout_seconds=180)  # 3 minute timeout
            logger.info("✅ Contextual image integration completed successfully")

        except TimeoutError:
            logger.error("⏰ Contextual image integration timed out after 3 minutes")
            logger.warning("⚠️ Continuing without contextual images")
        except Exception as e:
            logger.error(f"❌ Contextual image integration error: {e}")
            logger.warning("⚠️ Continuing without contextual images")
    elif contextual_images and not ENABLE_CONTEXTUAL_IMAGES:
        logger.info("ℹ️ Contextual images disabled for stability")
    else:
        logger.info("ℹ️ No contextual images to add")

    # Set audio with error handling
    try:
        logger.info("Setting audio track...")
        video_clip = video_clip.set_audio(audio_clip)
        logger.info("✅ Audio track set successfully")
    except Exception as e:
        logger.error(f"❌ Failed to set audio track: {e}")
        logger.warning("Continuing without audio...")

    # Write video file with enhanced error handling and progress monitoring
    try:
        logger.info(f"Starting video export to: {output_file}")
        logger.info(f"Export settings: fps={fps}, threads={params.n_threads or 2}, codec={audio_codec}")

        # Add progress callback and timeout handling
        start_time = time.time()

        def progress_callback(t):
            if t is not None:
                elapsed = time.time() - start_time
                if elapsed > 300:  # 5 minute timeout
                    logger.warning("⚠️ Video export taking longer than expected...")
                if elapsed % 30 == 0:  # Log every 30 seconds
                    logger.info(f"Export progress: {elapsed:.1f}s elapsed...")

        # Use safe export with timeout
        safe_video_export(
            video_clip,
            output_file,
            audio_codec=audio_codec,
            threads=params.n_threads or 2,
            logger=None,
            fps=fps,
            verbose=False,
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )

        export_time = time.time() - start_time
        logger.info(f"✅ Video export completed successfully in {export_time:.1f}s")

    except Exception as e:
        logger.error(f"❌ Video export failed: {e}")
        logger.error(f"Output file: {output_file}")
        logger.error(f"Video duration: {video_clip.duration if hasattr(video_clip, 'duration') else 'unknown'}")

        # Try alternative export method
        try:
            logger.info("🔄 Attempting alternative export method...")
            alternative_output = output_file.replace('.mp4', '_alt.mp4')

            safe_video_export(
                video_clip,
                alternative_output,
                codec='libx264',
                audio_codec='aac',
                threads=1,  # Single thread for stability
                logger=None,
                fps=24,  # Lower FPS for stability
                verbose=False,
                preset='ultrafast'  # Fastest encoding
            )

            # If successful, rename to original output
            if os.path.exists(alternative_output):
                if os.path.exists(output_file):
                    os.remove(output_file)
                os.rename(alternative_output, output_file)
                logger.info("✅ Alternative export method succeeded")

        except Exception as e2:
            logger.error(f"❌ Alternative export also failed: {e2}")
            raise e  # Re-raise original exception

    finally:
        # Cleanup with error handling and memory monitoring
        try:
            if psutil:
                # Log memory usage before cleanup
                process = psutil.Process(os.getpid())
                memory_before = process.memory_info().rss / 1024 / 1024  # MB
                logger.info(f"📊 Memory usage before cleanup: {memory_before:.1f} MB")

            if hasattr(video_clip, 'close'):
                video_clip.close()
            del video_clip

            # Force garbage collection
            gc.collect()

            if psutil:
                # Log memory usage after cleanup
                memory_after = process.memory_info().rss / 1024 / 1024  # MB
                memory_freed = memory_before - memory_after
                logger.info(f"📊 Memory usage after cleanup: {memory_after:.1f} MB")
                logger.info(f"🧹 Memory freed: {memory_freed:.1f} MB")
            logger.info("✅ Video clip cleanup completed")

        except ImportError:
            # psutil not available, basic cleanup
            try:
                if hasattr(video_clip, 'close'):
                    video_clip.close()
                del video_clip
                gc.collect()
                logger.info("✅ Basic video clip cleanup completed")
            except Exception as e:
                logger.warning(f"⚠️ Basic cleanup warning: {e}")
        except Exception as e:
            logger.warning(f"⚠️ Cleanup warning: {e}")


def preprocess_video(materials: List[MaterialInfo], clip_duration=4):
    for material in materials:
        if not material.url:
            continue

        ext = utils.parse_extension(material.url)
        try:
            clip = VideoFileClip(material.url)
        except Exception:
            clip = ImageClip(material.url)

        width = clip.size[0]
        height = clip.size[1]
        if width < 480 or height < 480:
            logger.warning(f"low resolution material: {width}x{height}, minimum 480x480 required")
            continue

        if ext in const.FILE_TYPE_IMAGES:
            logger.info(f"processing image: {material.url}")
            # Create an image clip and set its duration to 3 seconds
            clip = (
                ImageClip(material.url)
                .set_duration(clip_duration)
                .set_position("center")
            )
            # Apply a zoom effect using the resize method.
            # A lambda function is used to make the zoom effect dynamic over time.
            # The zoom effect starts from the original size and gradually scales up to 120%.
            # t represents the current time, and clip.duration is the total duration of the clip (3 seconds).
            # Note: 1 represents 100% size, so 1.2 represents 120% size.
            zoom_clip = clip.resize(
                lambda t: 1 + (clip_duration * 0.03) * (t / clip.duration)
            )

            # Optionally, create a composite video clip containing the zoomed clip.
            # This is useful when you want to add other elements to the video.
            final_clip = CompositeVideoClip([zoom_clip])

            # Output the video to a file.
            video_file = f"{material.url}.mp4"
            final_clip.write_videofile(video_file, fps=30, logger=None)
            close_clip(clip)
            material.url = video_file
            logger.success(f"image processed: {video_file}")
    return materials


def _add_contextual_images(video_clip, contextual_images, subtitle_path):
    """Add contextual images as overlays to the video"""
    from app.services.timing_synchronizer import TimingSynchronizer

    logger.info(f"Adding {len(contextual_images)} contextual images to video")

    try:
        # Initialize timing synchronizer
        synchronizer = TimingSynchronizer()

        # Load subtitles for timing synchronization
        if subtitle_path and os.path.exists(subtitle_path):
            synchronizer.load_subtitles(subtitle_path)

        # Synchronize contextual images with timing
        synchronized_segments = synchronizer.synchronize_with_images(contextual_images)

        if not synchronized_segments:
            logger.warning("No synchronized segments created")
            return video_clip

        # Create image clips for overlay with improved conflict resolution
        overlay_clips = []
        processed_segments = []

        # Sort segments by start time and remove overlaps
        sorted_segments = sorted(synchronized_segments, key=lambda x: x.start_time)

        for segment in sorted_segments:
            if not segment.image_path or not os.path.exists(segment.image_path):
                continue

            # Check for conflicts with already processed segments
            has_conflict = False
            for processed in processed_segments:
                if (segment.start_time < processed.end_time and
                    segment.end_time > processed.start_time):
                    # Conflict detected - skip this segment
                    logger.debug(f"Skipping overlapping segment at {segment.start_time:.1f}s-{segment.end_time:.1f}s")
                    has_conflict = True
                    break

            if has_conflict:
                continue

            try:
                # Create image clip
                img_clip = ImageClip(segment.image_path)

                # Resize to fit video dimensions while maintaining aspect ratio
                video_w, video_h = video_clip.size
                img_w, img_h = img_clip.size

                # Calculate scaling to fit within video bounds
                scale_w = video_w / img_w
                scale_h = video_h / img_h
                scale = min(scale_w, scale_h) * 0.6  # Reduced to 60% for better visibility

                # Resize and position the image
                img_clip = img_clip.resize(scale)
                img_clip = img_clip.set_position(('center', 'center'))
                img_clip = img_clip.set_start(segment.start_time)
                img_clip = img_clip.set_duration(segment.duration)

                # Add fade in/out effects with shorter transitions
                fade_duration = min(0.3, segment.duration / 4)  # Adaptive fade duration
                img_clip = img_clip.fadein(fade_duration).fadeout(fade_duration)

                # Set opacity for overlay effect (slightly more transparent)
                img_clip = img_clip.set_opacity(0.6)

                overlay_clips.append(img_clip)
                processed_segments.append(segment)

                logger.debug(f"Added contextual image overlay: {segment.start_time:.1f}s-{segment.end_time:.1f}s")

            except Exception as e:
                logger.error(f"Failed to process contextual image {segment.image_path}: {e}")
                continue

        if overlay_clips:
            try:
                logger.info(f"🎬 Creating composite video with {len(overlay_clips)} image overlays...")

                # Limit number of overlays to prevent memory issues
                max_overlays = 5
                if len(overlay_clips) > max_overlays:
                    logger.warning(f"⚠️ Too many overlays ({len(overlay_clips)}), limiting to {max_overlays}")
                    overlay_clips = overlay_clips[:max_overlays]

                # Create composite with memory optimization
                start_time = time.time()

                logger.info("🎬 Creating CompositeVideoClip...")

                # Use timeout for CompositeVideoClip creation
                def create_composite():
                    return CompositeVideoClip([video_clip] + overlay_clips)

                try:
                    final_clip = run_with_timeout(create_composite, timeout_seconds=120)  # 2 minute timeout
                    logger.info("✅ CompositeVideoClip created successfully with timeout")
                except TimeoutError:
                    logger.error("⏰ CompositeVideoClip creation timed out after 2 minutes")
                    logger.warning("⚠️ Returning video without contextual images due to timeout")

                    # Cleanup overlay clips
                    for clip in overlay_clips:
                        try:
                            if hasattr(clip, 'close'):
                                clip.close()
                        except:
                            pass

                    return video_clip

                composite_time = time.time() - start_time
                logger.info(f"✅ Composite video created successfully in {composite_time:.1f}s")
                logger.info(f"✅ Successfully added {len(overlay_clips)} contextual image overlays")

                logger.info("🔄 Returning final composite clip...")
                return final_clip

            except Exception as e:
                logger.error(f"❌ Failed to create composite video: {e}")
                logger.warning("⚠️ Returning video without contextual images")

                # Cleanup overlay clips to free memory
                for clip in overlay_clips:
                    try:
                        if hasattr(clip, 'close'):
                            clip.close()
                    except:
                        pass

                logger.info("🔄 Returning video without contextual images (after error)")
                return video_clip
        else:
            logger.warning("ℹ️ No contextual image overlays created")
            logger.info("🔄 Returning original video clip (no overlays)")
            return video_clip

    except Exception as e:
        logger.error(f"Failed to add contextual images: {e}")
        logger.info("🔄 Returning original video clip (exception caught)")
        return video_clip


class TimeoutError(Exception):
    """Custom timeout exception"""
    pass


def run_with_timeout(func, timeout_seconds=600, *args, **kwargs):
    """
    Run a function with a timeout

    Args:
        func: Function to run
        timeout_seconds: Timeout in seconds (default 10 minutes)
        *args, **kwargs: Arguments for the function

    Returns:
        Function result or raises TimeoutError
    """
    result = [None]
    exception = [None]

    def target():
        try:
            result[0] = func(*args, **kwargs)
        except Exception as e:
            exception[0] = e

    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)

    if thread.is_alive():
        logger.error(f"⏰ Function {func.__name__} timed out after {timeout_seconds}s")
        raise TimeoutError(f"Function {func.__name__} timed out after {timeout_seconds} seconds")

    if exception[0]:
        raise exception[0]

    return result[0]


def safe_video_export(video_clip, output_file, **kwargs):
    """
    Safely export video with timeout and error handling

    Args:
        video_clip: MoviePy video clip
        output_file: Output file path
        **kwargs: Additional arguments for write_videofile
    """

    def export_video():
        return video_clip.write_videofile(output_file, **kwargs)

    try:
        logger.info(f"🎬 Starting safe video export to: {output_file}")

        # Run with 10 minute timeout
        run_with_timeout(export_video, timeout_seconds=600)

        logger.info("✅ Video export completed successfully")

    except TimeoutError:
        logger.error("⏰ Video export timed out after 10 minutes")
        logger.info("🔄 Attempting fast export with reduced quality...")

        # Try fast export as fallback
        try:
            fast_kwargs = {
                'codec': 'libx264',
                'audio_codec': 'aac',
                'preset': 'ultrafast',
                'threads': 1,
                'fps': 24,
                'verbose': False,
                'logger': None
            }

            def fast_export():
                return video_clip.write_videofile(output_file, **fast_kwargs)

            run_with_timeout(fast_export, timeout_seconds=300)  # 5 minute timeout for fast export
            logger.info("✅ Fast export completed successfully")

        except Exception as e:
            logger.error(f"❌ Fast export also failed: {e}")
            raise

    except Exception as e:
        logger.error(f"❌ Video export failed: {e}")
        raise