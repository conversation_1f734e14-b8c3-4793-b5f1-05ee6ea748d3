"""
Podcast Clipper Service
Serviciu pentru extragerea automată de clipuri din podcast-uri
"""

import os
import numpy as np
import tempfile
import json
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import logging
from dataclasses import dataclass

# Import opțional pentru OpenCV
try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False

# Import opțional pentru MoviePy
try:
    from moviepy.editor import VideoFileClip, CompositeVideoClip, TextClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False

# Import opțional pentru Whisper
try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False

# Import servicii pentru funcționalități extinse
try:
    from .video_compression_service import VideoCompressionService
    COMPRESSION_AVAILABLE = True
except ImportError:
    COMPRESSION_AVAILABLE = False

try:
    from .audio_to_video_service import AudioToVideoService, AudioVideoConfig
    AUDIO_TO_VIDEO_AVAILABLE = True
except ImportError:
    AUDIO_TO_VIDEO_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class PodcastClipConfig:
    """Configurație pentru procesarea podcast-ului"""
    confidence_threshold: float = 0.5
    max_people: int = 2
    clip_duration: int = 30
    min_speaker_time: int = 5
    enable_captions: bool = True
    caption_style: str = "Modern"
    highlight_words: bool = True
    output_resolution: str = "1080x1920"
    frame_rate: int = 30
    video_quality: str = "High"
    audio_enhancement: bool = True
    noise_reduction: bool = True
    normalize_audio: bool = True

@dataclass
class PersonDetection:
    """Detectare persoană în video"""
    x1: int
    y1: int
    x2: int
    y2: int
    confidence: float
    person_id: int

@dataclass
class SpeakerSegment:
    """Segment de vorbitor"""
    start_time: float
    end_time: float
    speaker_id: int
    confidence: float
    text: Optional[str] = None

@dataclass
class PodcastClip:
    """Clip generat din podcast"""
    start_time: float
    end_time: float
    duration: float
    speaker_id: int
    video_path: str
    transcript: Optional[str] = None
    confidence_score: float = 0.0

class PodcastClipperService:
    """Serviciu principal pentru procesarea podcast-urilor"""
    
    def __init__(self):
        self.temp_dir = None
        self.yolo_net = None
        self.whisper_model = None
        self.compression_service = None
        self.audio_to_video_service = None
        self._initialize_models()
        self._initialize_extended_services()
    
    def _initialize_models(self):
        """Inițializează modelele necesare"""
        try:
            # Inițializare YOLO pentru detectarea persoanelor
            self._load_yolo_model()
            
            # Inițializare Whisper pentru transcripție
            self._load_whisper_model()
            
        except Exception as e:
            logger.warning(f"Nu s-au putut inițializa toate modelele: {e}")
    
    def _load_yolo_model(self):
        """Încarcă modelul YOLO pentru detectarea persoanelor"""
        try:
            if not OPENCV_AVAILABLE:
                logger.warning("⚠️ OpenCV nu este disponibil. Detectarea persoanelor va fi simulată.")
                return

            # Căutăm fișierele YOLO în directorul models
            models_dir = Path("models")
            yolo_weights = models_dir / "yolov3.weights"
            yolo_config = models_dir / "yolov3.cfg"

            if yolo_weights.exists() and yolo_config.exists():
                self.yolo_net = cv2.dnn.readNet(str(yolo_weights), str(yolo_config))
                logger.info("✅ Model YOLO încărcat cu succes")
            else:
                logger.warning("⚠️ Fișierele YOLO nu au fost găsite. Detectarea persoanelor va fi simulată.")

        except Exception as e:
            logger.warning(f"⚠️ Nu s-a putut încărca YOLO: {e}. Se va folosi detectarea simulată.")
    
    def _load_whisper_model(self):
        """Încarcă modelul Whisper pentru transcripție"""
        try:
            if not WHISPER_AVAILABLE:
                logger.warning("⚠️ Whisper nu este disponibil. Transcripția va fi simulată.")
                return

            self.whisper_model = whisper.load_model("base")
            logger.info("✅ Model Whisper încărcat cu succes")
        except Exception as e:
            logger.warning(f"⚠️ Nu s-a putut încărca Whisper: {e}. Se va folosi transcripția simulată.")

    def _initialize_extended_services(self):
        """Inițializează serviciile extinse pentru compresie și audio"""
        try:
            if COMPRESSION_AVAILABLE:
                self.compression_service = VideoCompressionService()
                logger.info("✅ Serviciu compresie video inițializat")
            else:
                logger.warning("⚠️ Serviciul de compresie video nu este disponibil")

            if AUDIO_TO_VIDEO_AVAILABLE:
                self.audio_to_video_service = AudioToVideoService()
                logger.info("✅ Serviciu audio-to-video inițializat")
            else:
                logger.warning("⚠️ Serviciul audio-to-video nu este disponibil")

        except Exception as e:
            logger.error(f"❌ Eroare la inițializarea serviciilor extinse: {e}")
    
    def process_podcast(
        self, 
        video_path: str, 
        config: PodcastClipConfig,
        progress_callback=None
    ) -> List[PodcastClip]:
        """
        Procesează un podcast și generează clipuri
        
        Args:
            video_path: Calea către videoclipul podcast-ului
            config: Configurația de procesare
            progress_callback: Funcție pentru raportarea progresului
            
        Returns:
            Lista de clipuri generate
        """
        
        try:
            if progress_callback:
                progress_callback("📁 Încărcare video...", 0.1)

            # Încărcare video
            if MOVIEPY_AVAILABLE:
                video_clip = VideoFileClip(video_path)
            else:
                # Simulare pentru demonstrație
                logger.warning("⚠️ MoviePy nu este disponibil. Se simulează procesarea.")
                video_clip = None
            
            if progress_callback:
                progress_callback("🎯 Detectare persoane...", 0.2)
            
            # Detectare persoane
            person_detections = self._detect_people_in_video(video_clip, config)
            
            if progress_callback:
                progress_callback("🗣️ Analiză vorbitori...", 0.4)
            
            # Diarizare vorbitori
            speaker_segments = self._perform_speaker_diarization(video_clip, config)
            
            if progress_callback:
                progress_callback("📝 Transcripție audio...", 0.6)
            
            # Transcripție
            transcript = self._transcribe_audio(video_clip)
            
            if progress_callback:
                progress_callback("✂️ Generare clipuri...", 0.8)
            
            # Generare clipuri
            clips = self._generate_clips(
                video_clip, 
                person_detections, 
                speaker_segments, 
                transcript, 
                config
            )
            
            if progress_callback:
                progress_callback("✅ Procesare completă!", 1.0)
            
            return clips
            
        except Exception as e:
            logger.error(f"❌ Eroare în procesarea podcast-ului: {e}")
            raise
        finally:
            # Cleanup
            if video_clip:
                video_clip.close()
    
    def _detect_people_in_video(
        self, 
        video_clip: VideoFileClip, 
        config: PodcastClipConfig
    ) -> List[PersonDetection]:
        """Detectează persoanele în video"""
        
        detections = []

        try:
            if not OPENCV_AVAILABLE or not self.yolo_net:
                logger.info("⚠️ YOLO nu este disponibil. Se simulează detectarea persoanelor.")
                # Simulare detectare pentru demonstrație
                for i in range(min(config.max_people, 2)):
                    detections.append(PersonDetection(
                        x1=100 + i * 200,
                        y1=100,
                        x2=300 + i * 200,
                        y2=400,
                        confidence=0.8,
                        person_id=i
                    ))
                return detections
            
            # Analizăm primul frame pentru detectarea persoanelor
            first_frame = video_clip.get_frame(0)
            height, width = first_frame.shape[:2]
            
            # Pregătire input pentru YOLO
            blob = cv2.dnn.blobFromImage(
                first_frame, 
                0.00392, 
                (416, 416), 
                (0, 0, 0), 
                True, 
                crop=False
            )
            
            self.yolo_net.setInput(blob)
            layer_names = self.yolo_net.getLayerNames()
            output_layers = [layer_names[i - 1] for i in self.yolo_net.getUnconnectedOutLayers()]
            outs = self.yolo_net.forward(output_layers)
            
            # Procesare detectări
            boxes = []
            confidences = []
            
            for out in outs:
                for detection in out:
                    scores = detection[5:]
                    class_id = np.argmax(scores)
                    confidence = scores[class_id]
                    
                    # Verificăm dacă este persoană (class_id = 0) și încrederea
                    if confidence > config.confidence_threshold and class_id == 0:
                        center_x = int(detection[0] * width)
                        center_y = int(detection[1] * height)
                        w = int(detection[2] * width)
                        h = int(detection[3] * height)
                        
                        x1 = max(0, int(center_x - w / 2))
                        y1 = max(0, int(center_y - h / 2))
                        x2 = min(width, int(center_x + w / 2))
                        y2 = min(height, int(center_y + h / 2))
                        
                        boxes.append([x1, y1, x2, y2])
                        confidences.append(float(confidence))
            
            # Non-maximum suppression
            if boxes:
                indexes = cv2.dnn.NMSBoxes(boxes, confidences, 0.5, 0.5)
                
                for i, idx in enumerate(indexes.flatten() if len(indexes) > 0 else []):
                    if i >= config.max_people:
                        break
                        
                    x1, y1, x2, y2 = boxes[idx]
                    detections.append(PersonDetection(
                        x1=x1, y1=y1, x2=x2, y2=y2,
                        confidence=confidences[idx],
                        person_id=i
                    ))
            
            logger.info(f"✅ Detectate {len(detections)} persoane")
            
        except Exception as e:
            logger.error(f"❌ Eroare în detectarea persoanelor: {e}")
        
        return detections
    
    def _perform_speaker_diarization(
        self, 
        video_clip: VideoFileClip, 
        config: PodcastClipConfig
    ) -> List[SpeakerSegment]:
        """Efectuează diarizarea vorbitorilor"""
        
        segments = []
        
        try:
            # Pentru moment, implementăm o versiune simplificată
            # În implementarea completă, am folosi pyannote.audio
            
            duration = video_clip.duration
            segment_duration = config.clip_duration
            
            # Generăm segmente simple pentru demonstrație
            current_time = 0
            speaker_id = 0
            
            while current_time < duration:
                end_time = min(current_time + segment_duration, duration)
                
                if end_time - current_time >= config.min_speaker_time:
                    segments.append(SpeakerSegment(
                        start_time=current_time,
                        end_time=end_time,
                        speaker_id=speaker_id % 2,  # Alternăm între 2 vorbitori
                        confidence=0.8
                    ))
                
                current_time = end_time
                speaker_id += 1
            
            logger.info(f"✅ Identificate {len(segments)} segmente de vorbitori")
            
        except Exception as e:
            logger.error(f"❌ Eroare în diarizarea vorbitorilor: {e}")
        
        return segments
    
    def _transcribe_audio(self, video_clip: VideoFileClip) -> Optional[Dict]:
        """Transcrie audio-ul folosind Whisper"""
        
        try:
            if not WHISPER_AVAILABLE or not self.whisper_model:
                logger.info("⚠️ Whisper nu este disponibil. Se simulează transcripția.")
                # Simulare transcripție pentru demonstrație
                return {
                    'text': 'Acesta este un transcript simulat pentru demonstrația funcționalității Podcast Clipper.',
                    'segments': [
                        {
                            'start': 0.0,
                            'end': 30.0,
                            'text': 'Primul segment de transcript simulat.'
                        },
                        {
                            'start': 30.0,
                            'end': 60.0,
                            'text': 'Al doilea segment de transcript simulat.'
                        }
                    ]
                }

            if not video_clip:
                logger.warning("Video clip nu este disponibil pentru transcripție")
                return None

            # Extragem audio-ul
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                video_clip.audio.write_audiofile(temp_audio.name, verbose=False, logger=None)

                # Transcripție
                result = self.whisper_model.transcribe(temp_audio.name)

                # Cleanup
                os.unlink(temp_audio.name)

                logger.info("✅ Transcripție completă")
                return result
                
        except Exception as e:
            logger.error(f"❌ Eroare în transcripție: {e}")
            return None
    
    def _generate_clips(
        self,
        video_clip: VideoFileClip,
        person_detections: List[PersonDetection],
        speaker_segments: List[SpeakerSegment],
        transcript: Optional[Dict],
        config: PodcastClipConfig
    ) -> List[PodcastClip]:
        """Generează clipurile finale"""
        
        clips = []
        
        try:
            for i, segment in enumerate(speaker_segments):
                # Pentru moment, salvăm doar informațiile despre clip
                # Implementarea completă va genera fișierele video
                
                clip_transcript = None
                if transcript and 'segments' in transcript:
                    # Găsim transcriptul pentru acest segment
                    for t_seg in transcript['segments']:
                        if (t_seg['start'] >= segment.start_time and 
                            t_seg['end'] <= segment.end_time):
                            clip_transcript = t_seg.get('text', '')
                            break
                
                clip = PodcastClip(
                    start_time=segment.start_time,
                    end_time=segment.end_time,
                    duration=segment.end_time - segment.start_time,
                    speaker_id=segment.speaker_id,
                    video_path=f"clip_{i:03d}.mp4",  # Placeholder
                    transcript=clip_transcript,
                    confidence_score=segment.confidence
                )
                
                clips.append(clip)
            
            logger.info(f"✅ Generate {len(clips)} clipuri")
            
        except Exception as e:
            logger.error(f"❌ Eroare în generarea clipurilor: {e}")
        
        return clips

    def process_large_video(
        self,
        video_path: str,
        compression_level: str,
        config: PodcastClipConfig,
        progress_callback=None
    ) -> List[PodcastClip]:
        """
        Procesează un videoclip mare cu compresie

        Args:
            video_path: Calea către videoclipul original
            compression_level: Nivelul de compresie
            config: Configurația de procesare
            progress_callback: Funcție pentru raportarea progresului

        Returns:
            Lista de clipuri generate
        """

        try:
            if not self.compression_service:
                raise Exception("Serviciul de compresie nu este disponibil")

            if progress_callback:
                progress_callback("🗜️ Comprim videoclipul pentru procesare...", 0.1)

            # Comprimă videoclipul
            compressed_path = self.compression_service.compress_video(
                input_path=video_path,
                compression_level=compression_level,
                progress_callback=lambda msg, prog: progress_callback(msg, 0.1 + prog * 0.4)
            )

            if not compressed_path:
                raise Exception("Compresia videoclipului a eșuat")

            if progress_callback:
                progress_callback("🎬 Procesez videoclipul comprimat...", 0.5)

            # Procesează videoclipul comprimat
            clips = self.process_podcast(
                video_path=compressed_path,
                config=config,
                progress_callback=lambda msg, prog: progress_callback(msg, 0.5 + prog * 0.5)
            )

            # Cleanup fișierul comprimat temporar
            try:
                if os.path.exists(compressed_path) and compressed_path != video_path:
                    os.unlink(compressed_path)
            except:
                pass

            return clips

        except Exception as e:
            logger.error(f"❌ Eroare în procesarea videoclipului mare: {e}")
            raise

    def process_audio_podcast(
        self,
        audio_path: str,
        background_image_path: Optional[str],
        config: PodcastClipConfig,
        title: str = "Podcast",
        progress_callback=None
    ) -> List[PodcastClip]:
        """
        Procesează un podcast audio și generează clipuri cu imagini statice

        Args:
            audio_path: Calea către fișierul audio
            background_image_path: Calea către imaginea de fundal (opțional)
            config: Configurația de procesare
            title: Titlul podcast-ului
            progress_callback: Funcție pentru raportarea progresului

        Returns:
            Lista de clipuri generate
        """

        try:
            if not self.audio_to_video_service:
                raise Exception("Serviciul audio-to-video nu este disponibil")

            if progress_callback:
                progress_callback("🎵 Analizez fișierul audio...", 0.1)

            # Încarcă audio-ul pentru analiză
            try:
                from moviepy.editor import AudioFileClip
                audio_clip = AudioFileClip(audio_path)
                duration = audio_clip.duration
            except ImportError:
                # Fallback fără MoviePy
                duration = 120.0  # Estimare pentru demonstrație
                audio_clip = None

            if progress_callback:
                progress_callback("🗣️ Efectuez diarizarea vorbitorilor...", 0.2)

            # Diarizare simplificată pentru audio
            speaker_segments = self._perform_audio_speaker_diarization(duration, config)

            if progress_callback:
                progress_callback("📝 Generez transcripția...", 0.4)

            # Transcripție audio
            transcript = self._transcribe_audio_file(audio_path)

            if progress_callback:
                progress_callback("🎬 Generez videoclipuri cu imagini statice...", 0.6)

            # Generează clipuri pentru demonstrație
            clips = []

            for i, segment in enumerate(speaker_segments):
                if progress_callback:
                    segment_progress = 0.6 + (i / len(speaker_segments)) * 0.4
                    progress_callback(f"🎬 Generez clipul {i+1}/{len(speaker_segments)}...", segment_progress)

                # Găsește transcriptul pentru acest segment
                segment_transcript = None
                if transcript and 'segments' in transcript:
                    for t_seg in transcript['segments']:
                        if (t_seg['start'] >= segment.start_time and
                            t_seg['end'] <= segment.end_time):
                            segment_transcript = t_seg.get('text', '')
                            break

                clip = PodcastClip(
                    start_time=segment.start_time,
                    end_time=segment.end_time,
                    duration=segment.end_time - segment.start_time,
                    speaker_id=segment.speaker_id,
                    video_path=f"audio_clip_{i:03d}.mp4",  # Placeholder
                    transcript=segment_transcript,
                    confidence_score=segment.confidence
                )

                clips.append(clip)

            if audio_clip:
                audio_clip.close()

            logger.info(f"✅ Generate {len(clips)} clipuri din audio")
            return clips

        except Exception as e:
            logger.error(f"❌ Eroare în procesarea podcast-ului audio: {e}")
            raise

    def _perform_audio_speaker_diarization(
        self,
        duration: float,
        config: PodcastClipConfig
    ) -> List[SpeakerSegment]:
        """Efectuează diarizarea vorbitorilor pentru audio"""

        segments = []

        try:
            segment_duration = config.clip_duration

            # Generăm segmente simple pentru demonstrație
            current_time = 0
            speaker_id = 0

            while current_time < duration:
                end_time = min(current_time + segment_duration, duration)

                if end_time - current_time >= config.min_speaker_time:
                    segments.append(SpeakerSegment(
                        start_time=current_time,
                        end_time=end_time,
                        speaker_id=speaker_id % 2,  # Alternăm între 2 vorbitori
                        confidence=0.8
                    ))

                current_time = end_time
                speaker_id += 1

            logger.info(f"✅ Identificate {len(segments)} segmente audio")

        except Exception as e:
            logger.error(f"❌ Eroare în diarizarea audio: {e}")

        return segments

    def _transcribe_audio_file(self, audio_path: str) -> Optional[Dict]:
        """Transcrie un fișier audio folosind Whisper"""

        try:
            if not WHISPER_AVAILABLE or not self.whisper_model:
                logger.info("⚠️ Whisper nu este disponibil. Se simulează transcripția audio.")
                # Simulare transcripție pentru demonstrație
                return {
                    'text': 'Acesta este un transcript audio simulat pentru demonstrația funcționalității Podcast Clipper.',
                    'segments': [
                        {
                            'start': 0.0,
                            'end': 30.0,
                            'text': 'Primul segment audio simulat.'
                        },
                        {
                            'start': 30.0,
                            'end': 60.0,
                            'text': 'Al doilea segment audio simulat.'
                        }
                    ]
                }

            # Transcripție directă din fișierul audio
            result = self.whisper_model.transcribe(audio_path)

            logger.info("✅ Transcripție audio completă")
            return result

        except Exception as e:
            logger.error(f"❌ Eroare în transcripția audio: {e}")
            return None
