"""
Podcast Clipper Service
Serviciu pentru extragerea automată de clipuri din podcast-uri
"""

import os
import cv2
import numpy as np
import tempfile
import json
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import logging
from dataclasses import dataclass
from moviepy.editor import VideoFileClip, CompositeVideoClip, TextClip
import whisper

logger = logging.getLogger(__name__)

@dataclass
class PodcastClipConfig:
    """Configurație pentru procesarea podcast-ului"""
    confidence_threshold: float = 0.5
    max_people: int = 2
    clip_duration: int = 30
    min_speaker_time: int = 5
    enable_captions: bool = True
    caption_style: str = "Modern"
    highlight_words: bool = True
    output_resolution: str = "1080x1920"
    frame_rate: int = 30
    video_quality: str = "High"
    audio_enhancement: bool = True
    noise_reduction: bool = True
    normalize_audio: bool = True

@dataclass
class PersonDetection:
    """Detectare persoană în video"""
    x1: int
    y1: int
    x2: int
    y2: int
    confidence: float
    person_id: int

@dataclass
class SpeakerSegment:
    """Segment de vorbitor"""
    start_time: float
    end_time: float
    speaker_id: int
    confidence: float
    text: Optional[str] = None

@dataclass
class PodcastClip:
    """Clip generat din podcast"""
    start_time: float
    end_time: float
    duration: float
    speaker_id: int
    video_path: str
    transcript: Optional[str] = None
    confidence_score: float = 0.0

class PodcastClipperService:
    """Serviciu principal pentru procesarea podcast-urilor"""
    
    def __init__(self):
        self.temp_dir = None
        self.yolo_net = None
        self.whisper_model = None
        self._initialize_models()
    
    def _initialize_models(self):
        """Inițializează modelele necesare"""
        try:
            # Inițializare YOLO pentru detectarea persoanelor
            self._load_yolo_model()
            
            # Inițializare Whisper pentru transcripție
            self._load_whisper_model()
            
        except Exception as e:
            logger.warning(f"Nu s-au putut inițializa toate modelele: {e}")
    
    def _load_yolo_model(self):
        """Încarcă modelul YOLO pentru detectarea persoanelor"""
        try:
            # Căutăm fișierele YOLO în directorul models
            models_dir = Path("models")
            yolo_weights = models_dir / "yolov3.weights"
            yolo_config = models_dir / "yolov3.cfg"
            
            if yolo_weights.exists() and yolo_config.exists():
                self.yolo_net = cv2.dnn.readNet(str(yolo_weights), str(yolo_config))
                logger.info("✅ Model YOLO încărcat cu succes")
            else:
                logger.warning("⚠️ Fișierele YOLO nu au fost găsite. Detectarea persoanelor va fi limitată.")
                
        except Exception as e:
            logger.error(f"❌ Eroare la încărcarea modelului YOLO: {e}")
    
    def _load_whisper_model(self):
        """Încarcă modelul Whisper pentru transcripție"""
        try:
            self.whisper_model = whisper.load_model("base")
            logger.info("✅ Model Whisper încărcat cu succes")
        except Exception as e:
            logger.warning(f"⚠️ Nu s-a putut încărca Whisper: {e}")
    
    def process_podcast(
        self, 
        video_path: str, 
        config: PodcastClipConfig,
        progress_callback=None
    ) -> List[PodcastClip]:
        """
        Procesează un podcast și generează clipuri
        
        Args:
            video_path: Calea către videoclipul podcast-ului
            config: Configurația de procesare
            progress_callback: Funcție pentru raportarea progresului
            
        Returns:
            Lista de clipuri generate
        """
        
        try:
            if progress_callback:
                progress_callback("📁 Încărcare video...", 0.1)
            
            # Încărcare video
            video_clip = VideoFileClip(video_path)
            
            if progress_callback:
                progress_callback("🎯 Detectare persoane...", 0.2)
            
            # Detectare persoane
            person_detections = self._detect_people_in_video(video_clip, config)
            
            if progress_callback:
                progress_callback("🗣️ Analiză vorbitori...", 0.4)
            
            # Diarizare vorbitori
            speaker_segments = self._perform_speaker_diarization(video_clip, config)
            
            if progress_callback:
                progress_callback("📝 Transcripție audio...", 0.6)
            
            # Transcripție
            transcript = self._transcribe_audio(video_clip)
            
            if progress_callback:
                progress_callback("✂️ Generare clipuri...", 0.8)
            
            # Generare clipuri
            clips = self._generate_clips(
                video_clip, 
                person_detections, 
                speaker_segments, 
                transcript, 
                config
            )
            
            if progress_callback:
                progress_callback("✅ Procesare completă!", 1.0)
            
            return clips
            
        except Exception as e:
            logger.error(f"❌ Eroare în procesarea podcast-ului: {e}")
            raise
        finally:
            # Cleanup
            if video_clip:
                video_clip.close()
    
    def _detect_people_in_video(
        self, 
        video_clip: VideoFileClip, 
        config: PodcastClipConfig
    ) -> List[PersonDetection]:
        """Detectează persoanele în video"""
        
        detections = []
        
        try:
            if not self.yolo_net:
                logger.warning("Model YOLO nu este disponibil")
                return detections
            
            # Analizăm primul frame pentru detectarea persoanelor
            first_frame = video_clip.get_frame(0)
            height, width = first_frame.shape[:2]
            
            # Pregătire input pentru YOLO
            blob = cv2.dnn.blobFromImage(
                first_frame, 
                0.00392, 
                (416, 416), 
                (0, 0, 0), 
                True, 
                crop=False
            )
            
            self.yolo_net.setInput(blob)
            layer_names = self.yolo_net.getLayerNames()
            output_layers = [layer_names[i - 1] for i in self.yolo_net.getUnconnectedOutLayers()]
            outs = self.yolo_net.forward(output_layers)
            
            # Procesare detectări
            boxes = []
            confidences = []
            
            for out in outs:
                for detection in out:
                    scores = detection[5:]
                    class_id = np.argmax(scores)
                    confidence = scores[class_id]
                    
                    # Verificăm dacă este persoană (class_id = 0) și încrederea
                    if confidence > config.confidence_threshold and class_id == 0:
                        center_x = int(detection[0] * width)
                        center_y = int(detection[1] * height)
                        w = int(detection[2] * width)
                        h = int(detection[3] * height)
                        
                        x1 = max(0, int(center_x - w / 2))
                        y1 = max(0, int(center_y - h / 2))
                        x2 = min(width, int(center_x + w / 2))
                        y2 = min(height, int(center_y + h / 2))
                        
                        boxes.append([x1, y1, x2, y2])
                        confidences.append(float(confidence))
            
            # Non-maximum suppression
            if boxes:
                indexes = cv2.dnn.NMSBoxes(boxes, confidences, 0.5, 0.5)
                
                for i, idx in enumerate(indexes.flatten() if len(indexes) > 0 else []):
                    if i >= config.max_people:
                        break
                        
                    x1, y1, x2, y2 = boxes[idx]
                    detections.append(PersonDetection(
                        x1=x1, y1=y1, x2=x2, y2=y2,
                        confidence=confidences[idx],
                        person_id=i
                    ))
            
            logger.info(f"✅ Detectate {len(detections)} persoane")
            
        except Exception as e:
            logger.error(f"❌ Eroare în detectarea persoanelor: {e}")
        
        return detections
    
    def _perform_speaker_diarization(
        self, 
        video_clip: VideoFileClip, 
        config: PodcastClipConfig
    ) -> List[SpeakerSegment]:
        """Efectuează diarizarea vorbitorilor"""
        
        segments = []
        
        try:
            # Pentru moment, implementăm o versiune simplificată
            # În implementarea completă, am folosi pyannote.audio
            
            duration = video_clip.duration
            segment_duration = config.clip_duration
            
            # Generăm segmente simple pentru demonstrație
            current_time = 0
            speaker_id = 0
            
            while current_time < duration:
                end_time = min(current_time + segment_duration, duration)
                
                if end_time - current_time >= config.min_speaker_time:
                    segments.append(SpeakerSegment(
                        start_time=current_time,
                        end_time=end_time,
                        speaker_id=speaker_id % 2,  # Alternăm între 2 vorbitori
                        confidence=0.8
                    ))
                
                current_time = end_time
                speaker_id += 1
            
            logger.info(f"✅ Identificate {len(segments)} segmente de vorbitori")
            
        except Exception as e:
            logger.error(f"❌ Eroare în diarizarea vorbitorilor: {e}")
        
        return segments
    
    def _transcribe_audio(self, video_clip: VideoFileClip) -> Optional[Dict]:
        """Transcrie audio-ul folosind Whisper"""
        
        try:
            if not self.whisper_model:
                logger.warning("Model Whisper nu este disponibil")
                return None
            
            # Extragem audio-ul
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                video_clip.audio.write_audiofile(temp_audio.name, verbose=False, logger=None)
                
                # Transcripție
                result = self.whisper_model.transcribe(temp_audio.name)
                
                # Cleanup
                os.unlink(temp_audio.name)
                
                logger.info("✅ Transcripție completă")
                return result
                
        except Exception as e:
            logger.error(f"❌ Eroare în transcripție: {e}")
            return None
    
    def _generate_clips(
        self,
        video_clip: VideoFileClip,
        person_detections: List[PersonDetection],
        speaker_segments: List[SpeakerSegment],
        transcript: Optional[Dict],
        config: PodcastClipConfig
    ) -> List[PodcastClip]:
        """Generează clipurile finale"""
        
        clips = []
        
        try:
            for i, segment in enumerate(speaker_segments):
                # Pentru moment, salvăm doar informațiile despre clip
                # Implementarea completă va genera fișierele video
                
                clip_transcript = None
                if transcript and 'segments' in transcript:
                    # Găsim transcriptul pentru acest segment
                    for t_seg in transcript['segments']:
                        if (t_seg['start'] >= segment.start_time and 
                            t_seg['end'] <= segment.end_time):
                            clip_transcript = t_seg.get('text', '')
                            break
                
                clip = PodcastClip(
                    start_time=segment.start_time,
                    end_time=segment.end_time,
                    duration=segment.end_time - segment.start_time,
                    speaker_id=segment.speaker_id,
                    video_path=f"clip_{i:03d}.mp4",  # Placeholder
                    transcript=clip_transcript,
                    confidence_score=segment.confidence
                )
                
                clips.append(clip)
            
            logger.info(f"✅ Generate {len(clips)} clipuri")
            
        except Exception as e:
            logger.error(f"❌ Eroare în generarea clipurilor: {e}")
        
        return clips
