
# GPT4Free Configuration for MoneyPrinterTurbo

[gpt4free]
# Enable GPT4Free integration
enabled = true

# Default model to use
default_model = "gpt-4o-mini"

# Fallback models (in order of preference)
fallback_models = [
    "gpt-4o",
    "gpt-4",
    "gemini-pro",
    "claude-3-sonnet"
]

# Provider preferences
preferred_providers = [
    "auto",
    "Bing",
    "ChatGPT",
    "Gemini"
]

# Enable web search for better content
web_search_enabled = true

# Maximum retries for failed requests
max_retries = 3

# Request timeout (seconds)
timeout = 30

# Enable streaming responses
streaming = false

# Romanian language optimization
romanian_optimization = true
