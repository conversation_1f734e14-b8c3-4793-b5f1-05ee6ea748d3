#!/usr/bin/env python3
"""
Test Import Fix

Tests that the import errors have been fixed.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))


async def test_import_fix():
    """Test that all imports work correctly"""
    print("🔧 Testing Import Fixes...")
    print("=" * 50)
    
    try:
        # Test 1: Import the correct classes
        print("1️⃣ Testing correct class imports...")
        
        from app.services.viral_content_generator import ViralVideoPackage
        from app.services.one_click_viral_generator import ViralVideoResult
        
        print("✅ ViralVideoPackage imported successfully")
        print("✅ ViralVideoResult imported successfully")
        
        # Test 2: Test creating a ViralVideoPackage
        print("\n2️⃣ Testing ViralVideoPackage creation...")
        
        from webui.components.viral_automation_interface import (
            create_fallback_topic,
            create_fallback_script
        )
        from app.services.one_click_viral_generator import ViralVideoConfig
        
        config = ViralVideoConfig(
            category="motivation",
            target_audience="tineri români 18-35",
            platform="tiktok",
            duration=60
        )
        
        topic = create_fallback_topic(config)
        script = create_fallback_script(topic, config)
        
        # Create a test package
        package = ViralVideoPackage(
            topic=topic,
            script=script,
            title="Test Title",
            description="Test Description",
            hashtags=["#test", "#viral", "#romania"],
            search_terms=["test", "viral", "romania"],
            contextual_image_prompts=["test prompt 1", "test prompt 2"],
            thumbnail_prompt="test thumbnail prompt",
            seo_keywords=["test", "viral", "romania"],
            target_platforms=["tiktok"]
        )
        
        print("✅ ViralVideoPackage created successfully")
        print(f"   Topic: {package.topic.title}")
        print(f"   Script length: {len(package.script.script_text)} characters")
        print(f"   Hashtags: {len(package.hashtags)}")
        print(f"   Search terms: {len(package.search_terms)}")
        
        # Test 3: Test creating a ViralVideoResult
        print("\n3️⃣ Testing ViralVideoResult creation...")
        
        result = ViralVideoResult(
            success=True,
            video_package=package,
            contextual_image_plan=None,
            generation_time=25.5,
            video_files=[],
            performance_predictions={
                "estimated_views": "10K-50K",
                "estimated_engagement_rate": "5-8%",
                "viral_probability": "high"
            }
        )
        
        print("✅ ViralVideoResult created successfully")
        print(f"   Success: {result.success}")
        print(f"   Generation time: {result.generation_time}s")
        print(f"   Performance predictions: {len(result.performance_predictions)} metrics")
        
        # Test 4: Test the enhanced generation function import
        print("\n4️⃣ Testing enhanced generation function...")
        
        from webui.components.viral_automation_interface import generate_viral_video_with_feedback
        
        print("✅ generate_viral_video_with_feedback imported successfully")
        
        print("\n" + "=" * 50)
        print("📊 IMPORT FIX SUMMARY:")
        print("=" * 50)
        print("✅ Fixed: ViralGenerationResult → ViralVideoResult")
        print("✅ Fixed: Correct import from one_click_viral_generator")
        print("✅ Fixed: ViralVideoPackage import from viral_content_generator")
        print("✅ Fixed: All required parameters for ViralVideoPackage")
        print("✅ Fixed: All required parameters for ViralVideoResult")
        
        print("\n🎉 All import fixes have been successfully applied!")
        print("🚀 The enhanced viral interface should now work without import errors!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    try:
        success = asyncio.run(test_import_fix())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
