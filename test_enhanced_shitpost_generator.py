#!/usr/bin/env python3
"""
Enhanced Shitpost Generator Test Suite
Comprehensive testing for AI-powered shitpost video generation
"""

import os
import sys
import asyncio
import unittest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Add project root to path
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))

class TestShitpostGenerator(unittest.TestCase):
    """Test the enhanced shitpost generator"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
        
        # Mock storage directories
        self.storage_dir = Path(self.temp_dir) / "storage"
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        (self.storage_dir / "videos").mkdir(exist_ok=True)
        (self.storage_dir / "ai_images").mkdir(exist_ok=True)
    
    def test_basic_import(self):
        """Test that all modules can be imported"""
        try:
            from app.services.shitpost_generator import ShitpostGenerator
            from app.services.gpt4free_service import gpt4free_service
            from app.services.free_ai_services import free_ai_generator
            self.assertTrue(True, "All imports successful")
        except ImportError as e:
            self.fail(f"Import failed: {e}")
    
    def test_shitpost_generator_initialization(self):
        """Test shitpost generator can be initialized"""
        try:
            from app.services.shitpost_generator import ShitpostGenerator
            generator = ShitpostGenerator()
            self.assertIsNotNone(generator)
            self.assertIsNotNone(generator.text_templates)
            self.assertIsNotNone(generator.word_banks)
        except Exception as e:
            self.fail(f"Generator initialization failed: {e}")
    
    def test_text_generation(self):
        """Test text generation functionality"""
        try:
            from app.services.shitpost_generator import ShitpostGenerator
            generator = ShitpostGenerator()
            
            # Test different themes
            themes = ['romanian', 'gaming', 'philosophical', 'random_chaos']
            
            for theme in themes:
                text = generator._generate_absurd_text(theme)
                self.assertIsInstance(text, str)
                self.assertGreater(len(text), 0)
                print(f"✅ {theme}: {text[:50]}...")
                
        except Exception as e:
            self.fail(f"Text generation failed: {e}")

class TestGPT4FreeService(unittest.TestCase):
    """Test GPT4Free service integration"""
    
    def setUp(self):
        """Set up GPT4Free tests"""
        try:
            from app.services.gpt4free_service import gpt4free_service
            self.service = gpt4free_service
        except ImportError:
            self.skipTest("GPT4Free not available")
    
    def test_service_availability(self):
        """Test if GPT4Free service is available"""
        is_available = self.service.is_available()
        print(f"GPT4Free available: {is_available}")
        
        if is_available:
            status = self.service.get_status()
            self.assertIsInstance(status, dict)
            self.assertIn('available', status)
    
    @unittest.skipIf(not hasattr(sys.modules.get('app.services.gpt4free_service', Mock()), 'gpt4free_service'), 
                     "GPT4Free not available")
    def test_text_generation_async(self):
        """Test async text generation"""
        async def run_test():
            if not self.service.is_available():
                self.skipTest("GPT4Free service not available")
            
            # Test basic text generation
            result = await self.service.generate_text("Test prompt for shitpost")
            
            if result:  # Service might be rate limited
                self.assertIsInstance(result, str)
                self.assertGreater(len(result), 0)
                print(f"✅ GPT4Free generated: {result[:50]}...")
            else:
                print("⚠️ GPT4Free returned None (possibly rate limited)")
        
        try:
            asyncio.run(run_test())
        except Exception as e:
            print(f"⚠️ GPT4Free test failed: {e}")

class TestFreeAIServices(unittest.TestCase):
    """Test free AI image generation services"""
    
    def setUp(self):
        """Set up AI services tests"""
        try:
            from app.services.free_ai_services import free_ai_generator
            self.generator = free_ai_generator
        except ImportError:
            self.skipTest("Free AI services not available")
    
    def test_service_status(self):
        """Test AI services status"""
        status = self.generator.get_service_status()
        self.assertIsInstance(status, dict)
        
        print("AI Services Status:")
        for service, info in status.items():
            print(f"  {service}: {info}")
    
    def test_pollinations_api(self):
        """Test Pollinations API"""
        async def run_test():
            try:
                from app.services.free_ai_services import PollinationsAPI
                api = PollinationsAPI()
                
                # Test image generation
                image_data = await api.generate_image(
                    "test meme image", 
                    width=256, 
                    height=256, 
                    style="meme"
                )
                
                if image_data:
                    self.assertIsInstance(image_data, bytes)
                    self.assertGreater(len(image_data), 0)
                    print(f"✅ Pollinations generated {len(image_data)} bytes")
                else:
                    print("⚠️ Pollinations returned None (possibly rate limited)")
                    
            except Exception as e:
                print(f"⚠️ Pollinations test failed: {e}")
        
        asyncio.run(run_test())

class TestCLIInterface(unittest.TestCase):
    """Test command-line interface"""
    
    def test_cli_import(self):
        """Test CLI can be imported"""
        try:
            import shitpost_cli
            self.assertTrue(hasattr(shitpost_cli, 'ShitpostCLI'))
            print("✅ CLI import successful")
        except ImportError as e:
            self.fail(f"CLI import failed: {e}")
    
    def test_cli_parser(self):
        """Test CLI argument parser"""
        try:
            from shitpost_cli import ShitpostCLI
            cli = ShitpostCLI()
            parser = cli.create_parser()
            
            # Test basic argument parsing
            args = parser.parse_args(['generate', '--theme', 'romanian', '--chaos', '5'])
            self.assertEqual(args.command, 'generate')
            self.assertEqual(args.theme, 'romanian')
            self.assertEqual(args.chaos, 5)
            
            print("✅ CLI parser working correctly")
            
        except Exception as e:
            self.fail(f"CLI parser test failed: {e}")

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        """Set up integration tests"""
        self.temp_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_dir)
    
    def test_basic_video_generation(self):
        """Test basic video generation without AI"""
        try:
            from app.services.shitpost_generator import ShitpostGenerator
            
            generator = ShitpostGenerator()
            
            # Mock the output path to use temp directory
            with patch.object(generator, '_generate_output_path') as mock_path:
                mock_path.return_value = os.path.join(self.temp_dir, "test_video.mp4")
                
                # Test basic generation (might fail due to missing dependencies)
                try:
                    result = generator.generate_random_shitpost(
                        theme="romanian",
                        duration=5,  # Short duration for testing
                        chaos_level=3
                    )
                    
                    if result['success']:
                        print("✅ Basic video generation successful")
                        self.assertTrue(result['success'])
                        self.assertIn('output_path', result)
                        self.assertIn('text_generated', result)
                    else:
                        print(f"⚠️ Video generation failed: {result.get('error', 'Unknown error')}")
                        
                except Exception as e:
                    print(f"⚠️ Video generation test failed (expected if dependencies missing): {e}")
                    
        except ImportError as e:
            self.skipTest(f"Required modules not available: {e}")

class TestPerformance(unittest.TestCase):
    """Performance and stress tests"""
    
    def test_text_generation_performance(self):
        """Test text generation performance"""
        try:
            from app.services.shitpost_generator import ShitpostGenerator
            import time
            
            generator = ShitpostGenerator()
            
            # Time multiple text generations
            start_time = time.time()
            
            for i in range(10):
                text = generator._generate_absurd_text("romanian")
                self.assertIsInstance(text, str)
                self.assertGreater(len(text), 0)
            
            end_time = time.time()
            avg_time = (end_time - start_time) / 10
            
            print(f"✅ Average text generation time: {avg_time:.3f}s")
            self.assertLess(avg_time, 1.0, "Text generation should be fast")
            
        except Exception as e:
            self.fail(f"Performance test failed: {e}")

def run_comprehensive_test():
    """Run all tests with detailed output"""
    print("🧪 Enhanced Shitpost Generator Test Suite")
    print("=" * 60)
    
    # Create test suite
    test_classes = [
        TestShitpostGenerator,
        TestGPT4FreeService, 
        TestFreeAIServices,
        TestCLIInterface,
        TestIntegration,
        TestPerformance
    ]
    
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    for test_class in test_classes:
        print(f"\n📋 Running {test_class.__name__}...")
        
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=1, stream=sys.stdout)
        result = runner.run(suite)
        
        total_tests += result.testsRun
        passed_tests += result.testsRun - len(result.failures) - len(result.errors)
        failed_tests += len(result.failures) + len(result.errors)
        
        if result.failures:
            print(f"❌ Failures in {test_class.__name__}:")
            for test, traceback in result.failures:
                print(f"   • {test}: {traceback.split('AssertionError:')[-1].strip()}")
        
        if result.errors:
            print(f"❌ Errors in {test_class.__name__}:")
            for test, traceback in result.errors:
                print(f"   • {test}: {traceback.split('Exception:')[-1].strip()}")
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 Test Summary")
    print("=" * 60)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "No tests run")
    
    if failed_tests == 0:
        print("\n🎉 All tests passed! The enhanced shitpost generator is ready to use.")
    else:
        print(f"\n⚠️ {failed_tests} tests failed. Check the output above for details.")
        print("💡 Some failures might be expected if optional dependencies are missing.")
    
    print("\n🚀 Next steps:")
    print("1. Run setup script: python setup_shitpost_generator.py")
    print("2. Start web interface: python webui.bat")
    print("3. Try CLI: python shitpost_cli.py generate --theme romanian")

if __name__ == "__main__":
    run_comprehensive_test()
