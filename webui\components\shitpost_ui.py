"""
Enhanced Shitpost Generator UI Component
Interfața pentru generarea de videoclipuri shitpost cu AI și cultură românească
"""

import streamlit as st
import os
import random
import asyncio
from typing import Dict, List
from app.services.shitpost_generator import ShitpostGenerator
import threading
import concurrent.futures

def _run_async_safely(coro):
    """Run async coroutine safely in Streamlit environment"""
    def run_in_thread():
        """Run coroutine in a separate thread with its own event loop"""
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        except Exception as e:
            # Log the error but don't crash the UI
            st.error(f"Async operation failed: {str(e)}")
            return None
        finally:
            try:
                # Clean up the loop
                loop.close()
            except:
                pass
            # Clear the event loop for this thread
            asyncio.set_event_loop(None)

    # Always run in a separate thread to avoid Streamlit conflicts
    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
        try:
            future = executor.submit(run_in_thread)
            return future.result(timeout=180)  # 3 minute timeout
        except concurrent.futures.TimeoutError:
            st.error("⏰ Operation timed out after 3 minutes")
            return None
        except Exception as e:
            st.error(f"❌ Thread execution failed: {str(e)}")
            return None

def _check_ai_services_status() -> Dict[str, bool]:
    """Check availability of AI services"""
    status = {
        'gpt4free': False,
        'free_ai_images': False,
        'local_sd': False
    }

    try:
        from app.services.gpt4free_service import gpt4free_service
        status['gpt4free'] = gpt4free_service.is_available()
    except ImportError:
        pass

    try:
        from app.services.free_ai_services import free_ai_generator
        # Check if at least one free service is available
        ai_status = free_ai_generator.get_service_status()
        status['free_ai_images'] = any(
            service.get('available', False)
            for service in ai_status.values()
            if isinstance(service, dict)
        )
    except ImportError:
        pass

    try:
        from app.services.ai_image_generator import StableDiffusionAPI
        sd_api = StableDiffusionAPI()
        status['local_sd'] = sd_api.available
    except ImportError:
        pass

    return status

def render_shitpost_tab():
    """Renderează tab-ul pentru Shitpost Generator"""
    st.header("😂 Generator de Videoclipuri Shitpost")
    st.markdown("**Creează videoclipuri absurde și virale pentru social media**")
    
    # Inițializează generatorul
    if 'shitpost_generator' not in st.session_state:
        st.session_state.shitpost_generator = ShitpostGenerator()
    
    generator = st.session_state.shitpost_generator
    
    # Tabs pentru diferite tipuri de shitpost
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🎲 Random Chaos",
        "🇷🇴 Romanian Memes",
        "🧠 Big Brain",
        "🎮 Gaming Shitpost",
        "🤖 AI-Powered"
    ])
    
    with tab1:
        _render_random_chaos_tab(generator)
    
    with tab2:
        _render_romanian_memes_tab(generator)
    
    with tab3:
        _render_big_brain_tab(generator)
    
    with tab4:
        _render_gaming_tab(generator)

    with tab5:
        _render_ai_powered_tab(generator)

def _render_random_chaos_tab(generator: ShitpostGenerator):
    """Renderează tab-ul pentru Random Chaos"""
    st.subheader("🎲 Shitpost Complet Random")
    st.markdown("Generează chaos pur cu efecte vizuale extreme și text absurd")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**⚙️ Setări Chaos**")
        
        chaos_level = st.slider(
            "🔥 Nivel de Chaos", 
            1, 10, 7,
            help="1 = Ușor amuzant, 10 = Chaos total"
        )
        
        duration = st.slider(
            "⏱️ Durată (secunde)", 
            5, 60, 15,
            help="Durata videoclipului generat"
        )
        
        auto_text = st.checkbox(
            "🤖 Text generat automat", 
            value=True,
            help="Generează text absurd automat"
        )
    
    with col2:
        st.markdown("**🎨 Efecte Incluse**")
        
        effects_preview = [
            "🔍 Zoom haotic",
            "🌈 Distorsiune culori extreme", 
            "🌪️ Rotație nebună",
            "📳 Shake earthquake",
            "🎨 Color invert flash",
            "📏 Size chaos random"
        ]
        
        for i, effect in enumerate(effects_preview):
            if i < chaos_level // 2:
                st.write(f"• {effect}")
        
        remaining = len(effects_preview) - (chaos_level // 2)
        if remaining > 0:
            st.write(f"• ... și încă {remaining} efecte la nivel maxim")
    
    # Text personalizat
    if not auto_text:
        custom_text = st.text_area(
            "✏️ Text personalizat",
            placeholder="Scrie textul tău absurd aici...",
            help="Textul care va apărea în videoclip"
        )
    else:
        custom_text = None
        
        # Preview text generat
        if st.button("👀 Preview Text Random"):
            preview_text = generator._generate_absurd_text("random_chaos")
            st.info(f"**Text generat**: {preview_text}")
    
    # Buton pentru generare
    if st.button("🎬 Generează Chaos Total", type="primary", use_container_width=True):
        _generate_shitpost(generator, "random_chaos", {
            'chaos_level': chaos_level,
            'duration': duration,
            'custom_text': custom_text
        })

def _render_romanian_memes_tab(generator: ShitpostGenerator):
    """Renderează tab-ul pentru Romanian Memes"""
    st.subheader("🇷🇴 Romanian Shitpost")
    st.markdown("Meme-uri românești cu umor local și referințe culturale")
    
    col1, col2 = st.columns(2)
    
    with col1:
        meme_type = st.selectbox(
            "🎭 Tip de meme românesc",
            [
                "Bunica Wisdom",
                "Școala Românească", 
                "Vecini Drama",
                "Manele Philosophy",
                "Țuică Moments",
                "Politică Absurdă",
                "Trafic București"
            ]
        )
        
        regional_flavor = st.selectbox(
            "🗺️ Aromă regională",
            [
                "București (Capitală)",
                "Moldova (Accent dulce)",
                "Transilvania (Influență maghiară)", 
                "Oltenia (Umor tăios)",
                "Banat (Multiculturalism)"
            ]
        )
    
    with col2:
        st.markdown("**🎯 Exemple de Template-uri**")
        
        examples = {
            "Bunica Wisdom": "Bunica: 'Mănâncă că ești slab'\nEu: 'Dar bunico, am 90kg'\nBunica: 'Ești piele și os'",
            "Școala Românească": "Profesorul: 'Cine știe capitala Franței?'\nEu: 'Depinde, înainte sau după război?'",
            "Manele Philosophy": "Când realizezi că versurile de manele sunt de fapt filozofie profundă",
            "Trafic București": "GPS: 'Ajungi în 20 de minute'\nTraficul din București: 'Allow me to introduce myself'"
        }
        
        if meme_type in examples:
            st.info(f"**Exemplu**: {examples[meme_type]}")
    
    # Setări specifice
    include_manele_refs = st.checkbox("🎵 Include referințe la manele", value=True)
    include_food_refs = st.checkbox("🍲 Include referințe culinare", value=True)
    
    if st.button("🎬 Generează Meme Românesc", type="primary", use_container_width=True):
        _generate_shitpost(generator, "romanian", {
            'meme_type': meme_type,
            'regional_flavor': regional_flavor,
            'include_manele': include_manele_refs,
            'include_food': include_food_refs,
            'chaos_level': 5,  # Moderat pentru meme-uri românești
            'duration': 20
        })

def _render_big_brain_tab(generator: ShitpostGenerator):
    """Renderează tab-ul pentru Big Brain Philosophy"""
    st.subheader("🧠 Big Brain Shitpost")
    st.markdown("Filozofie absurdă și realizări existențiale de 3 dimineața")
    
    col1, col2 = st.columns(2)
    
    with col1:
        philosophy_level = st.selectbox(
            "🤔 Nivel filozofic",
            [
                "Shower Thoughts (Gânduri sub duș)",
                "3AM Realizations (Realizări de noapte)", 
                "Existential Crisis (Criză existențială)",
                "Quantum Philosophy (Filozofie cuantică)",
                "Interdimensional Wisdom (Înțelepciune interdimensională)"
            ]
        )
        
        complexity = st.slider(
            "🌌 Complexitate conceptuală",
            1, 10, 6,
            help="Cât de profunde să fie conceptele"
        )
    
    with col2:
        st.markdown("**💭 Exemple de Concepte**")
        
        concepts = {
            "Shower Thoughts": "Dacă timpul este bani, atunci ceasurile sunt bănci",
            "3AM Realizations": "Când realizezi că covrigul este un donut care și-a găsit gaura",
            "Existential Crisis": "Dacă universul se extinde, în ce se extinde?",
            "Quantum Philosophy": "În realitatea cuantică, pisica lui Schrödinger face TikTok-uri"
        }
        
        concept_key = philosophy_level.split(" (")[0]
        if concept_key in concepts:
            st.info(f"**Exemplu**: {concepts[concept_key]}")
    
    # Efecte speciale pentru big brain
    cosmic_effects = st.checkbox("🌌 Efecte cosmice", value=True)
    mind_blown_moments = st.checkbox("🤯 Momente mind-blown", value=True)
    
    if st.button("🎬 Generează Big Brain Content", type="primary", use_container_width=True):
        _generate_shitpost(generator, "philosophical", {
            'philosophy_level': philosophy_level,
            'complexity': complexity,
            'cosmic_effects': cosmic_effects,
            'mind_blown': mind_blown_moments,
            'chaos_level': complexity,
            'duration': 25
        })

def _render_gaming_tab(generator: ShitpostGenerator):
    """Renderează tab-ul pentru Gaming Shitpost"""
    st.subheader("🎮 Gaming Shitpost")
    st.markdown("Meme-uri pentru gameri cu referințe la jocuri și cultura gaming")
    
    col1, col2 = st.columns(2)
    
    with col1:
        game_genre = st.selectbox(
            "🎮 Gen de joc",
            [
                "Dark Souls Suffering",
                "Minecraft Creativity", 
                "CS:GO Rage",
                "League Toxicity",
                "Speedrun Fails",
                "Battle Royale Chaos",
                "RPG Grinding"
            ]
        )
        
        gaming_culture = st.selectbox(
            "🕹️ Cultură gaming",
            [
                "Casual Gamer",
                "Hardcore Tryhard",
                "Speedrunner",
                "Streamer Life",
                "Retro Gaming"
            ]
        )
    
    with col2:
        st.markdown("**🎯 Gaming References**")
        
        gaming_examples = {
            "Dark Souls Suffering": "Când mori pentru a 47-a oară la același boss",
            "CS:GO Rage": "Teammate: 'Rush B'\nMe: *gets one-tapped*",
            "League Toxicity": "Jungle diff în minute 2",
            "Speedrun Fails": "World record run ruined by a pixel"
        }
        
        if game_genre in gaming_examples:
            st.info(f"**Exemplu**: {gaming_examples[game_genre]}")
    
    # Setări gaming specifice
    include_memes = st.checkbox("😂 Include meme-uri gaming clasice", value=True)
    include_sounds = st.checkbox("🔊 Include referințe la sunete de joc", value=False)
    
    if st.button("🎬 Generează Gaming Meme", type="primary", use_container_width=True):
        _generate_shitpost(generator, "gaming", {
            'game_genre': game_genre,
            'gaming_culture': gaming_culture,
            'include_memes': include_memes,
            'include_sounds': include_sounds,
            'chaos_level': 6,
            'duration': 18
        })

def _generate_shitpost(generator: ShitpostGenerator, theme: str, config: Dict):
    """Generează un shitpost cu configurația dată"""
    with st.spinner(f"😂 Generez shitpost {theme}..."):
        try:
            result = generator.generate_random_shitpost(
                theme=theme,
                duration=config.get('duration', 15),
                chaos_level=config.get('chaos_level', 7),
                custom_text=config.get('custom_text')
            )
            
            if result['success']:
                st.success("🎉 Shitpost generat cu succes!")
                
                # Afișează informații despre shitpost
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("😂 Tema", result['theme'].title())
                
                with col2:
                    st.metric("🔥 Chaos Level", result['chaos_level'])
                
                with col3:
                    st.metric("⏱️ Durată", f"{result['duration']}s")
                
                # Afișează textul generat
                if result['text_generated']:
                    st.subheader("📝 Text Generat")
                    st.info(result['text_generated'])
                
                # Link pentru download
                if os.path.exists(result['output_path']):
                    with open(result['output_path'], "rb") as file:
                        st.download_button(
                            label="📥 Descarcă Shitpost",
                            data=file.read(),
                            file_name=os.path.basename(result['output_path']),
                            mime="video/mp4",
                            use_container_width=True
                        )
                    
                    # Informații despre fișier
                    file_size = result['file_size'] / (1024 * 1024)
                    st.write(f"💾 Mărime fișier: {file_size:.1f} MB")
                
                # Sugestii pentru social media
                _show_social_media_suggestions(result, theme)
                
            else:
                st.error(f"❌ Eroare în generarea shitpost: {result.get('error', 'Eroare necunoscută')}")
                
        except Exception as e:
            st.error(f"❌ Eroare în generarea shitpost: {str(e)}")

def _show_social_media_suggestions(result: Dict, theme: str):
    """Afișează sugestii pentru social media"""
    st.subheader("📱 Sugestii Social Media")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**🎯 Platforme Recomandate**")
        if theme == "romanian":
            st.write("• TikTok România")
            st.write("• Instagram Reels")
            st.write("• Facebook (grupuri de meme-uri)")
        elif theme == "gaming":
            st.write("• YouTube Shorts")
            st.write("• Twitch clips")
            st.write("• Discord servers")
        else:
            st.write("• TikTok")
            st.write("• Instagram Reels")
            st.write("• YouTube Shorts")
    
    with col2:
        st.markdown("**⏰ Timing Optimal**")
        st.write("• 18:00-22:00 (prime time)")
        st.write("• Weekend dimineața")
        st.write("• Trending moments")
        
        # Hashtag-uri sugerate
        hashtags = _generate_hashtags(theme)
        st.markdown("**# Hashtag-uri**")
        st.write(hashtags)

def _generate_hashtags(theme: str) -> str:
    """Generează hashtag-uri pentru temă"""
    base_hashtags = ["#shitpost", "#meme", "#viral", "#funny"]
    
    theme_hashtags = {
        "romanian": ["#romania", "#romanesc", "#umor", "#bunica"],
        "gaming": ["#gaming", "#gamer", "#esports", "#twitch"],
        "philosophical": ["#deepthoughts", "#philosophy", "#mindblown"],
        "random_chaos": ["#chaos", "#random", "#absurd", "#wtf"]
    }
    
    hashtags = base_hashtags + theme_hashtags.get(theme, [])
    return " ".join(hashtags)

def _render_ai_powered_tab(generator: ShitpostGenerator):
    """Renderează tab-ul pentru AI-Powered Shitpost"""
    st.subheader("🤖 AI-Powered Shitpost Generator")
    st.markdown("**Generează shitpost-uri cu AI image generation și cultură românească avansată**")

    # Check AI services availability
    ai_services_status = _check_ai_services_status()

    # Display service status
    col1, col2, col3 = st.columns(3)

    with col1:
        if ai_services_status['gpt4free']:
            st.success("✅ GPT4Free Active")
        else:
            st.error("❌ GPT4Free Unavailable")

    with col2:
        if ai_services_status['free_ai_images']:
            st.success("✅ Free AI Images Active")
        else:
            st.warning("⚠️ Free AI Images Limited")

    with col3:
        if ai_services_status['local_sd']:
            st.success("✅ Local SD Available")
        else:
            st.info("ℹ️ Local SD Not Configured")

    # Show setup instructions if services are missing
    if not any(ai_services_status.values()):
        st.error("❌ No AI services available")
        st.markdown("""
        **Setup Instructions:**
        1. Install GPT4Free: `pip install -U g4f[all]`
        2. Install AI dependencies: `pip install aiohttp psutil`
        3. Run setup script: `python setup_shitpost_generator.py`
        """)
        st.info("💡 You can still use basic features in other tabs.")
        return

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### ⚙️ Configurare AI")

        # Theme selection with AI prompts
        theme = st.selectbox(
            "Temă AI:",
            ["romanian", "chaos", "gaming", "philosophical"],
            help="Fiecare temă are prompt-uri AI specifice"
        )

        # AI Service selection
        st.markdown("**🤖 Servicii AI**")
        ai_service_priority = st.multiselect(
            "Prioritate servicii AI:",
            ["GPT4Free", "Free AI Images", "Local Stable Diffusion"],
            default=["GPT4Free", "Free AI Images"],
            help="Ordinea în care să încerce serviciile AI"
        )

        # AI Mode Selection
        st.markdown("**🎬 Mod de Generare AI**")
        ai_mode = st.radio(
            "Selectează modul AI:",
            ["Standard AI", "AI-Only Mode (Complet AI)"],
            help="Standard: AI + template. AI-Only: Doar conținut generat de AI"
        )

        ai_only_mode = ai_mode == "AI-Only Mode (Complet AI)"

        if ai_only_mode:
            st.info("🤖 **AI-Only Mode**: Întregul video va fi generat folosind doar AI - imagini și text")
            st.markdown("**Caracteristici AI-Only:**")
            st.markdown("- 🎨 Secvență de imagini generate de AI")
            st.markdown("- 📝 Text generat cu GPT4Free")
            st.markdown("- 🎬 Tranziții fluide între imagini")
            st.markdown("- 🎯 Optimizat pentru social media")

        # AI Image settings
        use_ai_images = st.checkbox("🎨 Folosește AI pentru imagini", value=True, disabled=ai_only_mode)

        if ai_only_mode:
            st.caption("ℹ️ AI-Only Mode folosește automat imagini AI")

        if use_ai_images or ai_only_mode:
            image_style = st.selectbox(
                "Stil imagine AI:",
                ["absurd", "classic", "romanian", "gaming", "brain", "chaos"],
                help="Stilul influențează prompt-urile AI"
            )

            # Advanced image settings
            with st.expander("🔧 Setări Avansate Imagine"):
                image_width = st.slider("Lățime imagine:", 256, 1024, 512, step=64)
                image_height = st.slider("Înălțime imagine:", 256, 1024, 512, step=64)

                use_image_cache = st.checkbox("Cache imagini", value=True)

                custom_image_prompt = st.text_input(
                    "Prompt personalizat imagine:",
                    placeholder="Lasă gol pentru generare automată..."
                )

            template_options = ["auto"] + _get_available_templates(generator)
            template_name = st.selectbox(
                "Template meme:",
                template_options,
                help="Template pentru aplicarea textului pe imagine"
            )
            template_name = None if template_name == "auto" else template_name

        # Enhanced Romanian culture settings
        st.markdown("### 🇷🇴 Cultură Românească Avansată")
        use_romanian_ai = st.checkbox("Folosește AI românesc pentru text", value=True)

        if use_romanian_ai:
            cultural_intensity = st.slider(
                "Intensitate culturală:",
                1, 10, 7,
                help="Cât de mult să folosească referințe culturale românești"
            )

            romanian_style = st.selectbox(
                "Stil românesc:",
                ["General", "Bunica Wisdom", "Manele Philosophy", "Balkan Humor", "Urban Romanian"],
                help="Tipul specific de umor românesc"
            )

            include_regional = st.checkbox("Include referințe regionale", value=True)

    with col2:
        st.markdown("### 🎬 Parametri Video")

        duration = st.slider("Durată (secunde):", 3, 30, 8)
        chaos_level = st.slider("Chaos Level:", 1, 10, 6)

        # Custom text with AI enhancement
        custom_text = st.text_area(
            "Text personalizat (opțional):",
            placeholder="Lasă gol pentru generare automată cu AI...",
            help="Textul va fi îmbunătățit cu slang românesc"
        )

        # Advanced AI settings
        with st.expander("🔧 Setări Avansate AI"):
            ai_creativity = st.slider(
                "Creativitate AI:",
                1, 10, 7,
                help="Cât de creativ să fie AI-ul (afectează prompt-urile)"
            )

            use_meme_templates = st.checkbox("Folosește template-uri meme", value=True)

            batch_mode = st.checkbox("Mod batch (generează multiple)", value=False)

            if batch_mode:
                batch_count = st.number_input("Numărul de shitpost-uri:", 2, 10, 3)

    # Generation section
    st.markdown("### 🚀 Generare")

    col_gen1, col_gen2 = st.columns(2)

    with col_gen1:
        if st.button("🤖 Generează AI Shitpost", type="primary", use_container_width=True):
            _generate_ai_shitpost(
                generator, theme, duration, chaos_level, custom_text,
                use_ai_images or ai_only_mode, template_name, use_romanian_ai, ai_only_mode
            )

    with col_gen2:
        if batch_mode and st.button("📦 Generează Batch", use_container_width=True):
            _generate_batch_shitposts(
                generator, theme, duration, chaos_level,
                batch_count, use_ai_images, use_romanian_ai
            )

    # Performance monitoring
    if st.checkbox("📊 Monitorizare performanță"):
        _show_performance_metrics(generator)

def _get_available_templates(generator: ShitpostGenerator) -> List[str]:
    """Obține template-urile disponibile"""
    try:
        if hasattr(generator, 'meme_template_engine') and generator.meme_template_engine:
            return generator.meme_template_engine.list_templates(min_popularity=5)
        return ["classic_meme", "text_only"]
    except Exception:
        return ["classic_meme", "text_only"]

def _generate_ai_shitpost(generator: ShitpostGenerator, theme: str, duration: int,
                         chaos_level: int, custom_text: str, use_ai_images: bool,
                         template_name: str, use_romanian_ai: bool, ai_only_mode: bool = False):
    """Generează un singur AI shitpost"""

    with st.spinner("🤖 Generez AI shitpost... Poate dura până la 60 secunde..."):
        try:
            # Check if async method is available and AI services are working
            if hasattr(generator, 'generate_ai_powered_shitpost') and (use_ai_images or ai_only_mode):
                if ai_only_mode:
                    st.info("🎬 Using AI-Only mode - generating complete video with AI...")
                else:
                    st.info("🤖 Using AI-powered generation...")

                # Use thread-safe async execution for Streamlit
                result = _run_async_safely(
                    generator.generate_ai_powered_shitpost(
                        theme=theme,
                        duration=duration,
                        chaos_level=chaos_level,
                        custom_text=custom_text or None,
                        use_ai_images=use_ai_images,
                        template_name=template_name,
                        ai_only_mode=ai_only_mode
                    )
                )

                # If AI generation failed, fall back to regular generation
                if result is None or not result.get('success', False):
                    st.warning("⚠️ AI generation failed, falling back to basic generation...")
                    result = generator.generate_random_shitpost(
                        theme=theme,
                        duration=duration,
                        chaos_level=chaos_level,
                        custom_text=custom_text or None
                    )
            else:
                # Use regular method
                st.info("🎬 Using basic generation...")
                result = generator.generate_random_shitpost(
                    theme=theme,
                    duration=duration,
                    chaos_level=chaos_level,
                    custom_text=custom_text or None
                )

            if result['success']:
                st.success("✅ AI Shitpost generat cu succes!")

                # Display results
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("### 📊 Informații")
                    st.write(f"**Fișier:** {os.path.basename(result['output_path'])}")
                    st.write(f"**Temă:** {result['theme']}")
                    st.write(f"**Chaos Level:** {result['chaos_level']}")
                    st.write(f"**Durată:** {result['duration']}s")

                    if result.get('ai_powered'):
                        st.write(f"**AI Image:** {'✅' if result.get('used_ai_image') else '❌'}")
                        if result.get('template_used'):
                            st.write(f"**Template:** {result['template_used']}")

                with col2:
                    st.markdown("### 💬 Text Generat")
                    st.write(f"*{result['text_generated']}*")

                # Download button
                if os.path.exists(result['output_path']):
                    with open(result['output_path'], 'rb') as f:
                        st.download_button(
                            "📥 Descarcă AI Shitpost",
                            f.read(),
                            file_name=os.path.basename(result['output_path']),
                            mime="video/mp4",
                            use_container_width=True
                        )

                # Social media suggestions
                st.markdown("### 📱 Sugestii Social Media")
                hashtags = _generate_hashtags(result['theme'])
                st.code(f"Caption: {result['text_generated']}\n\n{hashtags}")

            else:
                st.error(f"❌ Eroare: {result.get('error', 'Eroare necunoscută')}")

        except Exception as e:
            st.error(f"❌ Eroare în generarea AI: {str(e)}")

def _generate_batch_shitposts(generator: ShitpostGenerator, theme: str, duration: int,
                             chaos_level: int, batch_count: int, use_ai_images: bool,
                             use_romanian_ai: bool):
    """Generează multiple AI shitpost-uri"""

    with st.spinner(f"🤖 Generez {batch_count} AI shitpost-uri... Poate dura câteva minute..."):
        try:
            # Create batch requests
            requests = []
            for i in range(batch_count):
                requests.append({
                    'theme': theme,
                    'duration': duration,
                    'chaos_level': chaos_level + random.randint(-1, 1),  # Slight variation
                    'use_ai_images': use_ai_images,
                    'custom_text': None
                })

            # Process batch (simplified for now)
            results = []
            progress_bar = st.progress(0)

            for i, request in enumerate(requests):
                if hasattr(generator, 'generate_ai_powered_shitpost'):
                    result = _run_async_safely(
                        generator.generate_ai_powered_shitpost(**request)
                    )
                else:
                    result = generator.generate_random_shitpost(**request)

                results.append(result)
                progress_bar.progress((i + 1) / batch_count)

            # Display batch results
            successful = [r for r in results if r['success']]

            st.success(f"✅ Batch complet! {len(successful)}/{len(results)} shitpost-uri generate cu succes")

            # Show summary
            for i, result in enumerate(successful):
                with st.expander(f"Shitpost {i+1}: {result['theme']} (Chaos {result['chaos_level']})"):
                    st.write(f"**Text:** {result['text_generated']}")
                    st.write(f"**Fișier:** {os.path.basename(result['output_path'])}")

                    if os.path.exists(result['output_path']):
                        with open(result['output_path'], 'rb') as f:
                            st.download_button(
                                f"📥 Descarcă Shitpost {i+1}",
                                f.read(),
                                file_name=os.path.basename(result['output_path']),
                                mime="video/mp4",
                                key=f"download_{i}"
                            )

        except Exception as e:
            st.error(f"❌ Eroare în batch generation: {str(e)}")

def _show_performance_metrics(generator: ShitpostGenerator):
    """Afișează metrici de performanță"""
    try:
        if hasattr(generator, 'performance_optimizer'):
            metrics = generator.performance_optimizer.get_performance_report()

            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Timp mediu generare", f"{metrics.get('avg_generation_time', 0):.1f}s")

            with col2:
                st.metric("Memorie folosită", f"{metrics.get('avg_memory_usage_mb', 0):.0f} MB")

            with col3:
                st.metric("Rata de succes", f"{metrics.get('avg_success_rate', 0):.1%}")

            # System load
            system_load = metrics.get('current_system_load', {})
            if system_load:
                st.markdown("### 💻 Încărcare sistem")
                col1, col2 = st.columns(2)

                with col1:
                    st.metric("CPU", f"{system_load.get('cpu_percent', 0):.1f}%")

                with col2:
                    st.metric("Memorie", f"{system_load.get('memory_percent', 0):.1f}%")
        else:
            st.info("Metrici de performanță nu sunt disponibile")

    except Exception as e:
        st.error(f"Eroare la afișarea metricilor: {e}")
